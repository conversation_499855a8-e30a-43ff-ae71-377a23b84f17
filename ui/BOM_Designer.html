<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>参数化BOM设计器 - 工艺管理子系统</title>
    
    <!-- TailwindCSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom Styles -->
    <style>
        [x-cloak] { display: none !important; }
        
        .bom-node {
            transition: all 0.2s ease;
        }
        
        .bom-node:hover {
            background-color: rgba(59, 130, 246, 0.05);
        }
        
        .formula-editor {
            font-family: 'Courier New', monospace;
            min-height: 100px;
        }
        
        .parameter-tag {
            transition: all 0.15s ease;
        }
        
        .parameter-tag:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        
        .component-icon {
            width: 24px;
            height: 24px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            margin-right: 8px;
        }
        
        .product-icon { background-color: #3B82F6; color: white; }
        .component-icon { background-color: #10B981; color: white; }
        .material-icon { background-color: #F59E0B; color: white; }
        .submaterial-icon { background-color: #8B5CF6; color: white; }
        
        .tree-line {
            border-left: 1px dashed #CBD5E1;
            margin-left: 12px;
            padding-left: 12px;
        }
        
        .highlight {
            background-color: rgba(251, 191, 36, 0.2);
            border-left: 3px solid #F59E0B;
        }
        
        .modal-backdrop {
            backdrop-filter: blur(4px);
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800" x-data="bomDesigner()">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="fas fa-cube text-blue-600 text-2xl mr-2"></i>
                        <span class="font-bold text-xl text-gray-900">工艺管理子系统</span>
                    </div>
                    <nav class="ml-6 flex space-x-4">
                        <a href="#" class="px-3 py-2 rounded-md text-sm font-medium text-blue-600 bg-blue-50">参数化BOM设计器</a>
                        <a href="#" class="px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50">BOM管理</a>
                        <a href="#" class="px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50">工艺路线</a>
                    </nav>
                </div>
                <div class="flex items-center">
                    <button class="p-1 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none">
                        <i class="fas fa-bell"></i>
                    </button>
                    <div class="ml-3 relative">
                        <div>
                            <button class="flex text-sm rounded-full focus:outline-none" id="user-menu">
                                <img class="h-8 w-8 rounded-full" src="https://picsum.photos/seed/user123/40/40.jpg" alt="用户头像">
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col md:flex-row gap-6">
            <!-- Left Panel - Product Selection and Parameters -->
            <div class="w-full md:w-1/3 bg-white rounded-lg shadow p-4">
                <h2 class="text-lg font-semibold mb-4">产品信息</h2>
                
                <!-- Product Type Selection -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">产品类型</label>
                    <div class="grid grid-cols-2 gap-2">
                        <template x-for="productType in productTypes" :key="productType.id">
                            <button 
                                @click="selectProductType(productType.id)"
                                :class="{'ring-2 ring-blue-500 bg-blue-50': selectedProductType === productType.id}"
                                class="flex flex-col items-center justify-center p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                                <i :class="productType.icon" class="text-xl mb-1"></i>
                                <span class="text-sm" x-text="productType.name"></span>
                            </button>
                        </template>
                    </div>
                </div>
                
                <!-- Product Parameters -->
                <div class="mb-6">
                    <div class="flex justify-between items-center mb-2">
                        <label class="block text-sm font-medium text-gray-700">产品参数</label>
                        <button @click="showAddParameterModal = true" class="text-xs text-blue-600 hover:text-blue-800">
                            <i class="fas fa-plus mr-1"></i>添加参数
                        </button>
                    </div>
                    
                    <div class="space-y-2 max-h-60 overflow-y-auto pr-2">
                        <template x-for="param in productParameters" :key="param.id">
                            <div class="flex items-center justify-between p-2 bg-gray-50 rounded-md">
                                <div>
                                    <div class="font-medium text-sm" x-text="param.name"></div>
                                    <div class="text-xs text-gray-500" x-text="param.description"></div>
                                </div>
                                <div class="flex items-center">
                                    <input 
                                        type="text" 
                                        x-model="param.value" 
                                        class="w-20 px-2 py-1 text-sm border border-gray-300 rounded-md"
                                        @change="updateCalculations()">
                                    <span class="ml-1 text-xs text-gray-500" x-text="param.unit"></span>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
                
                <!-- Component Parameters -->
                <div class="mb-6" x-show="selectedComponent">
                    <div class="flex justify-between items-center mb-2">
                        <label class="block text-sm font-medium text-gray-700">构件参数</label>
                        <span class="text-xs text-gray-500" x-text="selectedComponent ? selectedComponent.name : ''"></span>
                    </div>
                    
                    <div class="space-y-2 max-h-60 overflow-y-auto pr-2" x-show="selectedComponent && selectedComponent.parameters">
                        <template x-for="param in selectedComponent.parameters" :key="param.id">
                            <div class="flex items-center justify-between p-2 bg-gray-50 rounded-md">
                                <div>
                                    <div class="font-medium text-sm" x-text="param.name"></div>
                                    <div class="text-xs text-gray-500" x-text="param.description"></div>
                                </div>
                                <div class="flex items-center">
                                    <input 
                                        type="text" 
                                        x-model="param.value" 
                                        class="w-20 px-2 py-1 text-sm border border-gray-300 rounded-md"
                                        @change="updateCalculations()">
                                    <span class="ml-1 text-xs text-gray-500" x-text="param.unit"></span>
                                </div>
                            </div>
                        </template>
                    </div>
                    
                    <div class="text-center py-4 text-gray-500 text-sm" x-show="!selectedComponent || !selectedComponent.parameters">
                        请选择一个构件以查看其参数
                    </div>
                </div>
                
                <!-- Calculation Test -->
                <div class="border-t border-gray-200 pt-4">
                    <button 
                        @click="runCalculationTest()"
                        class="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors flex items-center justify-center">
                        <i class="fas fa-calculator mr-2"></i>计算测试
                    </button>
                    
                    <div x-show="calculationResults.length > 0" class="mt-4">
                        <h3 class="text-sm font-medium text-gray-700 mb-2">计算结果</h3>
                        <div class="bg-gray-50 rounded-md p-3 max-h-40 overflow-y-auto text-sm">
                            <template x-for="result in calculationResults" :key="result.id">
                                <div class="py-1 border-b border-gray-200 last:border-0">
                                    <span class="font-medium" x-text="result.name"></span>: 
                                    <span x-text="result.value"></span>
                                    <span x-text="result.unit" class="text-gray-500"></span>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right Panel - BOM Structure -->
            <div class="w-full md:w-2/3 bg-white rounded-lg shadow p-4">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold">BOM结构</h2>
                    <div class="flex space-x-2">
                        <button 
                            @click="showAddComponentModal = true"
                            class="px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-sm rounded-md transition-colors">
                            <i class="fas fa-plus mr-1"></i>添加构件
                        </button>
                        <button 
                            @click="exportBOM()"
                            class="px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white text-sm rounded-md transition-colors">
                            <i class="fas fa-download mr-1"></i>导出BOM
                        </button>
                    </div>
                </div>
                
                <!-- BOM Tree -->
                <div class="border border-gray-200 rounded-lg overflow-hidden">
                    <div class="bg-gray-50 px-4 py-2 border-b border-gray-200 text-sm font-medium text-gray-700">
                        物料清单结构
                    </div>
                    
                    <div class="p-4 max-h-[500px] overflow-y-auto">
                        <template x-for="node in bomStructure" :key="node.id">
                            <div class="bom-node">
                                <div 
                                    @click="selectNode(node)"
                                    :class="{'highlight': selectedNode && selectedNode.id === node.id}"
                                    class="flex items-center p-2 rounded-md cursor-pointer">
                                    <div class="component-icon product-icon">
                                        <i class="fas fa-cube text-xs"></i>
                                    </div>
                                    <div class="flex-1">
                                        <div class="font-medium" x-text="node.name"></div>
                                        <div class="text-xs text-gray-500" x-text="node.description"></div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button 
                                            @click.stop="toggleNodeExpand(node)"
                                            class="text-gray-500 hover:text-gray-700">
                                            <i :class="node.expanded ? 'fas fa-chevron-down' : 'fas fa-chevron-right'"></i>
                                        </button>
                                        <button 
                                            @click.stop="editNode(node)"
                                            class="text-gray-500 hover:text-blue-600">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <div x-show="node.expanded" x-transition class="tree-line">
                                    <template x-for="component in node.children" :key="component.id">
                                        <div class="bom-node">
                                            <div 
                                                @click="selectNode(component)"
                                                :class="{'highlight': selectedNode && selectedNode.id === component.id}"
                                                class="flex items-center p-2 rounded-md cursor-pointer">
                                                <div class="component-icon component-icon">
                                                    <i :class="component.icon" class="text-xs"></i>
                                                </div>
                                                <div class="flex-1">
                                                    <div class="font-medium" x-text="component.name"></div>
                                                    <div class="text-xs text-gray-500" x-text="component.description"></div>
                                                </div>
                                                <div class="flex items-center space-x-2">
                                                    <button 
                                                        @click.stop="toggleNodeExpand(component)"
                                                        class="text-gray-500 hover:text-gray-700">
                                                        <i :class="component.expanded ? 'fas fa-chevron-down' : 'fas fa-chevron-right'"></i>
                                                    </button>
                                                    <button 
                                                        @click.stop="addComponentMaterial(component)"
                                                        class="text-gray-500 hover:text-green-600">
                                                        <i class="fas fa-plus"></i>
                                                    </button>
                                                    <button 
                                                        @click.stop="editNode(component)"
                                                        class="text-gray-500 hover:text-blue-600">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button 
                                                        @click.stop="deleteNode(component)"
                                                        class="text-gray-500 hover:text-red-600">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            
                                            <div x-show="component.expanded" x-transition class="tree-line">
                                                <template x-for="material in component.children" :key="material.id">
                                                    <div class="bom-node">
                                                        <div 
                                                            @click="selectNode(material)"
                                                            :class="{'highlight': selectedNode && selectedNode.id === material.id}"
                                                            class="flex items-center p-2 rounded-md cursor-pointer">
                                                            <div class="component-icon material-icon">
                                                                <i class="fas fa-shapes text-xs"></i>
                                                            </div>
                                                            <div class="flex-1">
                                                                <div class="font-medium" x-text="material.name"></div>
                                                                <div class="text-xs text-gray-500" x-text="material.description"></div>
                                                            </div>
                                                            <div class="flex items-center space-x-2">
                                                                <button 
                                                                    @click.stop="toggleNodeExpand(material)"
                                                                    class="text-gray-500 hover:text-gray-700">
                                                                    <i :class="material.expanded ? 'fas fa-chevron-down' : 'fas fa-chevron-right'"></i>
                                                                </button>
                                                                <button 
                                                                    @click.stop="addMaterialSubitem(material)"
                                                                    class="text-gray-500 hover:text-green-600">
                                                                    <i class="fas fa-plus"></i>
                                                                </button>
                                                                <button 
                                                                    @click.stop="editNode(material)"
                                                                    class="text-gray-500 hover:text-blue-600">
                                                                    <i class="fas fa-edit"></i>
                                                                </button>
                                                                <button 
                                                                    @click.stop="deleteNode(material)"
                                                                    class="text-gray-500 hover:text-red-600">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                        
                                                        <div x-show="material.expanded" x-transition class="tree-line">
                                                            <template x-for="subitem in material.children" :key="subitem.id">
                                                                <div class="bom-node">
                                                                    <div 
                                                                        @click="selectNode(subitem)"
                                                                        :class="{'highlight': selectedNode && selectedNode.id === subitem.id}"
                                                                        class="flex items-center p-2 rounded-md cursor-pointer">
                                                                        <div class="component-icon submaterial-icon">
                                                                            <i class="fas fa-circle text-xs"></i>
                                                                        </div>
                                                                        <div class="flex-1">
                                                                            <div class="font-medium" x-text="subitem.name"></div>
                                                                            <div class="text-xs text-gray-500" x-text="subitem.description"></div>
                                                                        </div>
                                                                        <div class="flex items-center space-x-2">
                                                                            <button 
                                                                                @click.stop="editNode(subitem)"
                                                                                class="text-gray-500 hover:text-blue-600">
                                                                                <i class="fas fa-edit"></i>
                                                                            </button>
                                                                            <button 
                                                                                @click.stop="deleteNode(subitem)"
                                                                                class="text-gray-500 hover:text-red-600">
                                                                                <i class="fas fa-trash"></i>
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </template>
                                                        </div>
                                                    </div>
                                                </template>
                                            </div>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
                
                <!-- Formula Editor -->
                <div x-show="selectedNode && (selectedNode.type === 'material' || selectedNode.type === 'submaterial')" class="mt-6 border border-gray-200 rounded-lg p-4">
                    <div class="flex justify-between items-center mb-2">
                        <h3 class="text-md font-medium">用量公式</h3>
                        <div class="text-sm text-gray-500">
                            <span x-text="selectedNode ? selectedNode.name : ''"></span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <textarea 
                            x-model="selectedNodeFormula"
                            class="formula-editor w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="输入用量公式，例如: (width * height) / 10000"></textarea>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <div>
                            <span class="text-sm text-gray-600 mr-2">可用参数:</span>
                            <template x-for="param in availableParameters" :key="param.id">
                                <button 
                                    @click="insertParameter(param)"
                                    class="parameter-tag inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-md mr-1 mb-1">
                                    <span x-text="param.name"></span>
                                </button>
                            </template>
                        </div>
                        
                        <button 
                            @click="saveFormula()"
                            class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md transition-colors">
                            保存公式
                        </button>
                    </div>
                    
                    <div x-show="formulaError" class="mt-2 text-sm text-red-600">
                        <i class="fas fa-exclamation-circle mr-1"></i>
                        <span x-text="formulaError"></span>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Status Bar -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2 text-sm text-gray-600">
        <div class="max-w-7xl mx-auto flex justify-between">
            <div>
                <span class="font-medium">当前产品:</span>
                <span x-text="currentProduct ? currentProduct.name : '未选择'"></span>
            </div>
            <div>
                <span class="font-medium">BOM节点:</span>
                <span x-text="totalNodes"></span>
            </div>
            <div>
                <span class="font-medium">最后更新:</span>
                <span x-text="lastUpdated"></span>
            </div>
        </div>
    </div>
    
    <!-- Add Component Modal -->
    <div x-show="showAddComponentModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 modal-backdrop" x-cloak>
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md" @click.away="showAddComponentModal = false">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">添加构件</h3>
            </div>
            
            <div class="px-6 py-4">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">构件名称</label>
                    <input 
                        type="text" 
                        x-model="newComponent.name" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入构件名称">
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">构件描述</label>
                    <textarea 
                        x-model="newComponent.description" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入构件描述"
                        rows="2"></textarea>
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">构件图标</label>
                    <div class="grid grid-cols-6 gap-2">
                        <template x-for="icon in availableIcons" :key="icon">
                            <button 
                                @click="newComponent.icon = icon"
                                :class="{'ring-2 ring-blue-500': newComponent.icon === icon}"
                                class="p-2 border border-gray-300 rounded-md hover:bg-gray-50">
                                <i :class="icon"></i>
                            </button>
                        </template>
                    </div>
                </div>
            </div>
            
            <div class="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
                <button 
                    @click="showAddComponentModal = false"
                    class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100">
                    取消
                </button>
                <button 
                    @click="addComponent()"
                    class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
                    添加
                </button>
            </div>
        </div>
    </div>
    
    <!-- Add Parameter Modal -->
    <div x-show="showAddParameterModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 modal-backdrop" x-cloak>
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md" @click.away="showAddParameterModal = false">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">添加参数</h3>
            </div>
            
            <div class="px-6 py-4">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">参数名称</label>
                    <input 
                        type="text" 
                        x-model="newParameter.name" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入参数名称">
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">参数描述</label>
                    <textarea 
                        x-model="newParameter.description" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入参数描述"
                        rows="2"></textarea>
                </div>
                
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">参数值</label>
                        <input 
                            type="text" 
                            x-model="newParameter.value" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="输入参数值">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">单位</label>
                        <input 
                            type="text" 
                            x-model="newParameter.unit" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="输入单位">
                    </div>
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">参数类型</label>
                    <select 
                        x-model="newParameter.type" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="number">数字</option>
                        <option value="text">文本</option>
                        <option value="boolean">布尔值</option>
                        <option value="select">选择</option>
                    </select>
                </div>
            </div>
            
            <div class="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
                <button 
                    @click="showAddParameterModal = false"
                    class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100">
                    取消
                </button>
                <button 
                    @click="addParameter()"
                    class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
                    添加
                </button>
            </div>
        </div>
    </div>
    
    <!-- Edit Node Modal -->
    <div x-show="showEditNodeModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 modal-backdrop" x-cloak>
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md" @click.away="showEditNodeModal = false">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">编辑节点</h3>
            </div>
            
            <div class="px-6 py-4">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">名称</label>
                    <input 
                        type="text" 
                        x-model="editingNode.name" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">描述</label>
                    <textarea 
                        x-model="editingNode.description" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        rows="2"></textarea>
                </div>
                
                <div x-show="editingNode.type === 'component'" class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">图标</label>
                    <div class="grid grid-cols-6 gap-2">
                        <template x-for="icon in availableIcons" :key="icon">
                            <button 
                                @click="editingNode.icon = icon"
                                :class="{'ring-2 ring-blue-500': editingNode.icon === icon}"
                                class="p-2 border border-gray-300 rounded-md hover:bg-gray-50">
                                <i :class="icon"></i>
                            </button>
                        </template>
                    </div>
                </div>
                
                <div x-show="editingNode.type === 'material' || editingNode.type === 'submaterial'" class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">用量公式</label>
                    <textarea 
                        x-model="editingNode.formula" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        rows="2"></textarea>
                </div>
            </div>
            
            <div class="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
                <button 
                    @click="showEditNodeModal = false"
                    class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100">
                    取消
                </button>
                <button 
                    @click="saveEditedNode()"
                    class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
                    保存
                </button>
            </div>
        </div>
    </div>
    
    <!-- Toast Notification -->
    <div x-show="toast.show" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform translate-y-2"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform translate-y-0"
         x-transition:leave-end="opacity-0 transform translate-y-2"
         class="fixed bottom-4 right-4 bg-white rounded-lg shadow-lg p-4 max-w-md z-50"
         x-cloak>
        <div class="flex items-start">
            <div :class="toast.type === 'success' ? 'text-green-500' : 'text-red-500'" class="flex-shrink-0">
                <i :class="toast.type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle'"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-gray-900" x-text="toast.title"></h3>
                <div class="mt-1 text-sm text-gray-500" x-text="toast.message"></div>
            </div>
            <div class="ml-auto pl-3">
                <button @click="toast.show = false" class="text-gray-400 hover:text-gray-500">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <script>
        function bomDesigner() {
            return {
                // Product types
                productTypes: [
                    { id: 'glass', name: '玻璃制品', icon: 'fas fa-glass-martini' },
                    { id: 'fireproof', name: '防火窗', icon: 'fas fa-fire-extinguisher' },
                    { id: 'shower', name: '淋浴房', icon: 'fas fa-shower' },
                    { id: 'custom', name: '自定义产品', icon: 'fas fa-cogs' }
                ],
                
                selectedProductType: 'fireproof',
                
                // Product parameters
                productParameters: [
                    { id: 'width', name: '宽度', description: '产品总宽度', value: '1200', unit: 'mm', type: 'number' },
                    { id: 'height', name: '高度', description: '产品总高度', value: '1500', unit: 'mm', type: 'number' },
                    { id: 'fire_rating', name: '防火等级', description: '防火窗防火等级', value: '1.5', unit: 'h', type: 'select' }
                ],
                
                // BOM structure
                bomStructure: [],
                
                // Selected nodes
                selectedNode: null,
                selectedComponent: null,
                selectedNodeFormula: '',
                
                // Modals
                showAddComponentModal: false,
                showAddParameterModal: false,
                showEditNodeModal: false,
                
                // New component
                newComponent: {
                    name: '',
                    description: '',
                    icon: 'fas fa-cube'
                },
                
                // New parameter
                newParameter: {
                    name: '',
                    description: '',
                    value: '',
                    unit: '',
                    type: 'number'
                },
                
                // Editing node
                editingNode: {},
                
                // Available icons
                availableIcons: [
                    'fas fa-cube', 'fas fa-window-maximize', 'fas fa-door-open', 
                    'fas fa-th-large', 'fas fa-square', 'fas fa-grip-horizontal',
                    'fas fa-border-all', 'fas fa-vector-square', 'fas fa-draw-polygon'
                ],
                
                // Calculation results
                calculationResults: [],
                
                // Formula error
                formulaError: '',
                
                // Toast notification
                toast: {
                    show: false,
                    type: 'success',
                    title: '',
                    message: ''
                },
                
                // Current product
                currentProduct: null,
                
                // Last updated
                lastUpdated: new Date().toLocaleTimeString(),
                
                // Initialize
                init() {
                    this.selectProductType('fireproof');
                },
                
                // Select product type
                selectProductType(typeId) {
                    this.selectedProductType = typeId;
                    this.currentProduct = this.productTypes.find(p => p.id === typeId);
                    
                    // Reset BOM structure based on product type
                    this.bomStructure = [];
                    
                    if (typeId === 'fireproof') {
                        // 防火窗默认结构 - 窗框、窗扇、固定玻璃
                        this.bomStructure = [
                            {
                                id: 'fireproof_window',
                                name: '防火窗',
                                description: '防火窗产品',
                                type: 'product',
                                expanded: true,
                                children: [
                                    {
                                        id: 'frame',
                                        name: '窗框',
                                        description: '防火窗窗框构件',
                                        type: 'component',
                                        icon: 'fas fa-border-all',
                                        expanded: true,
                                        parameters: [
                                            { id: 'frame_material', name: '窗框材质', description: '窗框材质类型', value: '钢质', unit: '', type: 'select' },
                                            { id: 'frame_thickness', name: '窗框厚度', description: '窗框厚度', value: '1.5', unit: 'mm', type: 'number' }
                                        ],
                                        children: [
                                            {
                                                id: 'frame_profile',
                                                name: '窗框型材',
                                                description: '窗框主型材',
                                                type: 'material',
                                                expanded: true,
                                                formula: '(width + height) * 2 / 1000',
                                                children: [
                                                    {
                                                        id: 'frame_profile_cut',
                                                        name: '型材切割',
                                                        description: '型材切割加工',
                                                        type: 'submaterial',
                                                        formula: '(width + height) * 2 / 1000'
                                                    }
                                                ]
                                            },
                                            {
                                                id: 'frame_corner',
                                                name: '窗框角码',
                                                description: '窗框连接角码',
                                                type: 'material',
                                                formula: '4',
                                                children: [
                                                    {
                                                        id: 'frame_corner_connect',
                                                        name: '角码连接',
                                                        description: '角码连接件',
                                                        type: 'submaterial',
                                                        formula: '8'
                                                    }
                                                ]
                                            }
                                        ]
                                    },
                                    {
                                        id: 'sash',
                                        name: '窗扇',
                                        description: '防火窗窗扇构件',
                                        type: 'component',
                                        icon: 'fas fa-door-open',
                                        expanded: true,
                                        parameters: [
                                            { id: 'sash_material', name: '窗扇材质', description: '窗扇材质类型', value: '钢质', unit: '', type: 'select' },
                                            { id: 'sash_thickness', name: '窗扇厚度', description: '窗扇厚度', value: '1.2', unit: 'mm', type: 'number' }
                                        ],
                                        children: [
                                            {
                                                id: 'sash_profile',
                                                name: '窗扇型材',
                                                description: '窗扇主型材',
                                                type: 'material',
                                                expanded: true,
                                                formula: '(width/2 + height) * 2 / 1000',
                                                children: [
                                                    {
                                                        id: 'sash_profile_cut',
                                                        name: '型材切割',
                                                        description: '型材切割加工',
                                                        type: 'submaterial',
                                                        formula: '(width/2 + height) * 2 / 1000'
                                                    }
                                                ]
                                            },
                                            {
                                                id: 'sash_glass',
                                                name: '窗扇玻璃',
                                                description: '窗扇防火玻璃',
                                                type: 'material',
                                                expanded: true,
                                                formula: '(width/2 - 20) * (height - 20) / 1000000',
                                                children: [
                                                    {
                                                        id: 'sash_glass_cut',
                                                        name: '玻璃切割',
                                                        description: '玻璃切割加工',
                                                        type: 'submaterial',
                                                        formula: '(width/2 - 20) * (height - 20) / 1000000'
                                                    }
                                                ]
                                            },
                                            {
                                                id: 'sash_hardware',
                                                name: '窗扇五金',
                                                description: '窗扇五金件',
                                                type: 'material',
                                                formula: '1',
                                                children: [
                                                    {
                                                        id: 'sash_hinge',
                                                        name: '铰链',
                                                        description: '窗扇铰链',
                                                        type: 'submaterial',
                                                        formula: '2'
                                                    },
                                                    {
                                                        id: 'sash_handle',
                                                        name: '把手',
                                                        description: '窗扇把手',
                                                        type: 'submaterial',
                                                        formula: '1'
                                                    }
                                                ]
                                            }
                                        ]
                                    },
                                    {
                                        id: 'fixed_glass',
                                        name: '固定玻璃',
                                        description: '防火窗固定玻璃构件',
                                        type: 'component',
                                        icon: 'fas fa-th-large',
                                        expanded: true,
                                        parameters: [
                                            { id: 'glass_type', name: '玻璃类型', description: '固定玻璃类型', value: '防火玻璃', unit: '', type: 'select' },
                                            { id: 'glass_thickness', name: '玻璃厚度', description: '玻璃厚度', value: '12', unit: 'mm', type: 'number' }
                                        ],
                                        children: [
                                            {
                                                id: 'fixed_glass_panel',
                                                name: '固定玻璃板',
                                                description: '固定防火玻璃板',
                                                type: 'material',
                                                expanded: true,
                                                formula: '(width/2 - 20) * (height - 20) / 1000000',
                                                children: [
                                                    {
                                                        id: 'fixed_glass_cut',
                                                        name: '玻璃切割',
                                                        description: '玻璃切割加工',
                                                        type: 'submaterial',
                                                        formula: '(width/2 - 20) * (height - 20) / 1000000'
                                                    }
                                                ]
                                            },
                                            {
                                                id: 'glass_sealant',
                                                name: '玻璃密封胶',
                                                description: '玻璃密封胶条',
                                                type: 'material',
                                                formula: '(width/2 + height) * 2 / 1000',
                                                children: [
                                                    {
                                                        id: 'glass_sealant_install',
                                                        name: '密封胶安装',
                                                        description: '密封胶安装',
                                                        type: 'submaterial',
                                                        formula: '(width/2 + height) * 2 / 1000'
                                                    }
                                                ]
                                            }
                                        ]
                                    }
                                ]
                            }
                        ];
                    } else if (typeId === 'shower') {
                        // 淋浴房默认结构
                        this.bomStructure = [
                            {
                                id: 'shower_room',
                                name: '淋浴房',
                                description: '淋浴房产品',
                                type: 'product',
                                expanded: true,
                                children: [
                                    {
                                        id: 'shower_frame',
                                        name: '框架',
                                        description: '淋浴房框架构件',
                                        type: 'component',
                                        icon: 'fas fa-border-all',
                                        expanded: true,
                                        children: [
                                            {
                                                id: 'frame_profile',
                                                name: '框架型材',
                                                description: '框架主型材',
                                                type: 'material',
                                                expanded: true,
                                                formula: '(width + height * 2) / 1000',
                                                children: []
                                            }
                                        ]
                                    },
                                    {
                                        id: 'shower_glass',
                                        name: '玻璃',
                                        description: '淋浴房玻璃构件',
                                        type: 'component',
                                        icon: 'fas fa-th-large',
                                        expanded: true,
                                        children: [
                                            {
                                                id: 'glass_panel',
                                                name: '玻璃板',
                                                description: '淋浴房玻璃板',
                                                type: 'material',
                                                expanded: true,
                                                formula: 'width * height / 1000000',
                                                children: []
                                            }
                                        ]
                                    },
                                    {
                                        id: 'shower_hardware',
                                        name: '五金',
                                        description: '淋浴房五金构件',
                                        type: 'component',
                                        icon: 'fas fa-tools',
                                        expanded: true,
                                        children: [
                                            {
                                                id: 'door_handle',
                                                name: '门把手',
                                                description: '淋浴房门把手',
                                                type: 'material',
                                                expanded: true,
                                                formula: '1',
                                                children: []
                                            },
                                            {
                                                id: 'roller',
                                                name: '滑轮',
                                                description: '淋浴房滑轮',
                                                type: 'material',
                                                expanded: true,
                                                formula: '2',
                                                children: []
                                            }
                                        ]
                                    }
                                ]
                            }
                        ];
                    } else if (typeId === 'glass') {
                        // 玻璃制品默认结构
                        this.bomStructure = [
                            {
                                id: 'glass_product',
                                name: '玻璃制品',
                                description: '玻璃制品产品',
                                type: 'product',
                                expanded: true,
                                children: [
                                    {
                                        id: 'glass_material',
                                        name: '玻璃材料',
                                        description: '玻璃材料',
                                        type: 'component',
                                        icon: 'fas fa-glass-martini',
                                        expanded: true,
                                        children: [
                                            {
                                                id: 'glass_panel',
                                                name: '玻璃板',
                                                description: '玻璃板',
                                                type: 'material',
                                                expanded: true,
                                                formula: 'width * height / 1000000',
                                                children: []
                                            }
                                        ]
                                    }
                                ]
                            }
                        ];
                    } else {
                        // 自定义产品默认结构
                        this.bomStructure = [
                            {
                                id: 'custom_product',
                                name: '自定义产品',
                                description: '自定义产品',
                                type: 'product',
                                expanded: true,
                                children: []
                            }
                        ];
                    }
                    
                    this.updateLastUpdated();
                    this.showToast('success', '产品类型已切换', `已切换到${this.currentProduct.name}`);
                },
                
                // Select node
                selectNode(node) {
                    this.selectedNode = node;
                    this.selectedNodeFormula = node.formula || '';
                    
                    if (node.type === 'component') {
                        this.selectedComponent = node;
                    } else {
                        // Find parent component
                        this.selectedComponent = this.findParentComponent(node);
                    }
                    
                    this.updateAvailableParameters();
                },
                
                // Find parent component
                findParentComponent(node) {
                    for (let product of this.bomStructure) {
                        for (let component of product.children) {
                            if (component.children && component.children.includes(node)) {
                                return component;
                            }
                            
                            if (component.children) {
                                for (let material of component.children) {
                                    if (material.children && material.children.includes(node)) {
                                        return component;
                                    }
                                }
                            }
                        }
                    }
                    return null;
                },
                
                // Toggle node expand
                toggleNodeExpand(node) {
                    node.expanded = !node.expanded;
                },
                
                // Add component
                addComponent() {
                    if (!this.newComponent.name) {
                        this.showToast('error', '添加失败', '请输入构件名称');
                        return;
                    }
                    
                    const newComponent = {
                        id: 'component_' + Date.now(),
                        name: this.newComponent.name,
                        description: this.newComponent.description,
                        type: 'component',
                        icon: this.newComponent.icon,
                        expanded: true,
                        parameters: [],
                        children: []
                    };
                    
                    this.bomStructure[0].children.push(newComponent);
                    
                    // Reset form
                    this.newComponent = {
                        name: '',
                        description: '',
                        icon: 'fas fa-cube'
                    };
                    
                    this.showAddComponentModal = false;
                    this.updateLastUpdated();
                    this.showToast('success', '添加成功', '构件已成功添加');
                },
                
                // Add component material
                addComponentMaterial(component) {
                    const newMaterial = {
                        id: 'material_' + Date.now(),
                        name: '新原料',
                        description: '新原料描述',
                        type: 'material',
                        expanded: true,
                        formula: '',
                        children: []
                    };
                    
                    component.children.push(newMaterial);
                    this.updateLastUpdated();
                    this.showToast('success', '添加成功', '原料已成功添加');
                },
                
                // Add material subitem
                addMaterialSubitem(material) {
                    const newSubitem = {
                        id: 'subitem_' + Date.now(),
                        name: '新辅料',
                        description: '新辅料描述',
                        type: 'submaterial',
                        formula: ''
                    };
                    
                    material.children.push(newSubitem);
                    this.updateLastUpdated();
                    this.showToast('success', '添加成功', '辅料已成功添加');
                },
                
                // Edit node
                editNode(node) {
                    this.editingNode = JSON.parse(JSON.stringify(node));
                    this.showEditNodeModal = true;
                },
                
                // Save edited node
                saveEditedNode() {
                    if (!this.editingNode.name) {
                        this.showToast('error', '保存失败', '请输入节点名称');
                        return;
                    }
                    
                    // Find and update the node
                    this.updateNodeInStructure(this.bomStructure, this.editingNode);
                    
                    this.showEditNodeModal = false;
                    this.updateLastUpdated();
                    this.showToast('success', '保存成功', '节点已成功更新');
                },
                
                // Update node in structure
                updateNodeInStructure(structure, updatedNode) {
                    for (let i = 0; i < structure.length; i++) {
                        if (structure[i].id === updatedNode.id) {
                            structure[i] = { ...structure[i], ...updatedNode };
                            return true;
                        }
                        
                        if (structure[i].children && structure[i].children.length > 0) {
                            if (this.updateNodeInStructure(structure[i].children, updatedNode)) {
                                return true;
                            }
                        }
                    }
                    return false;
                },
                
                // Delete node
                deleteNode(node) {
                    if (confirm(`确定要删除"${node.name}"吗？`)) {
                        this.removeNodeFromStructure(this.bomStructure, node);
                        this.updateLastUpdated();
                        this.showToast('success', '删除成功', '节点已成功删除');
                    }
                },
                
                // Remove node from structure
                removeNodeFromStructure(structure, nodeToRemove) {
                    for (let i = 0; i < structure.length; i++) {
                        if (structure[i].id === nodeToRemove.id) {
                            structure.splice(i, 1);
                            return true;
                        }
                        
                        if (structure[i].children && structure[i].children.length > 0) {
                            if (this.removeNodeFromStructure(structure[i].children, nodeToRemove)) {
                                return true;
                            }
                        }
                    }
                    return false;
                },
                
                // Add parameter
                addParameter() {
                    if (!this.newParameter.name) {
                        this.showToast('error', '添加失败', '请输入参数名称');
                        return;
                    }
                    
                    const newParameter = {
                        id: 'param_' + Date.now(),
                        name: this.newParameter.name,
                        description: this.newParameter.description,
                        value: this.newParameter.value,
                        unit: this.newParameter.unit,
                        type: this.newParameter.type
                    };
                    
                    this.productParameters.push(newParameter);
                    
                    // Reset form
                    this.newParameter = {
                        name: '',
                        description: '',
                        value: '',
                        unit: '',
                        type: 'number'
                    };
                    
                    this.showAddParameterModal = false;
                    this.updateLastUpdated();
                    this.showToast('success', '添加成功', '参数已成功添加');
                },
                
                // Insert parameter into formula
                insertParameter(param) {
                    if (this.selectedNode && (this.selectedNode.type === 'material' || this.selectedNode.type === 'submaterial')) {
                        this.selectedNodeFormula += param.name;
                        this.selectedNode.formula = this.selectedNodeFormula;
                    }
                },
                
                // Save formula
                saveFormula() {
                    if (!this.selectedNode) {
                        this.showToast('error', '保存失败', '请选择一个节点');
                        return;
                    }
                    
                    if (this.selectedNode.type !== 'material' && this.selectedNode.type !== 'submaterial') {
                        this.showToast('error', '保存失败', '只能为原料或辅料设置公式');
                        return;
                    }
                    
                    // Validate formula
                    if (!this.validateFormula(this.selectedNodeFormula)) {
                        return;
                    }
                    
                    this.selectedNode.formula = this.selectedNodeFormula;
                    this.updateLastUpdated();
                    this.showToast('success', '保存成功', '公式已成功保存');
                },
                
                // Validate formula
                validateFormula(formula) {
                    if (!formula) {
                        this.formulaError = '公式不能为空';
                        return false;
                    }
                    
                    // Simple validation - check for balanced parentheses
                    let openParentheses = 0;
                    for (let char of formula) {
                        if (char === '(') openParentheses++;
                        if (char === ')') openParentheses--;
                        if (openParentheses < 0) {
                            this.formulaError = '括号不匹配';
                            return false;
                        }
                    }
                    
                    if (openParentheses !== 0) {
                        this.formulaError = '括号不匹配';
                        return false;
                    }
                    
                    // Check for invalid characters
                    const validChars = /^[0-9+\-*/().\sA-Za-z_]+$/;
                    if (!validChars.test(formula)) {
                        this.formulaError = '公式包含无效字符';
                        return false;
                    }
                    
                    this.formulaError = '';
                    return true;
                },
                
                // Update available parameters
                updateAvailableParameters() {
                    // This will be used to show available parameters in the formula editor
                    // Implementation depends on your specific requirements
                },
                
                // Get available parameters
                get availableParameters() {
                    let params = [...this.productParameters];
                    
                    if (this.selectedComponent && this.selectedComponent.parameters) {
                        params = [...params, ...this.selectedComponent.parameters];
                    }
                    
                    return params;
                },
                
                // Run calculation test
                runCalculationTest() {
                    this.calculationResults = [];
                    
                    // Collect all materials and submaterials with formulas
                    const materials = [];
                    this.collectMaterialsWithFormulas(this.bomStructure, materials);
                    
                    // Calculate each material
                    for (let material of materials) {
                        try {
                            const result = this.evaluateFormula(material.formula);
                            this.calculationResults.push({
                                id: material.id,
                                name: material.name,
                                value: result.toFixed(2),
                                unit: this.getUnitForMaterial(material)
                            });
                        } catch (error) {
                            this.calculationResults.push({
                                id: material.id,
                                name: material.name,
                                value: '计算错误',
                                unit: ''
                            });
                        }
                    }
                    
                    this.updateLastUpdated();
                },
                
                // Collect materials with formulas
                collectMaterialsWithFormulas(structure, materials) {
                    for (let node of structure) {
                        if ((node.type === 'material' || node.type === 'submaterial') && node.formula) {
                            materials.push(node);
                        }
                        
                        if (node.children && node.children.length > 0) {
                            this.collectMaterialsWithFormulas(node.children, materials);
                        }
                    }
                },
                
                // Get unit for material
                getUnitForMaterial(material) {
                    // This is a simplified implementation
                    // In a real application, you would have a more sophisticated way to determine units
                    if (material.name.includes('型材') || material.name.includes('胶条')) {
                        return 'm';
                    } else if (material.name.includes('玻璃')) {
                        return 'm²';
                    } else if (material.name.includes('角码') || material.name.includes('铰链') || material.name.includes('把手')) {
                        return '个';
                    }
                    return '';
                },
                
                // Evaluate formula
                evaluateFormula(formula) {
                    // Create a context with parameter values
                    const context = {};
                    
                    // Add product parameters
                    for (let param of this.productParameters) {
                        context[param.name] = parseFloat(param.value) || 0;
                    }
                    
                    // Add component parameters if a component is selected
                    if (this.selectedComponent && this.selectedComponent.parameters) {
                        for (let param of this.selectedComponent.parameters) {
                            context[param.name] = parseFloat(param.value) || 0;
                        }
                    }
                    
                    // Replace parameter names with values
                    let expression = formula;
                    for (let name in context) {
                        const regex = new RegExp(`\\b${name}\\b`, 'g');
                        expression = expression.replace(regex, context[name]);
                    }
                    
                    // Evaluate the expression
                    // Note: In a real application, you would use a safer expression evaluator
                    try {
                        // eslint-disable-next-line no-eval
                        return eval(expression);
                    } catch (error) {
                        throw new Error('公式计算错误');
                    }
                },
                
                // Update calculations
                updateCalculations() {
                    // This method is called when parameter values change
                    // You can implement auto-calculation here if needed
                    this.updateLastUpdated();
                },
                
                // Export BOM
                exportBOM() {
                    // In a real application, this would generate and download a file
                    this.showToast('success', '导出成功', 'BOM已成功导出');
                    this.updateLastUpdated();
                },
                
                // Show toast notification
                showToast(type, title, message) {
                    this.toast = {
                        show: true,
                        type,
                        title,
                        message
                    };
                    
                    // Auto hide after 3 seconds
                    setTimeout(() => {
                        this.toast.show = false;
                    }, 3000);
                },
                
                // Update last updated time
                updateLastUpdated() {
                    this.lastUpdated = new Date().toLocaleTimeString();
                },
                
                // Get total nodes count
                get totalNodes() {
                    let count = 0;
                    this.countNodes(this.bomStructure, count);
                    return count;
                },
                
                // Count nodes
                countNodes(structure) {
                    let count = 0;
                    for (let node of structure) {
                        count++;
                        if (node.children && node.children.length > 0) {
                            count += this.countNodes(node.children);
                        }
                    }
                    return count;
                }
            };
        }
    </script>
</body>
</html>
