<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>质量检查点管理 - PDM系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        [x-cloak] { display: none !important; }
        .font-chinese {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }
        .custom-scrollbar {
            scrollbar-width: thin;
            scrollbar-color: #CBD5E0 #F7FAFC;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe', 
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8'
                        },
                        success: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            500: '#22c55e',
                            600: '#16a34a'
                        },
                        warning: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            500: '#f59e0b',
                            600: '#d97706'
                        },
                        error: {
                            50: '#fef2f2',
                            100: '#fee2e2',
                            500: '#ef4444',
                            600: '#dc2626'
                        },
                        'title-gray': '#1f2937'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 font-chinese">
    <div x-data="qualityControlApp()" x-cloak class="min-h-screen">
        <!-- 顶部导航栏 -->
        <nav class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0">
                            <h1 class="text-xl font-semibold text-title-gray">质量检查点管理</h1>
                        </div>
                        <div class="hidden md:block">
                            <div class="ml-10 flex items-baseline space-x-4">
                                <span class="text-sm text-gray-600">项目: <span class="font-medium text-primary-600">华彩大厦玻璃幕墙</span></span>
                                <span class="text-sm text-gray-600">|</span>
                                <span class="text-sm text-gray-600">质量等级: <span class="font-medium text-success-600">A级</span></span>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button @click="showQualityReportModal = true" class="bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-700">
                            质量报告
                        </button>
                        <button @click="showCheckpointModal = true" class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700">
                            新建检查点
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- 主要内容区域 -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- 质量概览仪表板 -->
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
                <!-- 质量统计卡片 -->
                <div class="lg:col-span-1 space-y-6">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-primary-100 rounded-md flex items-center justify-center">
                                    <svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">检查点总数</dt>
                                    <dd class="text-lg font-medium text-gray-900">32</dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-success-100 rounded-md flex items-center justify-center">
                                    <svg class="w-5 h-5 text-success-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">通过检查</dt>
                                    <dd class="text-lg font-medium text-gray-900">28</dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-warning-100 rounded-md flex items-center justify-center">
                                    <svg class="w-5 h-5 text-warning-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">需要整改</dt>
                                    <dd class="text-lg font-medium text-gray-900">3</dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-error-100 rounded-md flex items-center justify-center">
                                    <svg class="w-5 h-5 text-error-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">不合格项</dt>
                                    <dd class="text-lg font-medium text-gray-900">1</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 质量趋势图表 -->
                <div class="lg:col-span-3 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-title-gray">质量趋势分析</h3>
                        <div class="flex space-x-2">
                            <button @click="chartPeriod = 'week'" :class="chartPeriod === 'week' ? 'bg-primary-100 text-primary-600' : 'text-gray-600'" class="px-3 py-1 rounded-md text-sm">
                                周视图
                            </button>
                            <button @click="chartPeriod = 'month'" :class="chartPeriod === 'month' ? 'bg-primary-100 text-primary-600' : 'text-gray-600'" class="px-3 py-1 rounded-md text-sm">
                                月视图
                            </button>
                        </div>
                    </div>
                    <div class="h-64">
                        <canvas id="qualityTrendChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- 标签导航 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8 px-6">
                        <template x-for="tab in tabs" :key="tab.id">
                            <button 
                                @click="activeTab = tab.id"
                                :class="activeTab === tab.id ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors">
                                <span x-text="tab.name"></span>
                                <span x-show="tab.badge" :class="activeTab === tab.id ? 'bg-primary-100 text-primary-600' : 'bg-gray-100 text-gray-600'" class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" x-text="tab.badge"></span>
                            </button>
                        </template>
                    </nav>
                </div>

                <!-- 检查点列表 -->
                <div x-show="activeTab === 'checkpoints'" class="p-6">
                    <!-- 过滤器 -->
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0 sm:space-x-4 mb-6">
                        <div class="flex-1">
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                                <input type="text" x-model="searchTerm" 
                                       class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500" 
                                       placeholder="搜索检查点...">
                            </div>
                        </div>
                        <div class="flex space-x-3">
                            <select x-model="filterPhase" class="block w-full px-3 py-2 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                <option value="">全部阶段</option>
                                <option value="design">设计阶段</option>
                                <option value="review">评审阶段</option>
                                <option value="production">生产准备</option>
                                <option value="installation">安装阶段</option>
                            </select>
                            <select x-model="filterStatus" class="block w-full px-3 py-2 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                <option value="">全部状态</option>
                                <option value="pending">待检查</option>
                                <option value="passed">通过</option>
                                <option value="failed">不合格</option>
                                <option value="rectifying">整改中</option>
                            </select>
                        </div>
                    </div>

                    <!-- 检查点列表 -->
                    <div class="space-y-4">
                        <template x-for="checkpoint in filteredCheckpoints" :key="checkpoint.id">
                            <div class="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-3 mb-2">
                                            <h4 class="text-sm font-medium text-gray-900" x-text="checkpoint.name"></h4>
                                            <span :class="getStatusColor(checkpoint.status)" 
                                                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" 
                                                  x-text="getStatusText(checkpoint.status)"></span>
                                            <span :class="getPriorityColor(checkpoint.priority)" 
                                                  class="inline-flex items-center px-2 py-1 rounded text-xs font-medium" 
                                                  x-text="getPriorityText(checkpoint.priority)"></span>
                                        </div>
                                        
                                        <p class="text-sm text-gray-600 mb-3" x-text="checkpoint.description"></p>
                                        
                                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-xs">
                                            <div>
                                                <span class="text-gray-500">检查阶段:</span>
                                                <span class="font-medium ml-1" x-text="getPhaseText(checkpoint.phase)"></span>
                                            </div>
                                            <div>
                                                <span class="text-gray-500">检查类型:</span>
                                                <span class="font-medium ml-1" x-text="checkpoint.type"></span>
                                            </div>
                                            <div>
                                                <span class="text-gray-500">检查员:</span>
                                                <span class="font-medium ml-1" x-text="checkpoint.inspector"></span>
                                            </div>
                                            <div>
                                                <span class="text-gray-500">计划时间:</span>
                                                <span class="font-medium ml-1" x-text="checkpoint.plannedDate"></span>
                                            </div>
                                        </div>

                                        <!-- 检查标准 -->
                                        <div class="mt-3 bg-white rounded-md p-3">
                                            <div class="flex items-center justify-between mb-2">
                                                <h5 class="text-xs font-medium text-gray-700">检查标准</h5>
                                                <span class="text-xs text-gray-500">
                                                    <span x-text="checkpoint.criteriaCount"></span> 个标准
                                                </span>
                                            </div>
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                                                <template x-for="criteria in checkpoint.criteria.slice(0, 4)" :key="criteria.id">
                                                    <div class="flex items-center justify-between text-xs">
                                                        <span x-text="criteria.name"></span>
                                                        <span :class="criteria.status === 'pass' ? 'text-success-600' : criteria.status === 'fail' ? 'text-error-600' : 'text-gray-500'">
                                                            <svg x-show="criteria.status === 'pass'" class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                            </svg>
                                                            <svg x-show="criteria.status === 'fail'" class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                                            </svg>
                                                            <span x-show="!criteria.status" class="text-gray-400">-</span>
                                                        </span>
                                                    </div>
                                                </template>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="flex flex-col space-y-2">
                                        <button @click="executeCheckpoint(checkpoint)" 
                                                :disabled="checkpoint.status === 'passed'"
                                                class="px-3 py-1 bg-primary-600 text-white rounded-md text-xs hover:bg-primary-700 disabled:bg-gray-300">
                                            执行检查
                                        </button>
                                        <button @click="viewCheckpointDetail(checkpoint)" 
                                                class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-xs hover:bg-gray-200">
                                            查看详情
                                        </button>
                                        <template x-if="checkpoint.status === 'failed'">
                                            <button @click="rectifyCheckpoint(checkpoint)" 
                                                    class="px-3 py-1 bg-warning-600 text-white rounded-md text-xs hover:bg-warning-700">
                                                整改处理
                                            </button>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- 质量报告 -->
                <div x-show="activeTab === 'reports'" class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 质量得分卡 -->
                        <div class="bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-lg font-medium">整体质量得分</h3>
                                    <p class="text-primary-100 text-sm mt-1">基于32个检查点的综合评估</p>
                                </div>
                                <div class="text-right">
                                    <div class="text-3xl font-bold">92.5</div>
                                    <div class="text-primary-100 text-sm">A级标准</div>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex justify-between text-sm mb-1">
                                    <span>质量进度</span>
                                    <span>29/32 已完成</span>
                                </div>
                                <div class="w-full bg-primary-400 rounded-full h-2">
                                    <div class="bg-white h-2 rounded-full" style="width: 90.6%"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 问题统计 -->
                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">问题分布统计</h3>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-3 h-3 bg-success-500 rounded-full"></div>
                                        <span class="text-sm text-gray-700">通过检查</span>
                                    </div>
                                    <div class="text-sm font-medium text-gray-900">28 (87.5%)</div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-3 h-3 bg-warning-500 rounded-full"></div>
                                        <span class="text-sm text-gray-700">需要整改</span>
                                    </div>
                                    <div class="text-sm font-medium text-gray-900">3 (9.4%)</div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-3 h-3 bg-error-500 rounded-full"></div>
                                        <span class="text-sm text-gray-700">不合格项</span>
                                    </div>
                                    <div class="text-sm font-medium text-gray-900">1 (3.1%)</div>
                                </div>
                            </div>
                        </div>

                        <!-- 阶段质量统计 -->
                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">各阶段质量统计</h3>
                            <div class="space-y-4">
                                <template x-for="phase in phaseStats" :key="phase.name">
                                    <div>
                                        <div class="flex justify-between items-center mb-2">
                                            <span class="text-sm font-medium text-gray-700" x-text="phase.name"></span>
                                            <span class="text-sm text-gray-500" x-text="phase.score + '%'"></span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-2">
                                            <div :style="`width: ${phase.score}%`" 
                                                 :class="phase.score >= 90 ? 'bg-success-500' : phase.score >= 70 ? 'bg-warning-500' : 'bg-error-500'" 
                                                 class="h-2 rounded-full transition-all duration-300"></div>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>

                        <!-- 改进建议 -->
                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">改进建议</h3>
                            <div class="space-y-3">
                                <template x-for="suggestion in suggestions" :key="suggestion.id">
                                    <div class="flex items-start space-x-3">
                                        <div :class="suggestion.priority === 'high' ? 'bg-error-100 text-error-600' : suggestion.priority === 'medium' ? 'bg-warning-100 text-warning-600' : 'bg-gray-100 text-gray-600'" 
                                             class="flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium">
                                            <span x-text="suggestion.priority === 'high' ? 'H' : suggestion.priority === 'medium' ? 'M' : 'L'"></span>
                                        </div>
                                        <div class="flex-1">
                                            <p class="text-sm text-gray-900" x-text="suggestion.title"></p>
                                            <p class="text-xs text-gray-500 mt-1" x-text="suggestion.description"></p>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 检查历史 -->
                <div x-show="activeTab === 'history'" class="p-6">
                    <div class="overflow-hidden">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查点</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查员</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查时间</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">结果</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">得分</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <template x-for="record in checkHistory" :key="record.id">
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900" x-text="record.checkpointName"></div>
                                            <div class="text-sm text-gray-500" x-text="record.phase"></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900" x-text="record.inspector"></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900" x-text="record.checkDate"></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span :class="getStatusColor(record.result)" 
                                                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" 
                                                  x-text="getStatusText(record.result)"></span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900" x-text="record.score + '/100'"></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button @click="viewRecord(record)" class="text-primary-600 hover:text-primary-700 mr-3">查看</button>
                                            <button @click="downloadReport(record)" class="text-gray-600 hover:text-gray-700">下载</button>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 新建检查点模态框 -->
        <div x-show="showCheckpointModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" style="display: none;">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-medium text-title-gray">新建质量检查点</h3>
                    <button @click="showCheckpointModal = false" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <form @submit.prevent="saveCheckpoint">
                    <div class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">检查点名称 *</label>
                                <input type="text" x-model="newCheckpoint.name" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">检查阶段 *</label>
                                <select x-model="newCheckpoint.phase" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                    <option value="">请选择阶段</option>
                                    <option value="design">设计阶段</option>
                                    <option value="review">评审阶段</option>
                                    <option value="production">生产准备</option>
                                    <option value="installation">安装阶段</option>
                                </select>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">检查类型</label>
                                <select x-model="newCheckpoint.type"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                    <option value="">请选择类型</option>
                                    <option value="设计验证">设计验证</option>
                                    <option value="材料检验">材料检验</option>
                                    <option value="工艺检查">工艺检查</option>
                                    <option value="安全检查">安全检查</option>
                                    <option value="质量检验">质量检验</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">优先级</label>
                                <select x-model="newCheckpoint.priority"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                    <option value="low">低</option>
                                    <option value="medium" selected>中</option>
                                    <option value="high">高</option>
                                    <option value="critical">关键</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">计划检查时间</label>
                                <input type="date" x-model="newCheckpoint.plannedDate"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">检查点描述</label>
                            <textarea x-model="newCheckpoint.description" rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                                      placeholder="详细描述检查内容和要求..."></textarea>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">检查标准</label>
                            <div class="space-y-2">
                                <template x-for="(criteria, index) in newCheckpoint.criteria" :key="index">
                                    <div class="flex items-center space-x-2">
                                        <input type="text" x-model="criteria.name" placeholder="检查标准名称"
                                               class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                        <input type="text" x-model="criteria.requirement" placeholder="要求"
                                               class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                        <button type="button" @click="removeCriteria(index)" class="text-error-600 hover:text-error-700">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </template>
                                <button type="button" @click="addCriteria" class="text-primary-600 hover:text-primary-700 text-sm">
                                    + 添加检查标准
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" @click="showCheckpointModal = false" 
                                class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                            取消
                        </button>
                        <button type="submit" 
                                class="px-4 py-2 bg-primary-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-primary-700">
                            创建检查点
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 质量报告模态框 -->
        <div x-show="showQualityReportModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" style="display: none;">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-4/5 shadow-lg rounded-md bg-white max-h-screen overflow-y-auto">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-medium text-title-gray">质量检查报告</h3>
                    <div class="flex space-x-2">
                        <button @click="exportReport" class="bg-success-600 text-white px-4 py-2 rounded-md text-sm hover:bg-success-700">
                            导出报告
                        </button>
                        <button @click="showQualityReportModal = false" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- 报告内容 -->
                <div class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-primary-50 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-primary-600">92.5</div>
                            <div class="text-sm text-primary-700">整体质量得分</div>
                        </div>
                        <div class="bg-success-50 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-success-600">87.5%</div>
                            <div class="text-sm text-success-700">检查通过率</div>
                        </div>
                        <div class="bg-warning-50 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-warning-600">4</div>
                            <div class="text-sm text-warning-700">待处理问题</div>
                        </div>
                    </div>

                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="font-medium text-gray-900 mb-3">报告摘要</h4>
                        <p class="text-sm text-gray-700">
                            本次质量检查涵盖了华彩大厦玻璃幕墙项目的32个关键检查点，涉及设计验证、材料检验、工艺检查等多个方面。
                            总体质量水平达到A级标准，检查通过率为87.5%。发现4个需要处理的问题，其中1个为不合格项，需要立即整改。
                            建议加强生产准备阶段的质量控制，特别是材料检验和工艺验证环节。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function qualityControlApp() {
            return {
                activeTab: 'checkpoints',
                searchTerm: '',
                filterPhase: '',
                filterStatus: '',
                chartPeriod: 'week',
                showCheckpointModal: false,
                showQualityReportModal: false,
                
                tabs: [
                    { id: 'checkpoints', name: '检查点管理', badge: '32' },
                    { id: 'reports', name: '质量报告', badge: null },
                    { id: 'history', name: '检查历史', badge: null }
                ],
                
                newCheckpoint: {
                    name: '',
                    phase: '',
                    type: '',
                    priority: 'medium',
                    plannedDate: '',
                    description: '',
                    criteria: [
                        { name: '', requirement: '' }
                    ]
                },
                
                checkpoints: [
                    {
                        id: 1,
                        name: '玻璃规格符合性检查',
                        description: '验证玻璃厚度、尺寸、性能参数是否符合设计要求',
                        phase: 'design',
                        type: '设计验证',
                        status: 'passed',
                        priority: 'critical',
                        inspector: '王工程师',
                        plannedDate: '2024-01-15',
                        criteriaCount: 6,
                        criteria: [
                            { id: 1, name: '玻璃厚度', requirement: '6+12A+6mm', status: 'pass' },
                            { id: 2, name: '可见光透射比', requirement: '≥80%', status: 'pass' },
                            { id: 3, name: '太阳能透射比', requirement: '≤50%', status: 'pass' },
                            { id: 4, name: '保温性能', requirement: 'K≤2.8', status: 'pass' }
                        ]
                    },
                    {
                        id: 2,
                        name: '结构胶粘接质量检查',
                        description: '检查结构胶的品牌、性能、施工质量',
                        phase: 'production',
                        type: '工艺检查',
                        status: 'failed',
                        priority: 'high',
                        inspector: '李工程师',
                        plannedDate: '2024-01-16',
                        criteriaCount: 4,
                        criteria: [
                            { id: 1, name: '胶缝宽度', requirement: '20±2mm', status: 'fail' },
                            { id: 2, name: '粘接强度', requirement: '≥0.6MPa', status: 'pass' },
                            { id: 3, name: '外观质量', requirement: '无气泡', status: 'pass' },
                            { id: 4, name: '固化时间', requirement: '≥24h', status: 'pass' }
                        ]
                    },
                    {
                        id: 3,
                        name: '铝框架加工精度检查',
                        description: '检查铝合金框架的加工精度和表面处理质量',
                        phase: 'production',
                        type: '质量检验',
                        status: 'rectifying',
                        priority: 'medium',
                        inspector: '张工程师',
                        plannedDate: '2024-01-17',
                        criteriaCount: 5,
                        criteria: [
                            { id: 1, name: '角度精度', requirement: '±0.5°', status: 'fail' },
                            { id: 2, name: '直线度', requirement: '≤2mm/m', status: 'pass' },
                            { id: 3, name: '表面粗糙度', requirement: 'Ra≤3.2', status: 'pass' },
                            { id: 4, name: '氧化膜厚度', requirement: '≥15μm', status: 'pass' }
                        ]
                    },
                    {
                        id: 4,
                        name: '抗风压性能验证',
                        description: '验证幕墙系统的抗风压性能是否满足设计要求',
                        phase: 'review',
                        type: '设计验证',
                        status: 'passed',
                        priority: 'critical',
                        inspector: '刘工程师',
                        plannedDate: '2024-01-18',
                        criteriaCount: 3,
                        criteria: [
                            { id: 1, name: '设计风压', requirement: '3.0kPa', status: 'pass' },
                            { id: 2, name: '安全系数', requirement: '≥2.5', status: 'pass' },
                            { id: 3, name: '变形限值', requirement: 'L/150', status: 'pass' }
                        ]
                    },
                    {
                        id: 5,
                        name: '防火性能检查',
                        description: '检查防火玻璃和防火密封材料的性能',
                        phase: 'design',
                        type: '安全检查',
                        status: 'pending',
                        priority: 'critical',
                        inspector: '王工程师',
                        plannedDate: '2024-01-19',
                        criteriaCount: 4,
                        criteria: [
                            { id: 1, name: '耐火时间', requirement: '≥60min', status: null },
                            { id: 2, name: '防火等级', requirement: 'A级', status: null },
                            { id: 3, name: '烟气密度', requirement: '≤75', status: null },
                            { id: 4, name: '毒性指标', requirement: 'ZA2级', status: null }
                        ]
                    },
                    {
                        id: 6,
                        name: '五金配件功能检查',
                        description: '检查开启扇五金配件的功能和耐久性',
                        phase: 'production',
                        type: '质量检验',
                        status: 'rectifying',
                        priority: 'medium',
                        inspector: '李工程师',
                        plannedDate: '2024-01-20',
                        criteriaCount: 4,
                        criteria: [
                            { id: 1, name: '开启力', requirement: '≤100N', status: 'pass' },
                            { id: 2, name: '关闭密封性', requirement: '8级', status: 'fail' },
                            { id: 3, name: '耐久性', requirement: '≥10000次', status: 'pass' },
                            { id: 4, name: '防腐性能', requirement: '5级', status: 'pass' }
                        ]
                    }
                ],
                
                checkHistory: [
                    {
                        id: 1,
                        checkpointName: '玻璃规格符合性检查',
                        phase: '设计阶段',
                        inspector: '王工程师',
                        checkDate: '2024-01-15 14:30',
                        result: 'passed',
                        score: 95
                    },
                    {
                        id: 2,
                        checkpointName: '抗风压性能验证',
                        phase: '评审阶段',
                        inspector: '刘工程师',
                        checkDate: '2024-01-14 10:15',
                        result: 'passed',
                        score: 92
                    },
                    {
                        id: 3,
                        checkpointName: '结构胶粘接质量检查',
                        phase: '生产准备',
                        inspector: '李工程师',
                        checkDate: '2024-01-12 16:45',
                        result: 'failed',
                        score: 65
                    }
                ],
                
                phaseStats: [
                    { name: '设计阶段', score: 95 },
                    { name: '评审阶段', score: 88 },
                    { name: '生产准备', score: 85 },
                    { name: '安装阶段', score: 90 }
                ],
                
                suggestions: [
                    {
                        id: 1,
                        priority: 'high',
                        title: '加强结构胶施工质量控制',
                        description: '建议制定详细的结构胶施工作业指导书，加强现场质量监督'
                    },
                    {
                        id: 2,
                        priority: 'medium',
                        title: '完善铝框架加工工艺',
                        description: '优化加工设备精度，建立严格的过程检验制度'
                    },
                    {
                        id: 3,
                        priority: 'low',
                        title: '建立供应商质量评价体系',
                        description: '对关键原材料供应商进行定期质量审核和评价'
                    }
                ],
                
                get filteredCheckpoints() {
                    return this.checkpoints.filter(checkpoint => {
                        const matchesSearch = !this.searchTerm || 
                            checkpoint.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                            checkpoint.description.toLowerCase().includes(this.searchTerm.toLowerCase());
                        
                        const matchesPhase = !this.filterPhase || checkpoint.phase === this.filterPhase;
                        const matchesStatus = !this.filterStatus || checkpoint.status === this.filterStatus;
                        
                        return matchesSearch && matchesPhase && matchesStatus;
                    });
                },
                
                getStatusText(status) {
                    const statusTexts = {
                        'pending': '待检查',
                        'passed': '通过',
                        'failed': '不合格',
                        'rectifying': '整改中'
                    };
                    return statusTexts[status] || status;
                },
                
                getStatusColor(status) {
                    const statusColors = {
                        'pending': 'bg-gray-100 text-gray-700',
                        'passed': 'bg-success-100 text-success-700',
                        'failed': 'bg-error-100 text-error-700',
                        'rectifying': 'bg-warning-100 text-warning-700'
                    };
                    return statusColors[status] || 'bg-gray-100 text-gray-700';
                },
                
                getPhaseText(phase) {
                    const phaseTexts = {
                        'design': '设计阶段',
                        'review': '评审阶段',
                        'production': '生产准备',
                        'installation': '安装阶段'
                    };
                    return phaseTexts[phase] || phase;
                },
                
                getPriorityText(priority) {
                    const priorityTexts = {
                        'low': '低',
                        'medium': '中',
                        'high': '高',
                        'critical': '关键'
                    };
                    return priorityTexts[priority] || priority;
                },
                
                getPriorityColor(priority) {
                    const priorityColors = {
                        'low': 'bg-gray-100 text-gray-700',
                        'medium': 'bg-primary-100 text-primary-700',
                        'high': 'bg-warning-100 text-warning-700',
                        'critical': 'bg-error-100 text-error-700'
                    };
                    return priorityColors[priority] || 'bg-gray-100 text-gray-700';
                },
                
                executeCheckpoint(checkpoint) {
                    // 执行检查点
                    console.log('执行检查点:', checkpoint);
                    // 这里会打开检查执行界面
                },
                
                viewCheckpointDetail(checkpoint) {
                    // 查看检查点详情
                    console.log('查看检查点详情:', checkpoint);
                },
                
                rectifyCheckpoint(checkpoint) {
                    // 整改处理
                    console.log('整改处理:', checkpoint);
                    checkpoint.status = 'rectifying';
                },
                
                addCriteria() {
                    this.newCheckpoint.criteria.push({ name: '', requirement: '' });
                },
                
                removeCriteria(index) {
                    this.newCheckpoint.criteria.splice(index, 1);
                },
                
                saveCheckpoint() {
                    const checkpoint = {
                        id: Date.now(),
                        ...this.newCheckpoint,
                        status: 'pending',
                        inspector: '当前用户',
                        criteriaCount: this.newCheckpoint.criteria.length,
                        criteria: this.newCheckpoint.criteria.map((c, i) => ({
                            id: i + 1,
                            ...c,
                            status: null
                        }))
                    };
                    
                    this.checkpoints.unshift(checkpoint);
                    this.showCheckpointModal = false;
                    
                    // 重置表单
                    this.newCheckpoint = {
                        name: '',
                        phase: '',
                        type: '',
                        priority: 'medium',
                        plannedDate: '',
                        description: '',
                        criteria: [{ name: '', requirement: '' }]
                    };
                },
                
                viewRecord(record) {
                    console.log('查看检查记录:', record);
                },
                
                downloadReport(record) {
                    console.log('下载检查报告:', record);
                },
                
                exportReport() {
                    console.log('导出质量报告');
                },
                
                init() {
                    // 初始化图表
                    this.$nextTick(() => {
                        this.initQualityChart();
                    });
                },
                
                initQualityChart() {
                    const ctx = document.getElementById('qualityTrendChart');
                    if (ctx) {
                        new Chart(ctx, {
                            type: 'line',
                            data: {
                                labels: ['第1周', '第2周', '第3周', '第4周'],
                                datasets: [{
                                    label: '通过率',
                                    data: [85, 88, 90, 87.5],
                                    borderColor: '#3b82f6',
                                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                                    tension: 0.4
                                }, {
                                    label: '质量得分',
                                    data: [88, 90, 94, 92.5],
                                    borderColor: '#22c55e',
                                    backgroundColor: 'rgba(34, 197, 94, 0.1)',
                                    tension: 0.4
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                scales: {
                                    y: {
                                        beginAtZero: false,
                                        min: 80,
                                        max: 100
                                    }
                                },
                                plugins: {
                                    legend: {
                                        position: 'top'
                                    }
                                }
                            }
                        });
                    }
                }
            }
        }
    </script>
</body>
</html>