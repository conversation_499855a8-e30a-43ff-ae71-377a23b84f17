<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户协作门户 - PDM系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        [x-cloak] { display: none !important; }
        .font-chinese {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }
        .custom-scrollbar {
            scrollbar-width: thin;
            scrollbar-color: #CBD5E0 #F7FAFC;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe', 
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8'
                        },
                        success: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            500: '#22c55e',
                            600: '#16a34a'
                        },
                        warning: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            500: '#f59e0b',
                            600: '#d97706'
                        },
                        error: {
                            50: '#fef2f2',
                            100: '#fee2e2',
                            500: '#ef4444',
                            600: '#dc2626'
                        },
                        'title-gray': '#1f2937'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 font-chinese">
    <div x-data="clientPortalApp()" x-cloak class="min-h-screen">
        <!-- 顶部导航栏 -->
        <nav class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0">
                            <h1 class="text-xl font-semibold text-title-gray">客户协作门户</h1>
                        </div>
                        <div class="hidden md:block">
                            <div class="ml-10 flex items-baseline space-x-4">
                                <span class="text-sm text-gray-600">项目编号: <span class="font-medium text-primary-600" x-text="currentProject.code"></span></span>
                                <span class="text-sm text-gray-600">|</span>
                                <span class="text-sm text-gray-600">项目名称: <span class="font-medium" x-text="currentProject.name"></span></span>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="flex items-center space-x-2">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <span class="text-sm font-medium text-blue-600">张</span>
                            </div>
                            <span class="text-sm font-medium text-gray-700">张总 (华彩玻璃)</span>
                        </div>
                        <button class="text-gray-400 hover:text-gray-600">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- 主要内容区域 -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- 项目进度总览 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-lg font-semibold text-title-gray">项目进度总览</h2>
                    <div class="flex items-center space-x-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                            设计评审中
                        </span>
                        <span class="text-sm text-gray-500">完成度: 65%</span>
                    </div>
                </div>
                
                <!-- 进度条 -->
                <div class="mb-6">
                    <div class="flex justify-between text-sm text-gray-600 mb-2">
                        <span>项目进度</span>
                        <span>65%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-primary-500 h-2 rounded-full" style="width: 65%"></div>
                    </div>
                </div>

                <!-- 关键阶段 -->
                <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-success-500 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-gray-900">需求收集</div>
                            <div class="text-xs text-gray-500">已完成</div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-success-500 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-gray-900">方案设计</div>
                            <div class="text-xs text-gray-500">已完成</div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-xs font-medium">3</span>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-gray-900">设计评审</div>
                            <div class="text-xs text-primary-600">进行中</div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                            <span class="text-gray-600 text-xs font-medium">4</span>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-gray-500">生产准备</div>
                            <div class="text-xs text-gray-400">待开始</div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                            <span class="text-gray-600 text-xs font-medium">5</span>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-gray-500">交付验收</div>
                            <div class="text-xs text-gray-400">待开始</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 标签导航 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8 px-6">
                        <template x-for="tab in tabs" :key="tab.id">
                            <button 
                                @click="activeTab = tab.id"
                                :class="activeTab === tab.id ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors">
                                <span x-text="tab.name"></span>
                                <span x-show="tab.badge" :class="activeTab === tab.id ? 'bg-primary-100 text-primary-600' : 'bg-gray-100 text-gray-600'" class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" x-text="tab.badge"></span>
                            </button>
                        </template>
                    </nav>
                </div>

                <!-- 设计方案展示 -->
                <div x-show="activeTab === 'designs'" class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 3D效果图 -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-medium text-title-gray">3D效果图</h3>
                            <div class="bg-gray-100 rounded-lg h-64 flex items-center justify-center">
                                <div class="text-center">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    <p class="mt-2 text-sm text-gray-500">钢化玻璃幕墙3D渲染图</p>
                                </div>
                            </div>
                            <div class="flex space-x-2">
                                <button class="flex-1 bg-primary-50 text-primary-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-100">
                                    查看大图
                                </button>
                                <button class="flex-1 bg-gray-50 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-100">
                                    下载图片
                                </button>
                            </div>
                        </div>

                        <!-- 技术规格 -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-medium text-title-gray">主要技术规格</h3>
                            <div class="bg-gray-50 rounded-lg p-4 space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">玻璃类型</span>
                                    <span class="text-sm font-medium">钢化中空玻璃</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">玻璃厚度</span>
                                    <span class="text-sm font-medium">6+12A+6mm</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">总面积</span>
                                    <span class="text-sm font-medium">1,250 m²</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">框架材质</span>
                                    <span class="text-sm font-medium">铝合金6063-T5</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">安装方式</span>
                                    <span class="text-sm font-medium">结构胶粘接</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">抗风压等级</span>
                                    <span class="text-sm font-medium">P4级 (≥3.0kPa)</span>
                                </div>
                            </div>
                            <button class="w-full bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700">
                                下载完整技术规格书
                            </button>
                        </div>
                    </div>

                    <!-- 设计版本历史 -->
                    <div class="mt-8">
                        <h3 class="text-lg font-medium text-title-gray mb-4">设计版本历史</h3>
                        <div class="space-y-4">
                            <template x-for="version in designVersions" :key="version.id">
                                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                    <div class="flex items-center space-x-4">
                                        <div class="flex-shrink-0">
                                            <span :class="version.status === 'current' ? 'bg-primary-100 text-primary-600' : 'bg-gray-100 text-gray-600'" 
                                                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" 
                                                  x-text="version.version"></span>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900" x-text="version.title"></div>
                                            <div class="text-xs text-gray-500">
                                                <span x-text="version.date"></span> · 
                                                <span x-text="version.author"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <template x-if="version.status === 'current'">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 text-success-800">
                                                当前版本
                                            </span>
                                        </template>
                                        <button class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                                            查看详情
                                        </button>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>

                <!-- 反馈评论 -->
                <div x-show="activeTab === 'feedback'" class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- 评论列表 -->
                        <div class="lg:col-span-2 space-y-6">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-medium text-title-gray">评论与反馈</h3>
                                <button @click="showFeedbackModal = true" class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700">
                                    添加反馈
                                </button>
                            </div>

                            <div class="space-y-4">
                                <template x-for="comment in comments" :key="comment.id">
                                    <div class="bg-white border border-gray-200 rounded-lg p-4">
                                        <div class="flex items-start space-x-3">
                                            <div class="flex-shrink-0">
                                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                                    <span class="text-sm font-medium text-blue-600" x-text="comment.author.charAt(0)"></span>
                                                </div>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <div class="flex items-center justify-between">
                                                    <p class="text-sm font-medium text-gray-900" x-text="comment.author"></p>
                                                    <div class="flex items-center space-x-2">
                                                        <span :class="comment.type === 'approve' ? 'bg-success-100 text-success-700' : comment.type === 'request' ? 'bg-warning-100 text-warning-700' : 'bg-gray-100 text-gray-700'" 
                                                              class="inline-flex items-center px-2 py-1 rounded text-xs font-medium" 
                                                              x-text="comment.typeLabel"></span>
                                                        <p class="text-xs text-gray-500" x-text="comment.date"></p>
                                                    </div>
                                                </div>
                                                <p class="mt-1 text-sm text-gray-700" x-text="comment.content"></p>
                                                <template x-if="comment.attachments && comment.attachments.length > 0">
                                                    <div class="mt-2">
                                                        <template x-for="attachment in comment.attachments" :key="attachment.name">
                                                            <a href="#" class="inline-flex items-center space-x-1 text-xs text-primary-600 hover:text-primary-700">
                                                                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                                    <path fill-rule="evenodd" d="M8 4a3 3 0 00-3 3v4a5 5 0 0010 0V7a1 1 0 112 0v4a7 7 0 11-14 0V7a5 5 0 0110 0v4a3 3 0 11-6 0V7a1 1 0 012 0v4a1 1 0 102 0V7a3 3 0 00-3-3z" clip-rule="evenodd"></path>
                                                                </svg>
                                                                <span x-text="attachment.name"></span>
                                                            </a>
                                                        </template>
                                                    </div>
                                                </template>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>

                        <!-- 反馈统计 -->
                        <div class="space-y-6">
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h4 class="text-sm font-medium text-gray-900 mb-3">反馈统计</h4>
                                <div class="space-y-3">
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">总反馈数</span>
                                        <span class="text-sm font-medium">12</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">待处理</span>
                                        <span class="text-sm font-medium text-warning-600">3</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">已批准</span>
                                        <span class="text-sm font-medium text-success-600">8</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">需修改</span>
                                        <span class="text-sm font-medium text-error-600">1</span>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-gray-50 rounded-lg p-4">
                                <h4 class="text-sm font-medium text-gray-900 mb-3">关键节点</h4>
                                <div class="space-y-3">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-2 h-2 bg-success-500 rounded-full"></div>
                                        <span class="text-sm text-gray-700">初版设计完成</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-2 h-2 bg-primary-500 rounded-full"></div>
                                        <span class="text-sm text-gray-700">客户反馈收集中</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
                                        <span class="text-sm text-gray-500">最终确认</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 文档资料 -->
                <div x-show="activeTab === 'documents'" class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                        <!-- 文档分类 -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-gray-900 mb-3">文档分类</h4>
                            <div class="space-y-2">
                                <template x-for="category in documentCategories" :key="category.id">
                                    <button @click="selectedDocCategory = category.id" 
                                            :class="selectedDocCategory === category.id ? 'bg-primary-100 text-primary-700' : 'text-gray-600 hover:text-gray-900'"
                                            class="w-full text-left px-3 py-2 rounded-md text-sm transition-colors">
                                        <div class="flex items-center justify-between">
                                            <span x-text="category.name"></span>
                                            <span class="text-xs" x-text="category.count"></span>
                                        </div>
                                    </button>
                                </template>
                            </div>
                        </div>

                        <!-- 文档列表 -->
                        <div class="lg:col-span-3">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-medium text-title-gray">项目文档</h3>
                                <div class="flex space-x-2">
                                    <button class="text-gray-400 hover:text-gray-600">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                        </svg>
                                    </button>
                                    <button class="text-gray-400 hover:text-gray-600">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <template x-for="doc in filteredDocuments" :key="doc.id">
                                    <div class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow">
                                        <div class="flex items-start space-x-3">
                                            <div class="flex-shrink-0">
                                                <div :class="doc.type === 'pdf' ? 'bg-red-100 text-red-600' : doc.type === 'dwg' ? 'bg-blue-100 text-blue-600' : 'bg-green-100 text-green-600'" 
                                                     class="w-10 h-10 rounded-lg flex items-center justify-center">
                                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <h4 class="text-sm font-medium text-gray-900 truncate" x-text="doc.name"></h4>
                                                <p class="text-xs text-gray-500 mt-1" x-text="doc.description"></p>
                                                <div class="flex items-center justify-between mt-2">
                                                    <span class="text-xs text-gray-400" x-text="doc.size"></span>
                                                    <span class="text-xs text-gray-400" x-text="doc.date"></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mt-3 flex space-x-2">
                                            <button class="flex-1 text-primary-600 hover:text-primary-700 text-xs font-medium">
                                                预览
                                            </button>
                                            <button class="flex-1 text-gray-600 hover:text-gray-700 text-xs font-medium">
                                                下载
                                            </button>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 沟通记录 -->
                <div x-show="activeTab === 'communications'" class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- 沟通时间线 -->
                        <div class="lg:col-span-2">
                            <h3 class="text-lg font-medium text-title-gray mb-4">沟通时间线</h3>
                            <div class="flow-root">
                                <ul class="-mb-8">
                                    <template x-for="(comm, index) in communications" :key="comm.id">
                                        <li>
                                            <div class="relative pb-8" :class="index === communications.length - 1 ? '' : 'pb-8'">
                                                <template x-if="index !== communications.length - 1">
                                                    <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                                                </template>
                                                <div class="relative flex space-x-3">
                                                    <div>
                                                        <span :class="comm.type === 'meeting' ? 'bg-blue-500' : comm.type === 'call' ? 'bg-green-500' : 'bg-gray-500'" 
                                                              class="h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white">
                                                            <template x-if="comm.type === 'meeting'">
                                                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                                                </svg>
                                                            </template>
                                                            <template x-if="comm.type === 'call'">
                                                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                                                </svg>
                                                            </template>
                                                            <template x-if="comm.type === 'email'">
                                                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                                                </svg>
                                                            </template>
                                                        </span>
                                                    </div>
                                                    <div class="min-w-0 flex-1 pt-1.5">
                                                        <div>
                                                            <p class="text-sm text-gray-500">
                                                                <span class="font-medium text-gray-900" x-text="comm.title"></span>
                                                                <span class="ml-2" x-text="comm.date"></span>
                                                            </p>
                                                        </div>
                                                        <div class="mt-2 text-sm text-gray-700">
                                                            <p x-text="comm.summary"></p>
                                                        </div>
                                                        <template x-if="comm.participants">
                                                            <div class="mt-2">
                                                                <p class="text-xs text-gray-500">
                                                                    参与人员: <span x-text="comm.participants"></span>
                                                                </p>
                                                            </div>
                                                        </template>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                    </template>
                                </ul>
                            </div>
                        </div>

                        <!-- 联系信息 -->
                        <div class="space-y-6">
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h4 class="text-sm font-medium text-gray-900 mb-3">项目联系人</h4>
                                <div class="space-y-3">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                            <span class="text-sm font-medium text-blue-600">王</span>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">王工程师</div>
                                            <div class="text-xs text-gray-500">设计负责人</div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                            <span class="text-sm font-medium text-green-600">李</span>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">李经理</div>
                                            <div class="text-xs text-gray-500">项目经理</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-gray-50 rounded-lg p-4">
                                <h4 class="text-sm font-medium text-gray-900 mb-3">快速联系</h4>
                                <div class="space-y-2">
                                    <button class="w-full bg-blue-500 text-white px-3 py-2 rounded-md text-sm hover:bg-blue-600">
                                        发起视频会议
                                    </button>
                                    <button class="w-full bg-green-500 text-white px-3 py-2 rounded-md text-sm hover:bg-green-600">
                                        预约现场会议
                                    </button>
                                    <button class="w-full bg-gray-500 text-white px-3 py-2 rounded-md text-sm hover:bg-gray-600">
                                        发送邮件
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 反馈模态框 -->
        <div x-show="showFeedbackModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" style="display: none;">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-title-gray">添加反馈</h3>
                    <button @click="showFeedbackModal = false" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <form @submit.prevent="submitFeedback">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">反馈类型</label>
                            <select x-model="newFeedback.type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                <option value="">请选择反馈类型</option>
                                <option value="approve">设计批准</option>
                                <option value="request">修改建议</option>
                                <option value="question">技术咨询</option>
                                <option value="comment">一般评论</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">反馈标题</label>
                            <input type="text" x-model="newFeedback.title" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500" 
                                   placeholder="请输入反馈标题">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">详细内容</label>
                            <textarea x-model="newFeedback.content" rows="4" 
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500" 
                                      placeholder="请详细描述您的反馈内容..."></textarea>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">优先级</label>
                            <select x-model="newFeedback.priority" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                <option value="low">低</option>
                                <option value="medium">中</option>
                                <option value="high">高</option>
                                <option value="urgent">紧急</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">附件</label>
                            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                                <div class="space-y-1 text-center">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </svg>
                                    <div class="flex text-sm text-gray-600">
                                        <label class="relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500">
                                            <span>上传文件</span>
                                            <input type="file" class="sr-only" multiple>
                                        </label>
                                        <p class="pl-1">或拖拽到此处</p>
                                    </div>
                                    <p class="text-xs text-gray-500">PNG, JPG, PDF 文件大小不超过 10MB</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" @click="showFeedbackModal = false" 
                                class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                            取消
                        </button>
                        <button type="submit" 
                                class="px-4 py-2 bg-primary-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-primary-700">
                            提交反馈
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function clientPortalApp() {
            return {
                activeTab: 'designs',
                showFeedbackModal: false,
                selectedDocCategory: 'all',
                
                currentProject: {
                    code: 'GCP-2024-001',
                    name: '华彩大厦玻璃幕墙项目'
                },
                
                tabs: [
                    { id: 'designs', name: '设计方案', badge: null },
                    { id: 'feedback', name: '反馈评论', badge: '3' },
                    { id: 'documents', name: '文档资料', badge: '12' },
                    { id: 'communications', name: '沟通记录', badge: null }
                ],
                
                designVersions: [
                    {
                        id: 1,
                        version: 'V3.0',
                        title: '优化后的幕墙设计方案',
                        date: '2024-01-15',
                        author: '王工程师',
                        status: 'current'
                    },
                    {
                        id: 2,
                        version: 'V2.1',
                        title: '调整玻璃规格和框架',
                        date: '2024-01-10',
                        author: '王工程师',
                        status: 'history'
                    },
                    {
                        id: 3,
                        version: 'V2.0',
                        title: '结构优化设计',
                        date: '2024-01-05',
                        author: '李设计师',
                        status: 'history'
                    }
                ],
                
                comments: [
                    {
                        id: 1,
                        author: '张总 (华彩玻璃)',
                        date: '2024-01-16 14:30',
                        type: 'approve',
                        typeLabel: '批准',
                        content: '整体设计方案非常不错，特别是结构胶粘接方案很合理。建议在安装细节上再优化一下。'
                    },
                    {
                        id: 2,
                        author: '李工 (华彩玻璃)',
                        date: '2024-01-16 10:15',
                        type: 'request',
                        typeLabel: '建议修改',
                        content: '玻璃厚度建议调整为8+12A+8mm，提高保温性能。另外希望能提供更详细的安装节点图。',
                        attachments: [
                            { name: '节点图参考.pdf', size: '2.1MB' }
                        ]
                    },
                    {
                        id: 3,
                        author: '王经理 (华彩玻璃)',
                        date: '2024-01-15 16:45',
                        type: 'comment',
                        typeLabel: '评论',
                        content: '设计很专业，3D效果图很直观。希望能增加防火等级说明和相关证书。'
                    }
                ],
                
                documentCategories: [
                    { id: 'all', name: '全部文档', count: 12 },
                    { id: 'design', name: '设计图纸', count: 5 },
                    { id: 'spec', name: '技术规格', count: 3 },
                    { id: 'cert', name: '认证资料', count: 2 },
                    { id: 'contract', name: '合同文件', count: 2 }
                ],
                
                documents: [
                    {
                        id: 1,
                        name: '幕墙设计总图',
                        description: '包含立面图、剖面图和节点详图',
                        type: 'dwg',
                        size: '15.2MB',
                        date: '2024-01-15',
                        category: 'design'
                    },
                    {
                        id: 2,
                        name: '玻璃技术规格书',
                        description: '详细的玻璃性能参数和检测报告',
                        type: 'pdf',
                        size: '3.8MB',
                        date: '2024-01-14',
                        category: 'spec'
                    },
                    {
                        id: 3,
                        name: '结构计算书',
                        description: '风荷载和地震作用下的结构分析',
                        type: 'pdf',
                        size: '8.5MB',
                        date: '2024-01-12',
                        category: 'spec'
                    },
                    {
                        id: 4,
                        name: '3D效果图集',
                        description: '多角度渲染效果图',
                        type: 'zip',
                        size: '25.6MB',
                        date: '2024-01-10',
                        category: 'design'
                    },
                    {
                        id: 5,
                        name: '玻璃3C认证证书',
                        description: '钢化玻璃安全认证',
                        type: 'pdf',
                        size: '1.2MB',
                        date: '2024-01-08',
                        category: 'cert'
                    },
                    {
                        id: 6,
                        name: '项目合同',
                        description: '设计服务合同',
                        type: 'pdf',
                        size: '2.3MB',
                        date: '2024-01-05',
                        category: 'contract'
                    }
                ],
                
                communications: [
                    {
                        id: 1,
                        type: 'meeting',
                        title: '设计方案讨论会',
                        date: '2024-01-16 14:00',
                        summary: '与客户深入讨论了V3.0设计方案，重点讨论了玻璃规格、安装工艺和防火要求。客户总体满意，提出了一些优化建议。',
                        participants: '张总、李工、王工程师、李经理'
                    },
                    {
                        id: 2,
                        type: 'call',
                        title: '技术规格确认电话',
                        date: '2024-01-14 10:30',
                        summary: '电话沟通玻璃厚度调整事宜，确认了新的技术参数和性能要求。',
                        participants: '李工、王工程师'
                    },
                    {
                        id: 3,
                        type: 'email',
                        title: '3D效果图确认邮件',
                        date: '2024-01-12 09:15',
                        summary: '发送了最新的3D渲染图，客户确认效果满意，要求提供高清版本用于内部展示。'
                    },
                    {
                        id: 4,
                        type: 'meeting',
                        title: '项目启动会议',
                        date: '2024-01-05 14:00',
                        summary: '项目正式启动，明确了设计要求、时间节点和交付标准。确立了项目团队和沟通机制。',
                        participants: '张总、王经理、李经理、王工程师、李设计师'
                    }
                ],
                
                newFeedback: {
                    type: '',
                    title: '',
                    content: '',
                    priority: 'medium'
                },
                
                get filteredDocuments() {
                    if (this.selectedDocCategory === 'all') {
                        return this.documents;
                    }
                    return this.documents.filter(doc => doc.category === this.selectedDocCategory);
                },
                
                submitFeedback() {
                    // 这里会提交反馈到后端
                    console.log('提交反馈:', this.newFeedback);
                    
                    // 添加到评论列表
                    this.comments.unshift({
                        id: Date.now(),
                        author: '张总 (华彩玻璃)',
                        date: new Date().toLocaleString('zh-CN'),
                        type: this.newFeedback.type,
                        typeLabel: this.getTypeLabel(this.newFeedback.type),
                        content: this.newFeedback.content
                    });
                    
                    // 重置表单
                    this.newFeedback = {
                        type: '',
                        title: '',
                        content: '',
                        priority: 'medium'
                    };
                    
                    this.showFeedbackModal = false;
                },
                
                getTypeLabel(type) {
                    const labels = {
                        'approve': '批准',
                        'request': '建议修改',
                        'question': '技术咨询',
                        'comment': '评论'
                    };
                    return labels[type] || '评论';
                }
            }
        }
    </script>
</body>
</html>