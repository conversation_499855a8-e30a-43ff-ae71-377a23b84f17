<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资源规划与分配 - 玻璃深加工ERP系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1890FF',
                        success: '#52C41A',
                        warning: '#FAAD14',
                        error: '#F5222D',
                        'title-gray': '#262626',
                        'body-gray': '#595959',
                        'aux-gray': '#8C8C8C',
                        'disabled-gray': '#BFBFBF',
                        'border-gray': '#D9D9D9',
                        'bg-gray': '#FAFAFA'
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', 'sans-serif']
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-bg-gray font-chinese" x-data="resourcePlanningApp()">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-border-gray h-16">
        <div class="flex items-center justify-between px-6 h-full">
            <div class="flex items-center space-x-4">
                <button @click="goBack()" class="text-aux-gray hover:text-title-gray transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                </button>
                <div>
                    <h1 class="text-xl font-medium text-title-gray">资源规划与分配</h1>
                    <div class="flex items-center space-x-2 mt-1">
                        <span class="text-sm text-aux-gray">设计团队资源管理</span>
                        <span class="text-sm text-aux-gray">|</span>
                        <span class="text-sm text-aux-gray" x-text="currentPeriod"></span>
                    </div>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <!-- 时间范围选择 -->
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-aux-gray">时间范围:</span>
                    <select x-model="selectedPeriod" @change="updatePeriod()"
                            class="px-3 py-1 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                        <option value="week">本周</option>
                        <option value="month">本月</option>
                        <option value="quarter">本季度</option>
                    </select>
                </div>
                
                <!-- 视图切换 -->
                <div class="flex items-center space-x-1 bg-bg-gray rounded-lg p-1">
                    <template x-for="view in viewModes" :key="view.id">
                        <button @click="activeView = view.id"
                                class="px-3 py-1 text-sm rounded-md transition-colors"
                                :class="activeView === view.id ? 'bg-white text-primary shadow-sm' : 'text-aux-gray hover:text-body-gray'"
                                x-text="view.name"></button>
                    </template>
                </div>
                
                <!-- 操作按钮 -->
                <div class="flex space-x-2">
                    <button @click="showAssignModal = true"
                            class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600 transition-colors">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        分配任务
                    </button>
                    <button @click="exportReport()"
                            class="px-4 py-2 bg-success text-white rounded-md hover:bg-green-600 transition-colors">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        导出报告
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main class="flex h-full">
        <!-- 团队概览视图 -->
        <div x-show="activeView === 'overview'" class="flex-1 p-6 space-y-6">
            <!-- 关键指标卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <template x-for="metric in keyMetrics" :key="metric.id">
                    <div class="bg-white rounded-lg shadow-sm border border-border-gray p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-aux-gray" x-text="metric.label"></p>
                                <p class="text-2xl font-bold text-title-gray mt-2" x-text="metric.value"></p>
                                <div class="flex items-center mt-2">
                                    <span class="text-sm" 
                                          :class="metric.trend >= 0 ? 'text-success' : 'text-error'"
                                          x-text="metric.trend >= 0 ? `+${metric.trend}%` : `${metric.trend}%`"></span>
                                    <span class="text-sm text-aux-gray ml-1">vs 上期</span>
                                </div>
                            </div>
                            <div class="w-12 h-12 rounded-lg flex items-center justify-center"
                                 :class="metric.bgColor">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="metric.icon"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </template>
            </div>

            <!-- 图表分析 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 工作负载分布 -->
                <div class="bg-white rounded-lg shadow-sm border border-border-gray p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-title-gray">团队工作负载分布</h3>
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-error rounded-full"></div>
                            <span class="text-xs text-aux-gray">超负荷</span>
                            <div class="w-3 h-3 bg-warning rounded-full"></div>
                            <span class="text-xs text-aux-gray">高负荷</span>
                            <div class="w-3 h-3 bg-success rounded-full"></div>
                            <span class="text-xs text-aux-gray">正常</span>
                        </div>
                    </div>
                    <canvas id="workloadChart" width="400" height="300"></canvas>
                </div>

                <!-- 项目进度概览 -->
                <div class="bg-white rounded-lg shadow-sm border border-border-gray p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-title-gray">项目进度概览</h3>
                        <span class="text-sm text-aux-gray">本月数据</span>
                    </div>
                    <canvas id="progressChart" width="400" height="300"></canvas>
                </div>
            </div>

            <!-- 团队成员状态 -->
            <div class="bg-white rounded-lg shadow-sm border border-border-gray">
                <div class="px-6 py-4 border-b border-border-gray">
                    <h3 class="text-lg font-medium text-title-gray">团队成员状态</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <template x-for="member in teamMembers" :key="member.id">
                            <div class="border border-border-gray rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                            <span class="text-sm font-medium text-primary" x-text="member.name.charAt(0)"></span>
                                        </div>
                                        <div>
                                            <h4 class="text-sm font-medium text-title-gray" x-text="member.name"></h4>
                                            <p class="text-xs text-aux-gray" x-text="member.role"></p>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-2 h-2 rounded-full" :class="member.status === 'online' ? 'bg-success' : 'bg-gray-300'"></div>
                                        <span class="text-xs text-aux-gray" x-text="member.status === 'online' ? '在线' : '离线'"></span>
                                    </div>
                                </div>
                                
                                <!-- 工作负载进度条 -->
                                <div class="mb-3">
                                    <div class="flex items-center justify-between mb-1">
                                        <span class="text-xs text-aux-gray">工作负载</span>
                                        <span class="text-xs font-medium" 
                                              :class="getWorkloadColor(member.workload)"
                                              x-text="`${member.workload}%`"></span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="h-2 rounded-full transition-all"
                                             :class="getWorkloadProgressColor(member.workload)"
                                             :style="`width: ${member.workload}%`"></div>
                                    </div>
                                </div>
                                
                                <!-- 当前任务 -->
                                <div class="space-y-2">
                                    <div class="flex items-center justify-between">
                                        <span class="text-xs text-aux-gray">当前任务</span>
                                        <span class="text-xs text-body-gray" x-text="`${member.currentTasks}个`"></span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-xs text-aux-gray">本周工时</span>
                                        <span class="text-xs text-body-gray" x-text="`${member.weeklyHours}h`"></span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-xs text-aux-gray">专业领域</span>
                                        <span class="text-xs text-body-gray" x-text="member.specialty"></span>
                                    </div>
                                </div>
                                
                                <!-- 快速操作 -->
                                <div class="mt-3 pt-3 border-t border-border-gray">
                                    <div class="flex space-x-2">
                                        <button @click="assignTask(member)" 
                                                class="flex-1 px-3 py-1 text-xs bg-primary text-white rounded hover:bg-blue-600">
                                            分配任务
                                        </button>
                                        <button @click="viewDetails(member)"
                                                class="flex-1 px-3 py-1 text-xs bg-gray-100 text-body-gray rounded hover:bg-gray-200">
                                            查看详情
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>

        <!-- 甘特图视图 -->
        <div x-show="activeView === 'gantt'" class="flex-1 p-6">
            <div class="bg-white rounded-lg shadow-sm border border-border-gray">
                <div class="px-6 py-4 border-b border-border-gray">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-title-gray">项目时间线</h3>
                        <div class="flex items-center space-x-3">
                            <div class="flex items-center space-x-2 text-sm">
                                <div class="w-3 h-3 bg-blue-500 rounded"></div>
                                <span class="text-aux-gray">设计阶段</span>
                                <div class="w-3 h-3 bg-green-500 rounded"></div>
                                <span class="text-aux-gray">评审阶段</span>
                                <div class="w-3 h-3 bg-purple-500 rounded"></div>
                                <span class="text-aux-gray">完成</span>
                            </div>
                            <button @click="refreshGantt()" class="text-aux-gray hover:text-title-gray">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <!-- 甘特图表头 -->
                    <div class="grid grid-cols-12 gap-2 mb-4">
                        <div class="col-span-3 text-sm font-medium text-aux-gray">项目/任务</div>
                        <div class="col-span-2 text-sm font-medium text-aux-gray">负责人</div>
                        <div class="col-span-7 grid grid-cols-7 gap-1">
                            <template x-for="day in weekDays" :key="day.date">
                                <div class="text-center">
                                    <div class="text-xs text-aux-gray" x-text="day.day"></div>
                                    <div class="text-xs text-body-gray" x-text="day.date"></div>
                                </div>
                            </template>
                        </div>
                    </div>
                    
                    <!-- 甘特图内容 -->
                    <div class="space-y-2">
                        <template x-for="project in ganttProjects" :key="project.id">
                            <div>
                                <!-- 项目行 -->
                                <div class="grid grid-cols-12 gap-2 py-2 border-b border-gray-100">
                                    <div class="col-span-3">
                                        <div class="flex items-center space-x-2">
                                            <button @click="toggleProject(project)" class="text-aux-gray hover:text-title-gray">
                                                <svg class="w-4 h-4 transition-transform" 
                                                     :class="project.expanded ? 'rotate-90' : ''"
                                                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                                </svg>
                                            </button>
                                            <span class="text-sm font-medium text-title-gray" x-text="project.name"></span>
                                        </div>
                                    </div>
                                    <div class="col-span-2 text-sm text-body-gray" x-text="project.manager"></div>
                                    <div class="col-span-7 grid grid-cols-7 gap-1">
                                        <template x-for="(day, index) in weekDays" :key="day.date">
                                            <div class="h-6 relative">
                                                <div x-show="isInProjectRange(project, day.date)"
                                                     class="absolute inset-0 bg-blue-200 rounded opacity-50"></div>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                                
                                <!-- 任务行 -->
                                <div x-show="project.expanded" class="ml-6 space-y-1">
                                    <template x-for="task in project.tasks" :key="task.id">
                                        <div class="grid grid-cols-12 gap-2 py-1">
                                            <div class="col-span-3 text-sm text-body-gray" x-text="task.name"></div>
                                            <div class="col-span-2 text-sm text-aux-gray" x-text="task.assignee"></div>
                                            <div class="col-span-7 grid grid-cols-7 gap-1">
                                                <template x-for="(day, index) in weekDays" :key="day.date">
                                                    <div class="h-4 relative">
                                                        <div x-show="isInTaskRange(task, day.date)"
                                                             class="absolute inset-0 rounded"
                                                             :class="getTaskStatusColor(task.status)"></div>
                                                    </div>
                                                </template>
                                            </div>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>

        <!-- 容量规划视图 -->
        <div x-show="activeView === 'capacity'" class="flex-1 p-6 space-y-6">
            <!-- 容量概览 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white rounded-lg shadow-sm border border-border-gray p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-sm font-medium text-title-gray">总体容量</h3>
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="space-y-2">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-aux-gray">总工时</span>
                            <span class="text-sm font-medium text-title-gray">1,680h</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-aux-gray">已分配</span>
                            <span class="text-sm font-medium text-title-gray">1,280h</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-aux-gray">剩余容量</span>
                            <span class="text-sm font-medium text-success">400h</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2 mt-3">
                            <div class="bg-primary h-2 rounded-full" style="width: 76%"></div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm border border-border-gray p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-sm font-medium text-title-gray">专业分布</h3>
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="space-y-2">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-aux-gray">结构设计</span>
                            <span class="text-sm font-medium text-title-gray">4人</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-aux-gray">工艺设计</span>
                            <span class="text-sm font-medium text-title-gray">3人</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-aux-gray">质量管理</span>
                            <span class="text-sm font-medium text-title-gray">2人</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-aux-gray">测试验证</span>
                            <span class="text-sm font-medium text-title-gray">2人</span>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm border border-border-gray p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-sm font-medium text-title-gray">项目优先级</h3>
                        <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="space-y-2">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-aux-gray">紧急项目</span>
                            <span class="text-sm font-medium text-error">2个</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-aux-gray">高优先级</span>
                            <span class="text-sm font-medium text-warning">5个</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-aux-gray">普通项目</span>
                            <span class="text-sm font-medium text-title-gray">8个</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-aux-gray">低优先级</span>
                            <span class="text-sm font-medium text-aux-gray">3个</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 容量规划表格 -->
            <div class="bg-white rounded-lg shadow-sm border border-border-gray">
                <div class="px-6 py-4 border-b border-border-gray">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-title-gray">人员容量规划</h3>
                        <div class="flex items-center space-x-2">
                            <button @click="optimizeAllocation()"
                                    class="px-3 py-1 text-sm bg-primary text-white rounded hover:bg-blue-600">
                                自动优化
                            </button>
                            <button @click="showCapacitySettings = true"
                                    class="px-3 py-1 text-sm bg-gray-100 text-body-gray rounded hover:bg-gray-200">
                                设置
                            </button>
                        </div>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead class="bg-bg-gray">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">成员</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">专业</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">标准工时</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">已分配</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">利用率</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">剩余容量</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">状态</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-border-gray">
                            <template x-for="member in capacityData" :key="member.id">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                                <span class="text-xs font-medium text-primary" x-text="member.name.charAt(0)"></span>
                                            </div>
                                            <div>
                                                <div class="text-sm font-medium text-title-gray" x-text="member.name"></div>
                                                <div class="text-xs text-aux-gray" x-text="member.role"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-body-gray" x-text="member.specialty"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-body-gray" x-text="`${member.standardHours}h`"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-body-gray" x-text="`${member.assignedHours}h`"></td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                                <div class="h-2 rounded-full"
                                                     :class="getUtilizationColor(member.utilization)"
                                                     :style="`width: ${member.utilization}%`"></div>
                                            </div>
                                            <span class="text-sm font-medium"
                                                  :class="getUtilizationTextColor(member.utilization)"
                                                  x-text="`${member.utilization}%`"></span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-body-gray" x-text="`${member.remainingHours}h`"></td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs rounded-full"
                                              :class="getCapacityStatusClass(member.status)"
                                              x-text="getCapacityStatusText(member.status)"></span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <div class="flex space-x-2">
                                            <button @click="adjustCapacity(member)" class="text-primary hover:text-blue-600">调整</button>
                                            <button @click="viewSchedule(member)" class="text-success hover:text-green-600">排期</button>
                                            <button @click="viewTasks(member)" class="text-warning hover:text-yellow-600">任务</button>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- 任务分配模态框 -->
    <div x-show="showAssignModal" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
         @click="showAssignModal = false">
        <div class="relative top-20 mx-auto p-5 border max-w-3xl shadow-lg rounded-md bg-white" @click.stop>
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-title-gray">任务分配</h3>
                <button @click="showAssignModal = false" class="text-aux-gray hover:text-title-gray">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            
            <form @submit.prevent="assignTask()" class="space-y-6">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-title-gray mb-2">任务名称</label>
                        <input type="text" x-model="assignForm.taskName" required
                               class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                               placeholder="请输入任务名称">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-title-gray mb-2">所属项目</label>
                        <select x-model="assignForm.projectId" required
                                class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                            <option value="">请选择项目</option>
                            <template x-for="project in ganttProjects" :key="project.id">
                                <option :value="project.id" x-text="project.name"></option>
                            </template>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-title-gray mb-2">开始时间</label>
                        <input type="date" x-model="assignForm.startDate" required
                               class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-title-gray mb-2">截止时间</label>
                        <input type="date" x-model="assignForm.endDate" required
                               class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-title-gray mb-2">预估工时</label>
                        <input type="number" x-model="assignForm.estimatedHours" min="1" required
                               class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                               placeholder="小时">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-title-gray mb-2">优先级</label>
                        <select x-model="assignForm.priority"
                                class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                            <option value="normal">普通</option>
                            <option value="high">高</option>
                            <option value="urgent">紧急</option>
                        </select>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-title-gray mb-2">任务描述</label>
                    <textarea x-model="assignForm.description" rows="3"
                              class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                              placeholder="请描述任务的具体要求和目标..."></textarea>
                </div>

                <!-- 候选人员列表 -->
                <div>
                    <label class="block text-sm font-medium text-title-gray mb-3">分配给</label>
                    <div class="grid grid-cols-2 gap-3">
                        <template x-for="member in teamMembers" :key="member.id">
                            <label class="flex items-center p-3 border border-border-gray rounded-lg cursor-pointer hover:bg-gray-50"
                                   :class="assignForm.assigneeId === member.id ? 'border-primary bg-blue-50' : ''">
                                <input type="radio" :value="member.id" x-model="assignForm.assigneeId" class="sr-only">
                                <div class="flex items-center space-x-3 flex-1">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <span class="text-xs font-medium text-primary" x-text="member.name.charAt(0)"></span>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-title-gray" x-text="member.name"></div>
                                        <div class="text-xs text-aux-gray" x-text="member.specialty"></div>
                                    </div>
                                    <div class="ml-auto">
                                        <div class="text-xs text-aux-gray">负载</div>
                                        <div class="text-xs font-medium"
                                             :class="getWorkloadColor(member.workload)"
                                             x-text="`${member.workload}%`"></div>
                                    </div>
                                </div>
                            </label>
                        </template>
                    </div>
                </div>

                <!-- 底部按钮 -->
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" @click="showAssignModal = false"
                            class="px-4 py-2 text-sm font-medium text-body-gray bg-white border border-border-gray rounded-md hover:bg-gray-50">
                        取消
                    </button>
                    <button type="submit" :disabled="assigning"
                            class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-blue-600 disabled:bg-disabled-gray">
                        <span x-show="assigning" class="inline-block w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                        <span x-text="assigning ? '分配中...' : '确定分配'"></span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function resourcePlanningApp() {
            return {
                activeView: 'overview',
                selectedPeriod: 'month',
                currentPeriod: '2025年2月',
                showAssignModal: false,
                showCapacitySettings: false,
                assigning: false,
                
                viewModes: [
                    { id: 'overview', name: '团队概览' },
                    { id: 'gantt', name: '时间线' },
                    { id: 'capacity', name: '容量规划' }
                ],
                
                assignForm: {
                    taskName: '',
                    projectId: '',
                    startDate: '',
                    endDate: '',
                    estimatedHours: '',
                    priority: 'normal',
                    description: '',
                    assigneeId: ''
                },
                
                keyMetrics: [
                    {
                        id: 1,
                        label: '团队成员',
                        value: '11人',
                        trend: 0,
                        bgColor: 'bg-blue-500',
                        icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z'
                    },
                    {
                        id: 2,
                        label: '活跃项目',
                        value: '18个',
                        trend: 12.5,
                        bgColor: 'bg-green-500',
                        icon: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
                    },
                    {
                        id: 3,
                        label: '平均负载',
                        value: '76%',
                        trend: -3.2,
                        bgColor: 'bg-yellow-500',
                        icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z'
                    },
                    {
                        id: 4,
                        label: '完成任务',
                        value: '142个',
                        trend: 8.7,
                        bgColor: 'bg-purple-500',
                        icon: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
                    }
                ],
                
                teamMembers: [
                    {
                        id: 1, name: '张工', role: '高级工艺工程师', specialty: '结构设计',
                        status: 'online', workload: 85, currentTasks: 5, weeklyHours: 38
                    },
                    {
                        id: 2, name: '李工', role: '资深设计师', specialty: '结构设计',
                        status: 'online', workload: 72, currentTasks: 4, weeklyHours: 32
                    },
                    {
                        id: 3, name: '王工', role: '工艺工程师', specialty: '工艺设计',
                        status: 'offline', workload: 95, currentTasks: 6, weeklyHours: 42
                    },
                    {
                        id: 4, name: '赵工', role: '质量工程师', specialty: '质量管理',
                        status: 'online', workload: 68, currentTasks: 3, weeklyHours: 30
                    },
                    {
                        id: 5, name: '钱工', role: '测试工程师', specialty: '测试验证',
                        status: 'online', workload: 55, currentTasks: 2, weeklyHours: 24
                    },
                    {
                        id: 6, name: '孙工', role: '技术专家', specialty: '工艺设计',
                        status: 'online', workload: 80, currentTasks: 4, weeklyHours: 36
                    }
                ],
                
                capacityData: [
                    {
                        id: 1, name: '张工', role: '高级工艺工程师', specialty: '结构设计',
                        standardHours: 160, assignedHours: 136, utilization: 85, remainingHours: 24, status: 'high'
                    },
                    {
                        id: 2, name: '李工', role: '资深设计师', specialty: '结构设计',
                        standardHours: 160, assignedHours: 115, utilization: 72, remainingHours: 45, status: 'normal'
                    },
                    {
                        id: 3, name: '王工', role: '工艺工程师', specialty: '工艺设计',
                        standardHours: 160, assignedHours: 152, utilization: 95, remainingHours: 8, status: 'overload'
                    },
                    {
                        id: 4, name: '赵工', role: '质量工程师', specialty: '质量管理',
                        standardHours: 160, assignedHours: 109, utilization: 68, remainingHours: 51, status: 'normal'
                    },
                    {
                        id: 5, name: '钱工', role: '测试工程师', specialty: '测试验证',
                        standardHours: 160, assignedHours: 88, utilization: 55, remainingHours: 72, status: 'low'
                    },
                    {
                        id: 6, name: '孙工', role: '技术专家', specialty: '工艺设计',
                        standardHours: 160, assignedHours: 128, utilization: 80, remainingHours: 32, status: 'normal'
                    }
                ],
                
                weekDays: [
                    { day: '周一', date: '2/10' },
                    { day: '周二', date: '2/11' },
                    { day: '周三', date: '2/12' },
                    { day: '周四', date: '2/13' },
                    { day: '周五', date: '2/14' },
                    { day: '周六', date: '2/15' },
                    { day: '周日', date: '2/16' }
                ],
                
                ganttProjects: [
                    {
                        id: 1,
                        name: '高强度钢化玻璃项目',
                        manager: '张工',
                        startDate: '2025-02-10',
                        endDate: '2025-02-16',
                        expanded: false,
                        tasks: [
                            { id: 11, name: '需求分析', assignee: '李工', startDate: '2025-02-10', endDate: '2025-02-11', status: 'completed' },
                            { id: 12, name: '方案设计', assignee: '王工', startDate: '2025-02-12', endDate: '2025-02-14', status: 'in_progress' },
                            { id: 13, name: '技术评审', assignee: '赵工', startDate: '2025-02-15', endDate: '2025-02-16', status: 'pending' }
                        ]
                    },
                    {
                        id: 2,
                        name: '中空玻璃设计项目',
                        manager: '李工',
                        startDate: '2025-02-11',
                        endDate: '2025-02-15',
                        expanded: false,
                        tasks: [
                            { id: 21, name: '结构设计', assignee: '张工', startDate: '2025-02-11', endDate: '2025-02-13', status: 'in_progress' },
                            { id: 22, name: '工艺验证', assignee: '孙工', startDate: '2025-02-14', endDate: '2025-02-15', status: 'pending' }
                        ]
                    }
                ],
                
                init() {
                    this.$nextTick(() => {
                        this.initCharts();
                    });
                },
                
                initCharts() {
                    // 工作负载分布图
                    const workloadCtx = document.getElementById('workloadChart');
                    if (workloadCtx) {
                        new Chart(workloadCtx, {
                            type: 'doughnut',
                            data: {
                                labels: ['正常', '高负荷', '超负荷'],
                                datasets: [{
                                    data: [3, 2, 1],
                                    backgroundColor: ['#52C41A', '#FAAD14', '#F5222D']
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        position: 'bottom'
                                    }
                                }
                            }
                        });
                    }
                    
                    // 项目进度图
                    const progressCtx = document.getElementById('progressChart');
                    if (progressCtx) {
                        new Chart(progressCtx, {
                            type: 'bar',
                            data: {
                                labels: ['第1周', '第2周', '第3周', '第4周'],
                                datasets: [{
                                    label: '完成任务',
                                    data: [12, 15, 18, 22],
                                    backgroundColor: '#1890FF'
                                }, {
                                    label: '新增任务',
                                    data: [8, 12, 14, 16],
                                    backgroundColor: '#52C41A'
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                scales: {
                                    y: {
                                        beginAtZero: true
                                    }
                                }
                            }
                        });
                    }
                },
                
                updatePeriod() {
                    const periodMap = {
                        'week': '2025年第7周',
                        'month': '2025年2月',
                        'quarter': '2025年第1季度'
                    };
                    this.currentPeriod = periodMap[this.selectedPeriod];
                },
                
                getWorkloadColor(workload) {
                    if (workload >= 90) return 'text-error';
                    if (workload >= 80) return 'text-warning';
                    return 'text-success';
                },
                
                getWorkloadProgressColor(workload) {
                    if (workload >= 90) return 'bg-error';
                    if (workload >= 80) return 'bg-warning';
                    return 'bg-success';
                },
                
                getUtilizationColor(utilization) {
                    if (utilization >= 90) return 'bg-red-500';
                    if (utilization >= 80) return 'bg-yellow-500';
                    return 'bg-green-500';
                },
                
                getUtilizationTextColor(utilization) {
                    if (utilization >= 90) return 'text-error';
                    if (utilization >= 80) return 'text-warning';
                    return 'text-success';
                },
                
                getCapacityStatusText(status) {
                    const statusMap = {
                        'low': '低负荷',
                        'normal': '正常',
                        'high': '高负荷',
                        'overload': '超负荷'
                    };
                    return statusMap[status] || '正常';
                },
                
                getCapacityStatusClass(status) {
                    const classMap = {
                        'low': 'bg-blue-100 text-blue-800',
                        'normal': 'bg-green-100 text-green-800',
                        'high': 'bg-yellow-100 text-yellow-800',
                        'overload': 'bg-red-100 text-red-800'
                    };
                    return classMap[status] || 'bg-green-100 text-green-800';
                },
                
                toggleProject(project) {
                    project.expanded = !project.expanded;
                },
                
                isInProjectRange(project, date) {
                    const dateObj = new Date('2025-' + date.replace('/', '-'));
                    const startDate = new Date(project.startDate);
                    const endDate = new Date(project.endDate);
                    return dateObj >= startDate && dateObj <= endDate;
                },
                
                isInTaskRange(task, date) {
                    const dateObj = new Date('2025-' + date.replace('/', '-'));
                    const startDate = new Date(task.startDate);
                    const endDate = new Date(task.endDate);
                    return dateObj >= startDate && dateObj <= endDate;
                },
                
                getTaskStatusColor(status) {
                    const colorMap = {
                        'completed': 'bg-green-500',
                        'in_progress': 'bg-blue-500',
                        'pending': 'bg-gray-400'
                    };
                    return colorMap[status] || 'bg-gray-400';
                },
                
                assignTask(member) {
                    if (member) {
                        this.assignForm.assigneeId = member.id;
                    }
                    this.showAssignModal = true;
                },
                
                async assignTaskSubmit() {
                    this.assigning = true;
                    try {
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        
                        // 更新成员工作负载
                        const member = this.teamMembers.find(m => m.id === parseInt(this.assignForm.assigneeId));
                        if (member) {
                            member.currentTasks++;
                            member.workload = Math.min(100, member.workload + 15);
                        }
                        
                        // 重置表单
                        this.assignForm = {
                            taskName: '',
                            projectId: '',
                            startDate: '',
                            endDate: '',
                            estimatedHours: '',
                            priority: 'normal',
                            description: '',
                            assigneeId: ''
                        };
                        
                        this.showAssignModal = false;
                        alert('任务分配成功！');
                    } catch (error) {
                        alert('分配失败，请重试');
                    } finally {
                        this.assigning = false;
                    }
                },
                
                viewDetails(member) {
                    alert(`查看 ${member.name} 的详细信息`);
                },
                
                adjustCapacity(member) {
                    alert(`调整 ${member.name} 的容量配置`);
                },
                
                viewSchedule(member) {
                    alert(`查看 ${member.name} 的工作排期`);
                },
                
                viewTasks(member) {
                    alert(`查看 ${member.name} 的任务列表`);
                },
                
                optimizeAllocation() {
                    if (confirm('确定要进行自动容量优化吗？这会重新分配部分任务。')) {
                        alert('容量优化完成！已自动调整3个任务的分配。');
                    }
                },
                
                refreshGantt() {
                    alert('甘特图已刷新');
                },
                
                exportReport() {
                    alert('正在导出资源规划报告...');
                },
                
                goBack() {
                    history.back();
                }
            }
        }
    </script>
</body>
</html>