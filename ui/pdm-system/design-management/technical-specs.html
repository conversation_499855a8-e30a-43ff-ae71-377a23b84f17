<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技术规格管理 - PDM系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        [x-cloak] { display: none !important; }
        .font-chinese {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }
        .custom-scrollbar {
            scrollbar-width: thin;
            scrollbar-color: #CBD5E0 #F7FAFC;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe', 
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8'
                        },
                        success: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            500: '#22c55e',
                            600: '#16a34a'
                        },
                        warning: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            500: '#f59e0b',
                            600: '#d97706'
                        },
                        error: {
                            50: '#fef2f2',
                            100: '#fee2e2',
                            500: '#ef4444',
                            600: '#dc2626'
                        },
                        'title-gray': '#1f2937'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 font-chinese">
    <div x-data="technicalSpecsApp()" x-cloak class="min-h-screen">
        <!-- 顶部导航栏 -->
        <nav class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0">
                            <h1 class="text-xl font-semibold text-title-gray">技术规格管理</h1>
                        </div>
                        <div class="hidden md:block">
                            <div class="ml-10 flex items-baseline space-x-4">
                                <span class="text-sm text-gray-600">项目: <span class="font-medium text-primary-600">华彩大厦玻璃幕墙</span></span>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button @click="showTemplateModal = true" class="bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-700">
                            规格模板
                        </button>
                        <button @click="showSpecModal = true" class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700">
                            新建规格
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- 主要内容区域 -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- 概览统计 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-primary-100 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">技术规格总数</dt>
                                <dd class="text-lg font-medium text-gray-900">24</dd>
                            </dl>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-success-100 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-success-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">已验证规格</dt>
                                <dd class="text-lg font-medium text-gray-900">18</dd>
                            </dl>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-warning-100 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-warning-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">待验证规格</dt>
                                <dd class="text-lg font-medium text-gray-900">6</dd>
                            </dl>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-gray-100 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">规格模板</dt>
                                <dd class="text-lg font-medium text-gray-900">8</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 过滤和搜索 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0 sm:space-x-4">
                    <div class="flex-1">
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                            <input type="text" x-model="searchTerm" 
                                   class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500" 
                                   placeholder="搜索技术规格...">
                        </div>
                    </div>
                    <div class="flex space-x-3">
                        <select x-model="filterCategory" class="block w-full px-3 py-2 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            <option value="">全部分类</option>
                            <option value="glass">玻璃规格</option>
                            <option value="frame">框架规格</option>
                            <option value="sealing">密封规格</option>
                            <option value="hardware">五金规格</option>
                            <option value="performance">性能指标</option>
                            <option value="safety">安全规格</option>
                        </select>
                        <select x-model="filterStatus" class="block w-full px-3 py-2 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            <option value="">全部状态</option>
                            <option value="draft">草稿</option>
                            <option value="pending">待验证</option>
                            <option value="verified">已验证</option>
                            <option value="approved">已批准</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 技术规格列表 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-title-gray">技术规格清单</h3>
                        <div class="flex space-x-2">
                            <button @click="viewMode = 'grid'" :class="viewMode === 'grid' ? 'bg-primary-100 text-primary-600' : 'text-gray-400 hover:text-gray-600'" class="p-2 rounded-md">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                                </svg>
                            </button>
                            <button @click="viewMode = 'list'" :class="viewMode === 'list' ? 'bg-primary-100 text-primary-600' : 'text-gray-400 hover:text-gray-600'" class="p-2 rounded-md">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- 网格视图 -->
                    <div x-show="viewMode === 'grid'" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <template x-for="spec in filteredSpecs" :key="spec.id">
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer" @click="viewSpecDetail(spec)">
                                <div class="flex items-start justify-between mb-3">
                                    <div class="flex-1">
                                        <h4 class="text-sm font-medium text-gray-900 truncate" x-text="spec.name"></h4>
                                        <p class="text-xs text-gray-500 mt-1" x-text="spec.code"></p>
                                    </div>
                                    <span :class="getStatusColor(spec.status)" 
                                          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" 
                                          x-text="getStatusText(spec.status)"></span>
                                </div>
                                
                                <div class="space-y-2 mb-3">
                                    <div class="flex justify-between text-xs">
                                        <span class="text-gray-500">分类</span>
                                        <span class="font-medium" x-text="getCategoryText(spec.category)"></span>
                                    </div>
                                    <div class="flex justify-between text-xs">
                                        <span class="text-gray-500">版本</span>
                                        <span class="font-medium" x-text="spec.version"></span>
                                    </div>
                                    <div class="flex justify-between text-xs">
                                        <span class="text-gray-500">更新时间</span>
                                        <span class="font-medium" x-text="spec.updatedAt"></span>
                                    </div>
                                </div>

                                <!-- 关键参数预览 -->
                                <div class="bg-gray-50 rounded-md p-2 mb-3">
                                    <div class="text-xs text-gray-600 mb-1">关键参数</div>
                                    <template x-for="param in spec.keyParams.slice(0, 2)" :key="param.name">
                                        <div class="flex justify-between text-xs">
                                            <span x-text="param.name"></span>
                                            <span class="font-medium" x-text="param.value + ' ' + param.unit"></span>
                                        </div>
                                    </template>
                                </div>

                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-gray-500">
                                        创建者: <span x-text="spec.author"></span>
                                    </span>
                                    <div class="flex space-x-1">
                                        <button @click.stop="editSpec(spec)" class="text-primary-600 hover:text-primary-700">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                            </svg>
                                        </button>
                                        <button @click.stop="validateSpec(spec)" class="text-success-600 hover:text-success-700">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>

                    <!-- 列表视图 -->
                    <div x-show="viewMode === 'list'" class="overflow-hidden">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规格名称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分类</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">版本</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关键参数</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建者</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <template x-for="spec in filteredSpecs" :key="spec.id">
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900" x-text="spec.name"></div>
                                                <div class="text-sm text-gray-500" x-text="spec.code"></div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="text-sm text-gray-900" x-text="getCategoryText(spec.category)"></span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span :class="getStatusColor(spec.status)" 
                                                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" 
                                                  x-text="getStatusText(spec.status)"></span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="spec.version"></td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">
                                                <template x-for="param in spec.keyParams.slice(0, 1)" :key="param.name">
                                                    <div>
                                                        <span x-text="param.name"></span>: 
                                                        <span class="font-medium" x-text="param.value + ' ' + param.unit"></span>
                                                    </div>
                                                </template>
                                                <template x-if="spec.keyParams.length > 1">
                                                    <div class="text-xs text-gray-500" x-text="'还有 ' + (spec.keyParams.length - 1) + ' 个参数'"></div>
                                                </template>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="spec.author"></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <button @click="viewSpecDetail(spec)" class="text-primary-600 hover:text-primary-700">详情</button>
                                                <button @click="editSpec(spec)" class="text-gray-600 hover:text-gray-700">编辑</button>
                                                <button @click="validateSpec(spec)" class="text-success-600 hover:text-success-700">验证</button>
                                            </div>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 新建/编辑规格模态框 -->
        <div x-show="showSpecModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" style="display: none;">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/4 shadow-lg rounded-md bg-white max-h-screen overflow-y-auto">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-medium text-title-gray">
                        <span x-text="editingSpec ? '编辑技术规格' : '新建技术规格'"></span>
                    </h3>
                    <button @click="closeSpecModal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <form @submit.prevent="saveSpec">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- 基本信息 -->
                        <div class="lg:col-span-1 space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">规格名称 *</label>
                                <input type="text" x-model="currentSpec.name" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">规格编码 *</label>
                                <input type="text" x-model="currentSpec.code" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">分类 *</label>
                                <select x-model="currentSpec.category" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                    <option value="">请选择分类</option>
                                    <option value="glass">玻璃规格</option>
                                    <option value="frame">框架规格</option>
                                    <option value="sealing">密封规格</option>
                                    <option value="hardware">五金规格</option>
                                    <option value="performance">性能指标</option>
                                    <option value="safety">安全规格</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">版本号</label>
                                <input type="text" x-model="currentSpec.version"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">规格描述</label>
                                <textarea x-model="currentSpec.description" rows="3"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"></textarea>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">适用标准</label>
                                <input type="text" x-model="currentSpec.standard"
                                       placeholder="如: GB/T 15763.2-2005"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            </div>
                        </div>

                        <!-- 技术参数 -->
                        <div class="lg:col-span-2">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-md font-medium text-gray-900">技术参数</h4>
                                <button type="button" @click="addParameter" 
                                        class="bg-primary-600 text-white px-3 py-1 rounded-md text-sm hover:bg-primary-700">
                                    添加参数
                                </button>
                            </div>

                            <div class="space-y-3 max-h-96 overflow-y-auto custom-scrollbar">
                                <template x-for="(param, index) in currentSpec.parameters" :key="index">
                                    <div class="grid grid-cols-12 gap-3 items-end bg-gray-50 p-3 rounded-md">
                                        <div class="col-span-3">
                                            <label class="block text-xs font-medium text-gray-700 mb-1">参数名称</label>
                                            <input type="text" x-model="param.name"
                                                   class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                        </div>
                                        <div class="col-span-2">
                                            <label class="block text-xs font-medium text-gray-700 mb-1">数值</label>
                                            <input type="text" x-model="param.value"
                                                   class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                        </div>
                                        <div class="col-span-2">
                                            <label class="block text-xs font-medium text-gray-700 mb-1">单位</label>
                                            <input type="text" x-model="param.unit"
                                                   class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                        </div>
                                        <div class="col-span-2">
                                            <label class="block text-xs font-medium text-gray-700 mb-1">公差</label>
                                            <input type="text" x-model="param.tolerance" placeholder="±0.5"
                                                   class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                        </div>
                                        <div class="col-span-2">
                                            <label class="block text-xs font-medium text-gray-700 mb-1">检测方法</label>
                                            <select x-model="param.testMethod"
                                                    class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                                <option value="">选择方法</option>
                                                <option value="visual">目视检查</option>
                                                <option value="measure">测量检查</option>
                                                <option value="test">试验检测</option>
                                                <option value="calculation">计算验证</option>
                                            </select>
                                        </div>
                                        <div class="col-span-1">
                                            <button type="button" @click="removeParameter(index)" 
                                                    class="w-full px-2 py-1 text-error-600 hover:bg-error-50 rounded text-sm">
                                                <svg class="w-4 h-4 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>

                    <!-- 验证规则 -->
                    <div class="mt-6">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="text-md font-medium text-gray-900">验证规则</h4>
                            <button type="button" @click="addValidationRule" 
                                    class="bg-success-600 text-white px-3 py-1 rounded-md text-sm hover:bg-success-700">
                                添加规则
                            </button>
                        </div>

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                            <template x-for="(rule, index) in currentSpec.validationRules" :key="index">
                                <div class="bg-gray-50 p-3 rounded-md">
                                    <div class="flex items-center justify-between mb-2">
                                        <label class="text-sm font-medium text-gray-700">规则名称</label>
                                        <button type="button" @click="removeValidationRule(index)" 
                                                class="text-error-600 hover:text-error-700">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                        </button>
                                    </div>
                                    <input type="text" x-model="rule.name"
                                           class="w-full px-2 py-1 mb-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">规则表达式</label>
                                    <input type="text" x-model="rule.expression" placeholder="如: value >= 6 && value <= 12"
                                           class="w-full px-2 py-1 mb-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">错误消息</label>
                                    <input type="text" x-model="rule.errorMessage"
                                           class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                </div>
                            </template>
                        </div>
                    </div>

                    <div class="mt-8 flex justify-end space-x-3">
                        <button type="button" @click="closeSpecModal" 
                                class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                            取消
                        </button>
                        <button type="submit" 
                                class="px-4 py-2 bg-primary-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-primary-700">
                            保存规格
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 规格模板模态框 -->
        <div x-show="showTemplateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" style="display: none;">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 shadow-lg rounded-md bg-white">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-medium text-title-gray">规格模板库</h3>
                    <button @click="showTemplateModal = false" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <template x-for="template in specTemplates" :key="template.id">
                        <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                            <div class="flex items-start justify-between mb-3">
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900" x-text="template.name"></h4>
                                    <p class="text-xs text-gray-500 mt-1" x-text="template.description"></p>
                                </div>
                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-primary-100 text-primary-700" 
                                      x-text="getCategoryText(template.category)"></span>
                            </div>
                            
                            <div class="mb-3">
                                <div class="text-xs text-gray-600 mb-1">包含参数: <span x-text="template.parameterCount"></span> 个</div>
                                <div class="text-xs text-gray-500">
                                    适用场景: <span x-text="template.useCase"></span>
                                </div>
                            </div>

                            <button @click="useTemplate(template)" 
                                    class="w-full bg-primary-600 text-white px-3 py-2 rounded-md text-sm hover:bg-primary-700">
                                使用模板
                            </button>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>

    <script>
        function technicalSpecsApp() {
            return {
                searchTerm: '',
                filterCategory: '',
                filterStatus: '',
                viewMode: 'grid',
                showSpecModal: false,
                showTemplateModal: false,
                editingSpec: false,
                
                currentSpec: {
                    name: '',
                    code: '',
                    category: '',
                    version: 'V1.0',
                    description: '',
                    standard: '',
                    parameters: [],
                    validationRules: []
                },
                
                specs: [
                    {
                        id: 1,
                        name: '钢化中空玻璃规格',
                        code: 'GLASS-001',
                        category: 'glass',
                        status: 'verified',
                        version: 'V2.1',
                        author: '王工程师',
                        updatedAt: '2024-01-15',
                        keyParams: [
                            { name: '玻璃厚度', value: '6+12A+6', unit: 'mm' },
                            { name: '可见光透射比', value: '≥80', unit: '%' }
                        ]
                    },
                    {
                        id: 2,
                        name: '铝合金框架规格',
                        code: 'FRAME-001',
                        category: 'frame',
                        status: 'verified',
                        version: 'V1.5',
                        author: '李设计师',
                        updatedAt: '2024-01-14',
                        keyParams: [
                            { name: '型材规格', value: '6063-T5', unit: '' },
                            { name: '壁厚', value: '≥2.0', unit: 'mm' }
                        ]
                    },
                    {
                        id: 3,
                        name: '结构胶密封规格',
                        code: 'SEAL-001',
                        category: 'sealing',
                        status: 'pending',
                        version: 'V1.0',
                        author: '张工程师',
                        updatedAt: '2024-01-13',
                        keyParams: [
                            { name: '胶缝宽度', value: '20±2', unit: 'mm' },
                            { name: '粘接强度', value: '≥0.6', unit: 'MPa' }
                        ]
                    },
                    {
                        id: 4,
                        name: '抗风压性能指标',
                        code: 'PERF-001',
                        category: 'performance',
                        status: 'verified',
                        version: 'V1.2',
                        author: '刘工程师',
                        updatedAt: '2024-01-12',
                        keyParams: [
                            { name: '设计风压', value: '3.0', unit: 'kPa' },
                            { name: '安全系数', value: '2.5', unit: '' }
                        ]
                    },
                    {
                        id: 5,
                        name: '防火安全规格',
                        code: 'SAFE-001',
                        category: 'safety',
                        status: 'approved',
                        version: 'V1.0',
                        author: '王工程师',
                        updatedAt: '2024-01-11',
                        keyParams: [
                            { name: '耐火时间', value: '≥60', unit: 'min' },
                            { name: '防火等级', value: 'A级', unit: '' }
                        ]
                    },
                    {
                        id: 6,
                        name: '五金配件规格',
                        code: 'HARD-001',
                        category: 'hardware',
                        status: 'draft',
                        version: 'V0.8',
                        author: '李设计师',
                        updatedAt: '2024-01-10',
                        keyParams: [
                            { name: '承重能力', value: '≥150', unit: 'kg' },
                            { name: '使用寿命', value: '≥25', unit: '年' }
                        ]
                    }
                ],
                
                specTemplates: [
                    {
                        id: 1,
                        name: '标准钢化玻璃模板',
                        category: 'glass',
                        description: '适用于一般建筑幕墙的钢化玻璃规格',
                        parameterCount: 8,
                        useCase: '商业建筑幕墙'
                    },
                    {
                        id: 2,
                        name: '隔热断桥铝框架模板',
                        category: 'frame',
                        description: '节能型铝合金框架标准规格',
                        parameterCount: 12,
                        useCase: '节能建筑'
                    },
                    {
                        id: 3,
                        name: '结构胶粘接模板',
                        category: 'sealing',
                        description: '结构性胶粘接系统规格',
                        parameterCount: 6,
                        useCase: '隐框幕墙'
                    },
                    {
                        id: 4,
                        name: '抗震性能模板',
                        category: 'performance',
                        description: '抗震设防区域性能要求',
                        parameterCount: 10,
                        useCase: '地震多发区'
                    },
                    {
                        id: 5,
                        name: '防火玻璃模板',
                        category: 'safety',
                        description: '防火等级A级玻璃规格',
                        parameterCount: 7,
                        useCase: '高层建筑'
                    },
                    {
                        id: 6,
                        name: '开启五金模板',
                        category: 'hardware',
                        description: '可开启窗扇五金配件',
                        parameterCount: 9,
                        useCase: '自然通风'
                    }
                ],
                
                get filteredSpecs() {
                    return this.specs.filter(spec => {
                        const matchesSearch = !this.searchTerm || 
                            spec.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                            spec.code.toLowerCase().includes(this.searchTerm.toLowerCase());
                        
                        const matchesCategory = !this.filterCategory || spec.category === this.filterCategory;
                        const matchesStatus = !this.filterStatus || spec.status === this.filterStatus;
                        
                        return matchesSearch && matchesCategory && matchesStatus;
                    });
                },
                
                getStatusText(status) {
                    const statusTexts = {
                        'draft': '草稿',
                        'pending': '待验证',
                        'verified': '已验证',
                        'approved': '已批准'
                    };
                    return statusTexts[status] || status;
                },
                
                getStatusColor(status) {
                    const statusColors = {
                        'draft': 'bg-gray-100 text-gray-700',
                        'pending': 'bg-warning-100 text-warning-700',
                        'verified': 'bg-success-100 text-success-700',
                        'approved': 'bg-primary-100 text-primary-700'
                    };
                    return statusColors[status] || 'bg-gray-100 text-gray-700';
                },
                
                getCategoryText(category) {
                    const categoryTexts = {
                        'glass': '玻璃规格',
                        'frame': '框架规格',
                        'sealing': '密封规格',
                        'hardware': '五金规格',
                        'performance': '性能指标',
                        'safety': '安全规格'
                    };
                    return categoryTexts[category] || category;
                },
                
                viewSpecDetail(spec) {
                    // 查看规格详情
                    console.log('查看规格详情:', spec);
                },
                
                editSpec(spec) {
                    this.editingSpec = true;
                    this.currentSpec = {
                        ...spec,
                        parameters: spec.keyParams.map(param => ({
                            ...param,
                            tolerance: '',
                            testMethod: ''
                        })) || [],
                        validationRules: []
                    };
                    this.showSpecModal = true;
                },
                
                validateSpec(spec) {
                    // 执行规格验证
                    console.log('验证规格:', spec);
                    spec.status = 'verified';
                },
                
                addParameter() {
                    this.currentSpec.parameters.push({
                        name: '',
                        value: '',
                        unit: '',
                        tolerance: '',
                        testMethod: ''
                    });
                },
                
                removeParameter(index) {
                    this.currentSpec.parameters.splice(index, 1);
                },
                
                addValidationRule() {
                    this.currentSpec.validationRules.push({
                        name: '',
                        expression: '',
                        errorMessage: ''
                    });
                },
                
                removeValidationRule(index) {
                    this.currentSpec.validationRules.splice(index, 1);
                },
                
                useTemplate(template) {
                    // 使用模板创建新规格
                    this.currentSpec = {
                        name: template.name.replace('模板', ''),
                        code: '',
                        category: template.category,
                        version: 'V1.0',
                        description: template.description,
                        standard: '',
                        parameters: [],
                        validationRules: []
                    };
                    
                    // 根据模板预填充参数
                    this.addTemplateParameters(template);
                    
                    this.showTemplateModal = false;
                    this.showSpecModal = true;
                    this.editingSpec = false;
                },
                
                addTemplateParameters(template) {
                    // 根据不同类型的模板添加预设参数
                    const templateParams = {
                        'glass': [
                            { name: '玻璃厚度', value: '', unit: 'mm', tolerance: '±0.2', testMethod: 'measure' },
                            { name: '可见光透射比', value: '', unit: '%', tolerance: '±2', testMethod: 'test' },
                            { name: '太阳能透射比', value: '', unit: '%', tolerance: '±2', testMethod: 'test' }
                        ],
                        'frame': [
                            { name: '型材规格', value: '', unit: '', tolerance: '', testMethod: 'visual' },
                            { name: '壁厚', value: '', unit: 'mm', tolerance: '±0.1', testMethod: 'measure' },
                            { name: '表面处理', value: '', unit: '', tolerance: '', testMethod: 'visual' }
                        ],
                        'sealing': [
                            { name: '胶缝宽度', value: '', unit: 'mm', tolerance: '±1', testMethod: 'measure' },
                            { name: '粘接强度', value: '', unit: 'MPa', tolerance: '±0.1', testMethod: 'test' }
                        ]
                    };
                    
                    this.currentSpec.parameters = templateParams[template.category] || [];
                },
                
                saveSpec() {
                    if (this.editingSpec) {
                        // 更新现有规格
                        const index = this.specs.findIndex(s => s.id === this.currentSpec.id);
                        if (index !== -1) {
                            this.specs[index] = {
                                ...this.currentSpec,
                                keyParams: this.currentSpec.parameters.slice(0, 3),
                                updatedAt: new Date().toISOString().split('T')[0]
                            };
                        }
                    } else {
                        // 创建新规格
                        const newSpec = {
                            ...this.currentSpec,
                            id: Date.now(),
                            status: 'draft',
                            author: '当前用户',
                            updatedAt: new Date().toISOString().split('T')[0],
                            keyParams: this.currentSpec.parameters.slice(0, 3)
                        };
                        this.specs.unshift(newSpec);
                    }
                    
                    this.closeSpecModal();
                },
                
                closeSpecModal() {
                    this.showSpecModal = false;
                    this.editingSpec = false;
                    this.currentSpec = {
                        name: '',
                        code: '',
                        category: '',
                        version: 'V1.0',
                        description: '',
                        standard: '',
                        parameters: [],
                        validationRules: []
                    };
                }
            }
        }
    </script>
</body>
</html>