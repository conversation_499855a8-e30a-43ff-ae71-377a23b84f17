<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统集成仪表板 - PDM系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        [x-cloak] { display: none !important; }
        .font-chinese {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }
        .custom-scrollbar {
            scrollbar-width: thin;
            scrollbar-color: #CBD5E0 #F7FAFC;
        }
        .integration-line {
            position: absolute;
            background: linear-gradient(90deg, transparent, #3b82f6, transparent);
            height: 2px;
            animation: flow 3s linear infinite;
        }
        @keyframes flow {
            0% { transform: translateX(-100%); opacity: 0; }
            50% { opacity: 1; }
            100% { transform: translateX(100%); opacity: 0; }
        }
        .module-card {
            transition: all 0.3s ease;
        }
        .module-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 6px;
        }
        .status-online { background-color: #22c55e; }
        .status-warning { background-color: #f59e0b; }
        .status-offline { background-color: #ef4444; }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe', 
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8'
                        },
                        success: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            500: '#22c55e',
                            600: '#16a34a'
                        },
                        warning: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            500: '#f59e0b',
                            600: '#d97706'
                        },
                        error: {
                            50: '#fef2f2',
                            100: '#fee2e2',
                            500: '#ef4444',
                            600: '#dc2626'
                        },
                        'title-gray': '#1f2937'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 font-chinese">
    <div x-data="integrationDashboardApp()" x-cloak class="min-h-screen">
        <!-- 顶部导航栏 -->
        <nav class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0">
                            <h1 class="text-xl font-semibold text-title-gray">系统集成仪表板</h1>
                        </div>
                        <div class="hidden md:block">
                            <div class="ml-10 flex items-baseline space-x-4">
                                <span class="text-sm text-gray-600">总览: <span class="font-medium text-primary-600">玻璃深加工ERP系统</span></span>
                                <span class="text-sm text-gray-600">|</span>
                                <span class="text-sm text-gray-600">在线模块: <span class="font-medium text-success-600">11/12</span></span>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="flex items-center space-x-2">
                            <div class="status-indicator status-online"></div>
                            <span class="text-sm text-gray-700">系统正常运行</span>
                        </div>
                        <button @click="refreshDashboard" class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700">
                            刷新状态
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- 主要内容区域 -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- 系统状态概览 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-success-100 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-success-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">在线模块</dt>
                                <dd class="text-lg font-medium text-gray-900">11/12</dd>
                            </dl>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-primary-100 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">API调用/小时</dt>
                                <dd class="text-lg font-medium text-gray-900">2,847</dd>
                            </dl>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-warning-100 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-warning-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">数据同步延迟</dt>
                                <dd class="text-lg font-medium text-gray-900">1.2s</dd>
                            </dl>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-error-100 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-error-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L5.268 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">错误数/24h</dt>
                                <dd class="text-lg font-medium text-gray-900">3</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统架构图 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-lg font-semibold text-title-gray">系统架构与数据流</h2>
                    <div class="flex space-x-2">
                        <button @click="viewMode = 'architecture'" :class="viewMode === 'architecture' ? 'bg-primary-100 text-primary-600' : 'text-gray-600'" class="px-3 py-1 rounded-md text-sm">
                            架构视图
                        </button>
                        <button @click="viewMode = 'dataflow'" :class="viewMode === 'dataflow' ? 'bg-primary-100 text-primary-600' : 'text-gray-600'" class="px-3 py-1 rounded-md text-sm">
                            数据流视图
                        </button>
                    </div>
                </div>

                <!-- 架构视图 -->
                <div x-show="viewMode === 'architecture'" class="relative">
                    <!-- 核心PDM系统 -->
                    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                        <div class="bg-primary-100 border-2 border-primary-500 rounded-lg p-4 text-center">
                            <div class="text-lg font-semibold text-primary-700">PDM系统</div>
                            <div class="text-sm text-primary-600">产品数据管理</div>
                        </div>
                    </div>

                    <!-- 周围的系统模块 -->
                    <div class="grid grid-cols-4 gap-8 h-96">
                        <!-- 第一行 -->
                        <div class="module-card bg-white border border-gray-200 rounded-lg p-3 text-center cursor-pointer" @click="viewModuleDetail('basic')">
                            <div class="w-8 h-8 bg-blue-100 rounded-md mx-auto mb-2 flex items-center justify-center">
                                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                            </div>
                            <div class="text-xs font-medium text-gray-900">基础管理</div>
                            <div class="status-indicator status-online"></div>
                        </div>

                        <div class="module-card bg-white border border-gray-200 rounded-lg p-3 text-center cursor-pointer" @click="viewModuleDetail('sales')">
                            <div class="w-8 h-8 bg-green-100 rounded-md mx-auto mb-2 flex items-center justify-center">
                                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                </svg>
                            </div>
                            <div class="text-xs font-medium text-gray-900">销售系统</div>
                            <div class="status-indicator status-online"></div>
                        </div>

                        <div class="module-card bg-white border border-gray-200 rounded-lg p-3 text-center cursor-pointer" @click="viewModuleDetail('procurement')">
                            <div class="w-8 h-8 bg-purple-100 rounded-md mx-auto mb-2 flex items-center justify-center">
                                <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v4a2 2 0 01-2 2H9a2 2 0 01-2-2v-4m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path>
                                </svg>
                            </div>
                            <div class="text-xs font-medium text-gray-900">采购系统</div>
                            <div class="status-indicator status-online"></div>
                        </div>

                        <div class="module-card bg-white border border-gray-200 rounded-lg p-3 text-center cursor-pointer" @click="viewModuleDetail('production')">
                            <div class="w-8 h-8 bg-orange-100 rounded-md mx-auto mb-2 flex items-center justify-center">
                                <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </div>
                            <div class="text-xs font-medium text-gray-900">生产系统</div>
                            <div class="status-indicator status-online"></div>
                        </div>

                        <!-- 第二行 -->
                        <div class="module-card bg-white border border-gray-200 rounded-lg p-3 text-center cursor-pointer" @click="viewModuleDetail('warehouse')">
                            <div class="w-8 h-8 bg-indigo-100 rounded-md mx-auto mb-2 flex items-center justify-center">
                                <svg class="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                            </div>
                            <div class="text-xs font-medium text-gray-900">仓储系统</div>
                            <div class="status-indicator status-online"></div>
                        </div>

                        <div></div>
                        <div></div>

                        <div class="module-card bg-white border border-gray-200 rounded-lg p-3 text-center cursor-pointer" @click="viewModuleDetail('finance')">
                            <div class="w-8 h-8 bg-yellow-100 rounded-md mx-auto mb-2 flex items-center justify-center">
                                <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="text-xs font-medium text-gray-900">财务系统</div>
                            <div class="status-indicator status-online"></div>
                        </div>

                        <!-- 第三行 -->
                        <div class="module-card bg-white border border-gray-200 rounded-lg p-3 text-center cursor-pointer" @click="viewModuleDetail('project')">
                            <div class="w-8 h-8 bg-teal-100 rounded-md mx-auto mb-2 flex items-center justify-center">
                                <svg class="w-4 h-4 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                                </svg>
                            </div>
                            <div class="text-xs font-medium text-gray-900">项目系统</div>
                            <div class="status-indicator status-online"></div>
                        </div>

                        <div class="module-card bg-white border border-gray-200 rounded-lg p-3 text-center cursor-pointer" @click="viewModuleDetail('quality')">
                            <div class="w-8 h-8 bg-red-100 rounded-md mx-auto mb-2 flex items-center justify-center">
                                <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                </svg>
                            </div>
                            <div class="text-xs font-medium text-gray-900">质量系统</div>
                            <div class="status-indicator status-online"></div>
                        </div>

                        <div class="module-card bg-white border border-gray-200 rounded-lg p-3 text-center cursor-pointer" @click="viewModuleDetail('crm')">
                            <div class="w-8 h-8 bg-pink-100 rounded-md mx-auto mb-2 flex items-center justify-center">
                                <svg class="w-4 h-4 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                </svg>
                            </div>
                            <div class="text-xs font-medium text-gray-900">CRM系统</div>
                            <div class="status-indicator status-online"></div>
                        </div>

                        <div class="module-card bg-white border border-gray-200 rounded-lg p-3 text-center cursor-pointer" @click="viewModuleDetail('hr')">
                            <div class="w-8 h-8 bg-gray-100 rounded-md mx-auto mb-2 flex items-center justify-center">
                                <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                            </div>
                            <div class="text-xs font-medium text-gray-900">HR系统</div>
                            <div class="status-indicator status-offline"></div>
                        </div>
                    </div>
                </div>

                <!-- 数据流视图 -->
                <div x-show="viewMode === 'dataflow'" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- 业务流程流向 -->
                        <div class="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4">
                            <h3 class="text-sm font-semibold text-blue-800 mb-3">订单业务流</h3>
                            <div class="space-y-2 text-xs">
                                <div class="flex items-center space-x-2">
                                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                    <span>CRM → 销售订单</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                    <span>销售 → PDM设计</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                    <span>PDM → 生产计划</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                    <span>生产 → 仓储发货</span>
                                </div>
                            </div>
                        </div>

                        <!-- 财务数据流 -->
                        <div class="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4">
                            <h3 class="text-sm font-semibold text-green-800 mb-3">财务数据流</h3>
                            <div class="space-y-2 text-xs">
                                <div class="flex items-center space-x-2">
                                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                    <span>销售 → 应收账款</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                    <span>采购 → 应付账款</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                    <span>生产 → 成本核算</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                    <span>项目 → 利润分析</span>
                                </div>
                            </div>
                        </div>

                        <!-- 质量控制流 -->
                        <div class="bg-gradient-to-r from-red-50 to-red-100 rounded-lg p-4">
                            <h3 class="text-sm font-semibold text-red-800 mb-3">质量控制流</h3>
                            <div class="space-y-2 text-xs">
                                <div class="flex items-center space-x-2">
                                    <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                                    <span>PDM → 设计质检</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                                    <span>采购 → 来料检验</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                                    <span>生产 → 过程控制</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                                    <span>仓储 → 出厂检验</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 实时数据同步状态 -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-sm font-semibold text-gray-800 mb-4">实时数据同步状态</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <template x-for="sync in syncStatus" :key="sync.module">
                                <div class="bg-white rounded-md p-3 border">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-xs font-medium text-gray-700" x-text="sync.module"></span>
                                        <span :class="sync.status === 'synced' ? 'status-online' : sync.status === 'syncing' ? 'status-warning' : 'status-offline'" class="status-indicator"></span>
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        <div>最后同步: <span x-text="sync.lastSync"></span></div>
                                        <div>记录数: <span x-text="sync.records"></span></div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 模块详情与集成配置 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- 模块集成状态 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-medium text-title-gray mb-4">模块集成状态</h3>
                    <div class="space-y-4">
                        <template x-for="module in modules" :key="module.id">
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div :class="module.status === 'online' ? 'bg-success-500' : module.status === 'warning' ? 'bg-warning-500' : 'bg-error-500'" class="w-3 h-3 rounded-full"></div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900" x-text="module.name"></div>
                                        <div class="text-xs text-gray-500" x-text="module.description"></div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-xs text-gray-500">版本: <span x-text="module.version"></span></span>
                                    <button @click="viewModuleDetail(module.id)" class="text-primary-600 hover:text-primary-700 text-xs">
                                        详情
                                    </button>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- API接口监控 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-medium text-title-gray mb-4">API接口监控</h3>
                    <div class="space-y-4">
                        <template x-for="api in apiStatus" :key="api.endpoint">
                            <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2 mb-1">
                                        <span :class="api.status === 'healthy' ? 'bg-success-500' : api.status === 'slow' ? 'bg-warning-500' : 'bg-error-500'" class="w-2 h-2 rounded-full"></span>
                                        <span class="text-sm font-medium text-gray-900" x-text="api.endpoint"></span>
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        响应时间: <span x-text="api.responseTime"></span> | 
                                        调用次数: <span x-text="api.calls"></span>
                                    </div>
                                </div>
                                <div class="text-xs">
                                    <div :class="api.status === 'healthy' ? 'text-success-600' : api.status === 'slow' ? 'text-warning-600' : 'text-error-600'" x-text="getApiStatusText(api.status)"></div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- 数据质量监控 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-medium text-title-gray mb-4">数据质量监控</h3>
                    <div class="space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-success-600">98.5%</div>
                                <div class="text-xs text-gray-500">数据完整性</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-warning-600">2.1%</div>
                                <div class="text-xs text-gray-500">数据冗余率</div>
                            </div>
                        </div>
                        <div class="space-y-2">
                            <template x-for="quality in dataQuality" :key="quality.table">
                                <div class="flex items-center justify-between text-sm">
                                    <span x-text="quality.table"></span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-20 bg-gray-200 rounded-full h-2">
                                            <div :style="`width: ${quality.score}%`" 
                                                 :class="quality.score >= 95 ? 'bg-success-500' : quality.score >= 85 ? 'bg-warning-500' : 'bg-error-500'" 
                                                 class="h-2 rounded-full"></div>
                                        </div>
                                        <span class="text-xs text-gray-600" x-text="quality.score + '%'"></span>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>

                <!-- 系统性能监控 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-medium text-title-gray mb-4">系统性能监控</h3>
                    <div class="h-48">
                        <canvas id="performanceChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 模块详情模态框 -->
        <div x-show="showModuleModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" style="display: none;">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-medium text-title-gray">模块详细信息</h3>
                    <button @click="showModuleModal = false" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div x-show="selectedModule" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">模块名称</label>
                            <div class="text-sm text-gray-900" x-text="selectedModule?.name"></div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">运行状态</label>
                            <div class="flex items-center space-x-2">
                                <div :class="selectedModule?.status === 'online' ? 'status-online' : selectedModule?.status === 'warning' ? 'status-warning' : 'status-offline'" class="status-indicator"></div>
                                <span class="text-sm" x-text="getStatusText(selectedModule?.status)"></span>
                            </div>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">模块描述</label>
                        <div class="text-sm text-gray-900" x-text="selectedModule?.description"></div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">版本号</label>
                            <div class="text-sm text-gray-900" x-text="selectedModule?.version"></div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">最后更新</label>
                            <div class="text-sm text-gray-900" x-text="selectedModule?.lastUpdate"></div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">API端点</label>
                            <div class="text-sm text-gray-900" x-text="selectedModule?.apiEndpoint"></div>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">集成接口</label>
                        <div class="space-y-2">
                            <template x-for="integration in selectedModule?.integrations || []" :key="integration.target">
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <span class="text-sm" x-text="integration.target"></span>
                                    <span :class="integration.status === 'active' ? 'text-success-600' : 'text-error-600'" class="text-xs" x-text="integration.status === 'active' ? '已连接' : '未连接'"></span>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function integrationDashboardApp() {
            return {
                viewMode: 'architecture',
                showModuleModal: false,
                selectedModule: null,
                
                modules: [
                    {
                        id: 'basic',
                        name: '基础管理系统',
                        description: '用户权限、组织架构、基础数据管理',
                        status: 'online',
                        version: 'v2.1.0',
                        lastUpdate: '2024-01-15',
                        apiEndpoint: '/api/basic/*',
                        integrations: [
                            { target: 'PDM系统', status: 'active' },
                            { target: '用户认证中心', status: 'active' }
                        ]
                    },
                    {
                        id: 'sales',
                        name: '销售管理系统',
                        description: '客户管理、订单处理、销售分析',
                        status: 'online',
                        version: 'v1.8.3',
                        lastUpdate: '2024-01-14',
                        apiEndpoint: '/api/sales/*',
                        integrations: [
                            { target: 'PDM系统', status: 'active' },
                            { target: 'CRM系统', status: 'active' },
                            { target: '财务系统', status: 'active' }
                        ]
                    },
                    {
                        id: 'procurement',
                        name: '采购管理系统',
                        description: '供应商管理、采购订单、物料管理',
                        status: 'online',
                        version: 'v1.6.2',
                        lastUpdate: '2024-01-13',
                        apiEndpoint: '/api/procurement/*',
                        integrations: [
                            { target: 'PDM系统', status: 'active' },
                            { target: '仓储系统', status: 'active' },
                            { target: '财务系统', status: 'active' }
                        ]
                    },
                    {
                        id: 'production',
                        name: '生产管理系统',
                        description: '生产计划、工艺管理、设备监控',
                        status: 'online',
                        version: 'v2.0.1',
                        lastUpdate: '2024-01-12',
                        apiEndpoint: '/api/production/*',
                        integrations: [
                            { target: 'PDM系统', status: 'active' },
                            { target: '质量系统', status: 'active' },
                            { target: '仓储系统', status: 'active' }
                        ]
                    },
                    {
                        id: 'warehouse',
                        name: '仓储管理系统',
                        description: '库存管理、出入库、仓位管理',
                        status: 'online',
                        version: 'v1.9.0',
                        lastUpdate: '2024-01-11',
                        apiEndpoint: '/api/warehouse/*',
                        integrations: [
                            { target: '采购系统', status: 'active' },
                            { target: '生产系统', status: 'active' },
                            { target: '销售系统', status: 'active' }
                        ]
                    },
                    {
                        id: 'finance',
                        name: '财务管理系统',
                        description: '会计核算、成本控制、财务分析',
                        status: 'online',
                        version: 'v1.7.4',
                        lastUpdate: '2024-01-10',
                        apiEndpoint: '/api/finance/*',
                        integrations: [
                            { target: '销售系统', status: 'active' },
                            { target: '采购系统', status: 'active' },
                            { target: '项目系统', status: 'active' }
                        ]
                    },
                    {
                        id: 'project',
                        name: '项目管理系统',
                        description: '项目跟踪、进度管理、交付管理',
                        status: 'online',
                        version: 'v1.5.1',
                        lastUpdate: '2024-01-09',
                        apiEndpoint: '/api/project/*',
                        integrations: [
                            { target: 'PDM系统', status: 'active' },
                            { target: '生产系统', status: 'active' },
                            { target: '财务系统', status: 'active' }
                        ]
                    },
                    {
                        id: 'quality',
                        name: '质量管理系统',
                        description: '质量控制、检验管理、质量分析',
                        status: 'online',
                        version: 'v1.4.2',
                        lastUpdate: '2024-01-08',
                        apiEndpoint: '/api/quality/*',
                        integrations: [
                            { target: 'PDM系统', status: 'active' },
                            { target: '生产系统', status: 'active' },
                            { target: '采购系统', status: 'active' }
                        ]
                    },
                    {
                        id: 'crm',
                        name: 'CRM客户系统',
                        description: '客户关系、销售机会、客户服务',
                        status: 'online',
                        version: 'v1.3.5',
                        lastUpdate: '2024-01-07',
                        apiEndpoint: '/api/crm/*',
                        integrations: [
                            { target: '销售系统', status: 'active' },
                            { target: '项目系统', status: 'active' }
                        ]
                    },
                    {
                        id: 'hr',
                        name: '人力资源系统',
                        description: '员工管理、薪资管理、绩效考核',
                        status: 'offline',
                        version: 'v1.2.0',
                        lastUpdate: '2024-01-06',
                        apiEndpoint: '/api/hr/*',
                        integrations: [
                            { target: '基础管理', status: 'inactive' },
                            { target: '财务系统', status: 'inactive' }
                        ]
                    }
                ],
                
                syncStatus: [
                    { module: '销售订单', status: 'synced', lastSync: '2min前', records: '1,247' },
                    { module: '产品数据', status: 'synced', lastSync: '5min前', records: '856' },
                    { module: '库存数据', status: 'syncing', lastSync: '进行中', records: '2,341' },
                    { module: '财务凭证', status: 'synced', lastSync: '1min前', records: '534' },
                    { module: '生产任务', status: 'synced', lastSync: '3min前', records: '189' },
                    { module: '质量检验', status: 'synced', lastSync: '4min前', records: '76' },
                    { module: '客户信息', status: 'synced', lastSync: '6min前', records: '423' },
                    { module: '供应商数据', status: 'synced', lastSync: '7min前', records: '158' }
                ],
                
                apiStatus: [
                    { endpoint: '/api/pdm/products', status: 'healthy', responseTime: '45ms', calls: '1,247' },
                    { endpoint: '/api/sales/orders', status: 'healthy', responseTime: '67ms', calls: '856' },
                    { endpoint: '/api/production/tasks', status: 'slow', responseTime: '234ms', calls: '432' },
                    { endpoint: '/api/warehouse/inventory', status: 'healthy', responseTime: '89ms', calls: '678' },
                    { endpoint: '/api/finance/transactions', status: 'healthy', responseTime: '56ms', calls: '345' },
                    { endpoint: '/api/quality/inspections', status: 'error', responseTime: 'timeout', calls: '23' }
                ],
                
                dataQuality: [
                    { table: '产品主数据', score: 98 },
                    { table: '客户信息', score: 95 },
                    { table: '订单数据', score: 97 },
                    { table: '库存记录', score: 92 },
                    { table: '财务数据', score: 99 },
                    { table: '质量记录', score: 89 }
                ],
                
                viewModuleDetail(moduleId) {
                    this.selectedModule = this.modules.find(m => m.id === moduleId);
                    this.showModuleModal = true;
                },
                
                getStatusText(status) {
                    const statusTexts = {
                        'online': '在线运行',
                        'warning': '运行异常',
                        'offline': '离线状态'
                    };
                    return statusTexts[status] || status;
                },
                
                getApiStatusText(status) {
                    const statusTexts = {
                        'healthy': '正常',
                        'slow': '缓慢',
                        'error': '错误'
                    };
                    return statusTexts[status] || status;
                },
                
                refreshDashboard() {
                    // 刷新仪表板数据
                    console.log('刷新系统状态...');
                    // 这里会调用API刷新数据
                },
                
                init() {
                    // 初始化图表
                    this.$nextTick(() => {
                        this.initPerformanceChart();
                    });
                },
                
                initPerformanceChart() {
                    const ctx = document.getElementById('performanceChart');
                    if (ctx) {
                        new Chart(ctx, {
                            type: 'line',
                            data: {
                                labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
                                datasets: [{
                                    label: 'CPU使用率',
                                    data: [15, 12, 25, 45, 38, 22],
                                    borderColor: '#3b82f6',
                                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                                    tension: 0.4
                                }, {
                                    label: '内存使用率',
                                    data: [35, 32, 42, 58, 55, 48],
                                    borderColor: '#22c55e',
                                    backgroundColor: 'rgba(34, 197, 94, 0.1)',
                                    tension: 0.4
                                }, {
                                    label: '数据库连接',
                                    data: [8, 6, 12, 18, 15, 11],
                                    borderColor: '#f59e0b',
                                    backgroundColor: 'rgba(245, 158, 11, 0.1)',
                                    tension: 0.4
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        max: 100
                                    }
                                },
                                plugins: {
                                    legend: {
                                        position: 'top'
                                    }
                                }
                            }
                        });
                    }
                }
            }
        }
    </script>
</body>
</html>