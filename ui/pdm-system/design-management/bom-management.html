<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BOM设计与转换 - 玻璃深加工ERP系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1890FF',
                        success: '#52C41A',
                        warning: '#FAAD14',
                        error: '#F5222D',
                        'title-gray': '#262626',
                        'body-gray': '#595959',
                        'aux-gray': '#8C8C8C',
                        'disabled-gray': '#BFBFBF',
                        'border-gray': '#D9D9D9',
                        'bg-gray': '#FAFAFA'
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', 'sans-serif']
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-bg-gray font-chinese" x-data="bomManagementApp()">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-border-gray h-16">
        <div class="flex items-center justify-between px-6 h-full">
            <div class="flex items-center space-x-4">
                <button @click="goBack()" class="text-aux-gray hover:text-title-gray transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                </button>
                <div>
                    <h1 class="text-xl font-medium text-title-gray">BOM设计与转换</h1>
                    <div class="flex items-center space-x-2 mt-1">
                        <span class="text-sm text-aux-gray" x-text="project.name"></span>
                        <span class="text-sm text-aux-gray">|</span>
                        <span class="text-sm text-aux-gray" x-text="project.code"></span>
                    </div>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <!-- BOM状态指示 -->
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-aux-gray">BOM状态:</span>
                    <div class="flex items-center space-x-1">
                        <div class="w-2 h-2 rounded-full" :class="bomStatus.sales ? 'bg-success' : 'bg-gray-300'"></div>
                        <span class="text-xs" :class="bomStatus.sales ? 'text-success' : 'text-aux-gray'">销售</span>
                        <span class="text-aux-gray">→</span>
                        <div class="w-2 h-2 rounded-full" :class="bomStatus.process ? 'bg-success' : 'bg-gray-300'"></div>
                        <span class="text-xs" :class="bomStatus.process ? 'text-success' : 'text-aux-gray'">工艺</span>
                        <span class="text-aux-gray">→</span>
                        <div class="w-2 h-2 rounded-full" :class="bomStatus.production ? 'bg-success' : 'bg-gray-300'"></div>
                        <span class="text-xs" :class="bomStatus.production ? 'text-success' : 'text-aux-gray'">生产</span>
                    </div>
                </div>
                
                <!-- 操作按钮 -->
                <div class="flex space-x-2">
                    <button @click="saveBom()" :disabled="saving"
                            class="px-4 py-2 bg-success text-white rounded-md hover:bg-green-600 transition-colors disabled:bg-disabled-gray">
                        <span x-show="saving" class="inline-block w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                        <span x-text="saving ? '保存中...' : '保存BOM'"></span>
                    </button>
                    <button @click="showTransformModal = true" :disabled="!canTransform"
                            class="px-4 py-2 bg-warning text-white rounded-md hover:bg-yellow-600 transition-colors disabled:bg-disabled-gray">
                        BOM转换
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main class="flex h-full">
        <!-- 左侧BOM类型选择 -->
        <div class="w-64 bg-white border-r border-border-gray">
            <div class="p-4">
                <h3 class="text-sm font-medium text-title-gray mb-4">BOM类型</h3>
                <div class="space-y-2">
                    <template x-for="type in bomTypes" :key="type.id">
                        <button @click="activeBomType = type.id"
                                class="w-full text-left p-3 rounded-lg transition-colors"
                                :class="activeBomType === type.id ? 'bg-primary text-white' : 'hover:bg-gray-50'">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="font-medium" x-text="type.name"></div>
                                    <div class="text-xs mt-1" 
                                         :class="activeBomType === type.id ? 'text-blue-100' : 'text-aux-gray'"
                                         x-text="type.description"></div>
                                </div>
                                <div class="w-3 h-3 rounded-full" 
                                     :class="bomStatus[type.id] ? 'bg-green-400' : (activeBomType === type.id ? 'bg-blue-100' : 'bg-gray-300')"></div>
                            </div>
                        </button>
                    </template>
                </div>
                
                <!-- BOM转换规则 -->
                <div class="mt-6">
                    <h4 class="text-sm font-medium text-title-gray mb-3">转换规则</h4>
                    <div class="space-y-2 text-xs text-aux-gray">
                        <div class="p-2 bg-gray-50 rounded">
                            <div class="font-medium">销售BOM → 工艺BOM</div>
                            <ul class="mt-1 space-y-1">
                                <li>• 添加加工工序</li>
                                <li>• 展开组件材料</li>
                                <li>• 计算损耗系数</li>
                            </ul>
                        </div>
                        <div class="p-2 bg-gray-50 rounded">
                            <div class="font-medium">工艺BOM → 生产BOM</div>
                            <ul class="mt-1 space-y-1">
                                <li>• 添加工序路线</li>
                                <li>• 计算工时定额</li>
                                <li>• 分配工作中心</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 中间BOM编辑区域 -->
        <div class="flex-1 flex flex-col">
            <!-- BOM工具栏 -->
            <div class="bg-white border-b border-border-gray px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <h3 class="text-lg font-medium text-title-gray" x-text="getCurrentBomTypeName()"></h3>
                        <span class="px-2 py-1 text-xs rounded-full"
                              :class="bomStatus[activeBomType] ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'"
                              x-text="bomStatus[activeBomType] ? '已确认' : '编辑中'"></span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <!-- 搜索框 -->
                        <div class="relative">
                            <input type="text" x-model="searchQuery" @input="filterBomItems"
                                   placeholder="搜索物料..."
                                   class="w-64 pl-8 pr-4 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                            <svg class="absolute left-2.5 top-2.5 h-4 w-4 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                        
                        <!-- 操作按钮 -->
                        <button @click="addBomItem()"
                                class="px-3 py-2 text-sm bg-primary text-white rounded-md hover:bg-blue-600">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                            </svg>
                            添加
                        </button>
                        <button @click="importBom()"
                                class="px-3 py-2 text-sm bg-success text-white rounded-md hover:bg-green-600">
                            导入
                        </button>
                        <button @click="exportBom()"
                                class="px-3 py-2 text-sm bg-warning text-white rounded-md hover:bg-yellow-600">
                            导出
                        </button>
                    </div>
                </div>
            </div>

            <!-- BOM树形结构 -->
            <div class="flex-1 overflow-auto bg-white">
                <table class="min-w-full">
                    <thead class="bg-bg-gray sticky top-0">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">层级</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">物料编码</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">物料名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">规格型号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">数量</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">单位</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider" x-show="activeBomType !== 'sales'">损耗率</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider" x-show="activeBomType === 'production'">工序</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-border-gray">
                        <template x-for="item in filteredBomItems" :key="item.id">
                            <tr class="hover:bg-gray-50" :class="item.isExpanded ? 'bg-blue-50' : ''">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <span class="text-sm text-aux-gray mr-2" x-text="item.level"></span>
                                        <button x-show="item.hasChildren" @click="toggleExpand(item)"
                                                class="text-primary hover:text-blue-600">
                                            <svg class="w-4 h-4 transition-transform" 
                                                 :class="item.isExpanded ? 'rotate-90' : ''"
                                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-title-gray" x-text="item.materialCode"></div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-body-gray" x-text="item.materialName"></div>
                                    <div x-show="item.description" class="text-xs text-aux-gray mt-1" x-text="item.description"></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-body-gray" x-text="item.specification"></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="number" x-model="item.quantity" step="0.001"
                                           class="w-20 px-2 py-1 text-sm border border-border-gray rounded focus:outline-none focus:ring-1 focus:ring-primary"
                                           :readonly="bomStatus[activeBomType]">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <select x-model="item.unit" 
                                            class="px-2 py-1 text-sm border border-border-gray rounded focus:outline-none focus:ring-1 focus:ring-primary"
                                            :disabled="bomStatus[activeBomType]">
                                        <option value="片">片</option>
                                        <option value="㎡">㎡</option>
                                        <option value="kg">kg</option>
                                        <option value="套">套</option>
                                        <option value="个">个</option>
                                        <option value="米">米</option>
                                        <option value="立方米">立方米</option>
                                    </select>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap" x-show="activeBomType !== 'sales'">
                                    <div class="flex items-center space-x-1">
                                        <input type="number" x-model="item.lossRate" step="0.01" min="0" max="100"
                                               class="w-16 px-2 py-1 text-sm border border-border-gray rounded focus:outline-none focus:ring-1 focus:ring-primary"
                                               :readonly="bomStatus[activeBomType]">
                                        <span class="text-xs text-aux-gray">%</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap" x-show="activeBomType === 'production'">
                                    <select x-model="item.operation" 
                                            class="px-2 py-1 text-sm border border-border-gray rounded focus:outline-none focus:ring-1 focus:ring-primary"
                                            :disabled="bomStatus[activeBomType]">
                                        <option value="">选择工序</option>
                                        <option value="cutting">切割</option>
                                        <option value="edging">磨边</option>
                                        <option value="tempering">钢化</option>
                                        <option value="laminating">夹胶</option>
                                        <option value="insulating">中空</option>
                                        <option value="coating">镀膜</option>
                                        <option value="assembly">组装</option>
                                    </select>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <div class="flex space-x-2">
                                        <button @click="editBomItem(item)" class="text-primary hover:text-blue-600">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                            </svg>
                                        </button>
                                        <button @click="copyBomItem(item)" class="text-success hover:text-green-600">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                            </svg>
                                        </button>
                                        <button @click="deleteBomItem(item)" class="text-error hover:text-red-600">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
                
                <!-- 空状态 -->
                <div x-show="filteredBomItems.length === 0" class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-title-gray">暂无BOM数据</h3>
                    <p class="mt-1 text-sm text-aux-gray">开始添加物料来构建BOM结构</p>
                    <div class="mt-6">
                        <button @click="addBomItem()" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600">
                            添加第一个物料
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧属性面板 -->
        <div class="w-80 bg-white border-l border-border-gray">
            <div class="p-6">
                <!-- BOM统计信息 -->
                <div class="mb-6">
                    <h3 class="text-sm font-medium text-title-gray mb-3">BOM统计</h3>
                    <div class="grid grid-cols-2 gap-3">
                        <div class="bg-blue-50 p-3 rounded-lg">
                            <div class="text-lg font-bold text-primary" x-text="bomStats.totalItems"></div>
                            <div class="text-xs text-aux-gray">物料数量</div>
                        </div>
                        <div class="bg-green-50 p-3 rounded-lg">
                            <div class="text-lg font-bold text-success" x-text="bomStats.totalLevels"></div>
                            <div class="text-xs text-aux-gray">BOM层级</div>
                        </div>
                        <div class="bg-yellow-50 p-3 rounded-lg">
                            <div class="text-lg font-bold text-warning" x-text="bomStats.materialTypes"></div>
                            <div class="text-xs text-aux-gray">物料类型</div>
                        </div>
                        <div class="bg-purple-50 p-3 rounded-lg">
                            <div class="text-lg font-bold text-purple-600" x-text="bomStats.operations"></div>
                            <div class="text-xs text-aux-gray">工序数量</div>
                        </div>
                    </div>
                </div>

                <!-- 成本分析 -->
                <div class="mb-6" x-show="activeBomType !== 'sales'">
                    <h3 class="text-sm font-medium text-title-gray mb-3">成本分析</h3>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-aux-gray">原材料成本</span>
                            <span class="text-sm font-medium text-title-gray" x-text="`¥${costAnalysis.material}`"></span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-aux-gray">加工成本</span>
                            <span class="text-sm font-medium text-title-gray" x-text="`¥${costAnalysis.processing}`"></span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-aux-gray">损耗成本</span>
                            <span class="text-sm font-medium text-title-gray" x-text="`¥${costAnalysis.loss}`"></span>
                        </div>
                        <div class="border-t border-border-gray pt-3">
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium text-title-gray">总成本</span>
                                <span class="text-lg font-bold text-primary" x-text="`¥${costAnalysis.total}`"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 物料分类 -->
                <div class="mb-6">
                    <h3 class="text-sm font-medium text-title-gray mb-3">物料分类</h3>
                    <div class="space-y-2">
                        <template x-for="category in materialCategories" :key="category.name">
                            <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                <span class="text-sm text-body-gray" x-text="category.name"></span>
                                <span class="text-sm font-medium text-title-gray" x-text="category.count"></span>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- 转换历史 -->
                <div>
                    <h3 class="text-sm font-medium text-title-gray mb-3">转换历史</h3>
                    <div class="space-y-2">
                        <template x-for="history in transformHistory" :key="history.id">
                            <div class="p-3 bg-gray-50 rounded text-xs">
                                <div class="flex items-center justify-between mb-1">
                                    <span class="font-medium text-title-gray" x-text="history.action"></span>
                                    <span class="text-aux-gray" x-text="history.time"></span>
                                </div>
                                <div class="text-aux-gray" x-text="history.description"></div>
                                <div class="text-aux-gray mt-1" x-text="`操作人: ${history.user}`"></div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- BOM转换模态框 -->
    <div x-show="showTransformModal" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
         @click="showTransformModal = false">
        <div class="relative top-20 mx-auto p-5 border max-w-2xl shadow-lg rounded-md bg-white" @click.stop>
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-title-gray">BOM转换</h3>
                <button @click="showTransformModal = false" class="text-aux-gray hover:text-title-gray">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            
            <div class="space-y-6">
                <!-- 转换方向选择 -->
                <div>
                    <label class="block text-sm font-medium text-title-gray mb-2">转换方向</label>
                    <div class="grid grid-cols-2 gap-4">
                        <button @click="transformDirection = 'sales_to_process'"
                                class="p-4 border rounded-lg text-left transition-colors"
                                :class="transformDirection === 'sales_to_process' ? 'border-primary bg-blue-50' : 'border-border-gray hover:bg-gray-50'">
                            <div class="font-medium text-title-gray">销售BOM → 工艺BOM</div>
                            <div class="text-sm text-aux-gray mt-1">添加工艺信息和损耗系数</div>
                        </button>
                        <button @click="transformDirection = 'process_to_production'"
                                class="p-4 border rounded-lg text-left transition-colors"
                                :class="transformDirection === 'process_to_production' ? 'border-primary bg-blue-50' : 'border-border-gray hover:bg-gray-50'">
                            <div class="font-medium text-title-gray">工艺BOM → 生产BOM</div>
                            <div class="text-sm text-aux-gray mt-1">添加工序和工作中心信息</div>
                        </button>
                    </div>
                </div>

                <!-- 转换选项 -->
                <div>
                    <label class="block text-sm font-medium text-title-gray mb-3">转换选项</label>
                    <div class="space-y-3">
                        <label class="flex items-center">
                            <input type="checkbox" x-model="transformOptions.autoCalculateLoss" class="mr-2">
                            <span class="text-sm text-body-gray">自动计算损耗率</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" x-model="transformOptions.addStandardOperations" class="mr-2">
                            <span class="text-sm text-body-gray">添加标准工序</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" x-model="transformOptions.inheritProperties" class="mr-2">
                            <span class="text-sm text-body-gray">继承物料属性</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" x-model="transformOptions.validateBom" class="mr-2">
                            <span class="text-sm text-body-gray">转换后自动验证</span>
                        </label>
                    </div>
                </div>

                <!-- 转换预览 -->
                <div x-show="transformDirection">
                    <label class="block text-sm font-medium text-title-gray mb-3">转换预览</label>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="text-sm text-aux-gray mb-2">将要进行的转换操作:</div>
                        <ul class="space-y-1 text-sm text-body-gray">
                            <template x-for="operation in getTransformOperations()" :key="operation">
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                    </svg>
                                    <span x-text="operation"></span>
                                </li>
                            </template>
                        </ul>
                    </div>
                </div>

                <!-- 底部按钮 -->
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" @click="showTransformModal = false"
                            class="px-4 py-2 text-sm font-medium text-body-gray bg-white border border-border-gray rounded-md hover:bg-gray-50">
                        取消
                    </button>
                    <button @click="executeBomTransform()" :disabled="transforming || !transformDirection"
                            class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-blue-600 disabled:bg-disabled-gray">
                        <span x-show="transforming" class="inline-block w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                        <span x-text="transforming ? '转换中...' : '开始转换'"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加物料模态框 -->
    <div x-show="showAddItemModal" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
         @click="showAddItemModal = false">
        <div class="relative top-20 mx-auto p-5 border max-w-3xl shadow-lg rounded-md bg-white" @click.stop>
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-title-gray">添加物料</h3>
                <button @click="showAddItemModal = false" class="text-aux-gray hover:text-title-gray">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            
            <form @submit.prevent="addNewBomItem()" class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-title-gray mb-2">物料编码</label>
                        <input type="text" x-model="newBomItem.materialCode" required
                               class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                               placeholder="如: GL-001">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-title-gray mb-2">物料名称</label>
                        <input type="text" x-model="newBomItem.materialName" required
                               class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                               placeholder="如: 6mm超白钢化玻璃">
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-title-gray mb-2">规格型号</label>
                        <input type="text" x-model="newBomItem.specification"
                               class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                               placeholder="如: 1200×800×6mm">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-title-gray mb-2">物料类型</label>
                        <select x-model="newBomItem.materialType" required
                                class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                            <option value="">请选择</option>
                            <option value="glass">玻璃</option>
                            <option value="hardware">五金件</option>
                            <option value="sealant">密封胶</option>
                            <option value="frame">框料</option>
                            <option value="accessory">辅料</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-title-gray mb-2">数量</label>
                        <input type="number" x-model="newBomItem.quantity" step="0.001" min="0" required
                               class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-title-gray mb-2">单位</label>
                        <select x-model="newBomItem.unit" required
                                class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                            <option value="片">片</option>
                            <option value="㎡">㎡</option>
                            <option value="kg">kg</option>
                            <option value="套">套</option>
                            <option value="个">个</option>
                            <option value="米">米</option>
                            <option value="立方米">立方米</option>
                        </select>
                    </div>
                    <div x-show="activeBomType !== 'sales'">
                        <label class="block text-sm font-medium text-title-gray mb-2">损耗率(%)</label>
                        <input type="number" x-model="newBomItem.lossRate" step="0.01" min="0" max="100"
                               class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-title-gray mb-2">描述</label>
                    <textarea x-model="newBomItem.description" rows="3"
                              class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                              placeholder="物料描述信息..."></textarea>
                </div>

                <!-- 底部按钮 -->
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" @click="showAddItemModal = false"
                            class="px-4 py-2 text-sm font-medium text-body-gray bg-white border border-border-gray rounded-md hover:bg-gray-50">
                        取消
                    </button>
                    <button type="submit" :disabled="addingItem"
                            class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-blue-600 disabled:bg-disabled-gray">
                        <span x-show="addingItem" class="inline-block w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                        <span x-text="addingItem ? '添加中...' : '确定添加'"></span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function bomManagementApp() {
            return {
                saving: false,
                transforming: false,
                addingItem: false,
                activeBomType: 'sales',
                searchQuery: '',
                showTransformModal: false,
                showAddItemModal: false,
                transformDirection: '',
                
                project: {
                    id: 1,
                    code: 'PRJ-2025-001',
                    name: '高强度钢化玻璃幕墙系统设计'
                },
                
                bomTypes: [
                    { id: 'sales', name: '销售BOM', description: '面向客户的产品结构' },
                    { id: 'process', name: '工艺BOM', description: '面向设计的工艺结构' },
                    { id: 'production', name: '生产BOM', description: '面向车间的生产结构' }
                ],
                
                bomStatus: {
                    sales: true,
                    process: true,
                    production: false
                },
                
                transformOptions: {
                    autoCalculateLoss: true,
                    addStandardOperations: true,
                    inheritProperties: true,
                    validateBom: true
                },
                
                newBomItem: {
                    materialCode: '',
                    materialName: '',
                    specification: '',
                    materialType: '',
                    quantity: 1,
                    unit: '片',
                    lossRate: 0,
                    description: ''
                },
                
                salesBomItems: [
                    {
                        id: 1, level: '1', materialCode: 'BG-001', materialName: '6+12A+6mm Low-E中空钢化玻璃',
                        specification: '1200×800×24mm', quantity: 1, unit: '㎡', lossRate: 0,
                        description: '主体玻璃产品', hasChildren: true, isExpanded: false, materialType: 'glass'
                    },
                    {
                        id: 2, level: '2', materialCode: 'GL-001', materialName: '6mm超白钢化玻璃',
                        specification: '1200×800×6mm', quantity: 2, unit: '片', lossRate: 0,
                        description: '外片和内片玻璃', hasChildren: false, isExpanded: false, materialType: 'glass'
                    },
                    {
                        id: 3, level: '2', materialCode: 'SP-001', materialName: '12mm铝隔条',
                        specification: '双道密封', quantity: 1, unit: '套', lossRate: 0,
                        description: '中空玻璃隔条', hasChildren: false, isExpanded: false, materialType: 'hardware'
                    },
                    {
                        id: 4, level: '2', materialCode: 'SL-001', materialName: '结构密封胶',
                        specification: 'DC995', quantity: 0.5, unit: 'kg', lossRate: 0,
                        description: '道康宁结构胶', hasChildren: false, isExpanded: false, materialType: 'sealant'
                    }
                ],
                
                processBomItems: [
                    {
                        id: 1, level: '1', materialCode: 'BG-001', materialName: '6+12A+6mm Low-E中空钢化玻璃',
                        specification: '1200×800×24mm', quantity: 1, unit: '㎡', lossRate: 0,
                        description: '主体玻璃产品', hasChildren: true, isExpanded: false, materialType: 'glass'
                    },
                    {
                        id: 2, level: '2', materialCode: 'GL-RAW-001', materialName: '6mm超白浮法玻璃原片',
                        specification: '1200×800×6mm', quantity: 2.1, unit: '片', lossRate: 5,
                        description: '考虑切割损耗的原片玻璃', hasChildren: false, isExpanded: false, materialType: 'glass'
                    },
                    {
                        id: 3, level: '2', materialCode: 'COAT-001', materialName: 'Low-E镀膜',
                        specification: '双银Low-E', quantity: 1, unit: '面', lossRate: 2,
                        description: '低辐射镀膜', hasChildren: false, isExpanded: false, materialType: 'accessory'
                    },
                    {
                        id: 4, level: '2', materialCode: 'SP-001', materialName: '12mm铝隔条',
                        specification: '双道密封', quantity: 1, unit: '套', lossRate: 3,
                        description: '中空玻璃隔条', hasChildren: false, isExpanded: false, materialType: 'hardware'
                    },
                    {
                        id: 5, level: '2', materialCode: 'DS-001', materialName: '双道密封胶',
                        specification: 'DC883+DC995', quantity: 0.3, unit: 'kg', lossRate: 10,
                        description: '耐候胶+结构胶', hasChildren: false, isExpanded: false, materialType: 'sealant'
                    },
                    {
                        id: 6, level: '2', materialCode: 'GAS-001', materialName: '氩气',
                        specification: '99.9%纯度', quantity: 0.02, unit: '立方米', lossRate: 15,
                        description: '中空层填充气体', hasChildren: false, isExpanded: false, materialType: 'accessory'
                    }
                ],
                
                productionBomItems: [
                    {
                        id: 1, level: '1', materialCode: 'BG-001', materialName: '6+12A+6mm Low-E中空钢化玻璃',
                        specification: '1200×800×24mm', quantity: 1, unit: '㎡', lossRate: 0,
                        description: '主体玻璃产品', hasChildren: true, isExpanded: false, 
                        materialType: 'glass', operation: 'assembly'
                    },
                    {
                        id: 2, level: '2', materialCode: 'GL-RAW-001', materialName: '6mm超白浮法玻璃原片',
                        specification: '1200×800×6mm', quantity: 2.1, unit: '片', lossRate: 5,
                        description: '考虑切割损耗的原片玻璃', hasChildren: true, isExpanded: false, 
                        materialType: 'glass', operation: 'cutting'
                    },
                    {
                        id: 3, level: '3', materialCode: 'CUT-001', materialName: '切割工序',
                        specification: '自动切割线', quantity: 2, unit: '片', lossRate: 0,
                        description: '玻璃切割加工', hasChildren: false, isExpanded: false, 
                        materialType: 'process', operation: 'cutting'
                    },
                    {
                        id: 4, level: '3', materialCode: 'EDGE-001', materialName: '磨边工序',
                        specification: '直边+倒角', quantity: 2, unit: '片', lossRate: 0,
                        description: '玻璃磨边加工', hasChildren: false, isExpanded: false, 
                        materialType: 'process', operation: 'edging'
                    },
                    {
                        id: 5, level: '3', materialCode: 'TEMP-001', materialName: '钢化工序',
                        specification: '680℃钢化', quantity: 2, unit: '片', lossRate: 1,
                        description: '玻璃钢化热处理', hasChildren: false, isExpanded: false, 
                        materialType: 'process', operation: 'tempering'
                    },
                    {
                        id: 6, level: '2', materialCode: 'SP-PROC-001', materialName: '铝隔条加工',
                        specification: '切割+折弯+充分子筛', quantity: 1, unit: '套', lossRate: 3,
                        description: '隔条加工工序', hasChildren: false, isExpanded: false, 
                        materialType: 'process', operation: 'processing'
                    },
                    {
                        id: 7, level: '2', materialCode: 'ASM-001', materialName: '中空组装工序',
                        specification: '合片+压合+充气', quantity: 1, unit: '片', lossRate: 0,
                        description: '中空玻璃组装', hasChildren: false, isExpanded: false, 
                        materialType: 'process', operation: 'assembly'
                    }
                ],
                
                bomStats: {
                    totalItems: 4,
                    totalLevels: 2,
                    materialTypes: 3,
                    operations: 0
                },
                
                costAnalysis: {
                    material: '1,250.00',
                    processing: '320.00',
                    loss: '78.50',
                    total: '1,648.50'
                },
                
                materialCategories: [
                    { name: '玻璃材料', count: 2 },
                    { name: '五金件', count: 1 },
                    { name: '密封胶', count: 1 },
                    { name: '辅助材料', count: 1 }
                ],
                
                transformHistory: [
                    {
                        id: 1,
                        action: '工艺BOM创建',
                        description: '从销售BOM转换生成工艺BOM，添加了损耗率和工艺信息',
                        user: '张工',
                        time: '2025-02-10 14:30'
                    },
                    {
                        id: 2,
                        action: '销售BOM确认',
                        description: '销售BOM结构确认完成，锁定版本v1.0',
                        user: '李工',
                        time: '2025-02-08 09:15'
                    }
                ],
                
                filteredBomItems: [],
                
                init() {
                    this.updateBomData();
                },
                
                get canTransform() {
                    if (this.activeBomType === 'sales' && this.bomStatus.sales) return true;
                    if (this.activeBomType === 'process' && this.bomStatus.process) return true;
                    return false;
                },
                
                getCurrentBomTypeName() {
                    const type = this.bomTypes.find(t => t.id === this.activeBomType);
                    return type ? type.name : '';
                },
                
                getCurrentBomItems() {
                    switch (this.activeBomType) {
                        case 'sales':
                            return this.salesBomItems;
                        case 'process':
                            return this.processBomItems;
                        case 'production':
                            return this.productionBomItems;
                        default:
                            return [];
                    }
                },
                
                updateBomData() {
                    const items = this.getCurrentBomItems();
                    this.filteredBomItems = items;
                    
                    // 更新统计信息
                    this.bomStats.totalItems = items.length;
                    this.bomStats.totalLevels = Math.max(...items.map(item => parseInt(item.level.split('.').length)));
                    this.bomStats.materialTypes = new Set(items.map(item => item.materialType)).size;
                    this.bomStats.operations = items.filter(item => item.operation).length;
                    
                    // 更新物料分类统计
                    const categoryCount = {};
                    items.forEach(item => {
                        const type = this.getMaterialTypeName(item.materialType);
                        categoryCount[type] = (categoryCount[type] || 0) + 1;
                    });
                    
                    this.materialCategories = Object.entries(categoryCount).map(([name, count]) => ({
                        name, count
                    }));
                },
                
                getMaterialTypeName(type) {
                    const typeMap = {
                        'glass': '玻璃材料',
                        'hardware': '五金件',
                        'sealant': '密封胶',
                        'frame': '框料',
                        'accessory': '辅助材料',
                        'process': '加工工序'
                    };
                    return typeMap[type] || '其他';
                },
                
                filterBomItems() {
                    const items = this.getCurrentBomItems();
                    if (!this.searchQuery) {
                        this.filteredBomItems = items;
                        return;
                    }
                    
                    const query = this.searchQuery.toLowerCase();
                    this.filteredBomItems = items.filter(item =>
                        item.materialCode.toLowerCase().includes(query) ||
                        item.materialName.toLowerCase().includes(query) ||
                        item.specification.toLowerCase().includes(query)
                    );
                },
                
                goBack() {
                    history.back();
                },
                
                async saveBom() {
                    this.saving = true;
                    try {
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        alert('BOM保存成功！');
                    } catch (error) {
                        alert('保存失败，请重试');
                    } finally {
                        this.saving = false;
                    }
                },
                
                toggleExpand(item) {
                    item.isExpanded = !item.isExpanded;
                },
                
                addBomItem() {
                    this.newBomItem = {
                        materialCode: '',
                        materialName: '',
                        specification: '',
                        materialType: '',
                        quantity: 1,
                        unit: '片',
                        lossRate: 0,
                        description: ''
                    };
                    this.showAddItemModal = true;
                },
                
                async addNewBomItem() {
                    this.addingItem = true;
                    try {
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        
                        const newItem = {
                            id: Date.now(),
                            level: '1',
                            hasChildren: false,
                            isExpanded: false,
                            operation: this.activeBomType === 'production' ? '' : undefined,
                            ...this.newBomItem
                        };
                        
                        // 添加到当前BOM
                        const currentItems = this.getCurrentBomItems();
                        currentItems.push(newItem);
                        
                        this.updateBomData();
                        this.showAddItemModal = false;
                        alert('物料添加成功！');
                    } catch (error) {
                        alert('添加失败，请重试');
                    } finally {
                        this.addingItem = false;
                    }
                },
                
                editBomItem(item) {
                    alert(`编辑物料: ${item.materialName}`);
                },
                
                copyBomItem(item) {
                    const newItem = {
                        ...item,
                        id: Date.now(),
                        materialCode: item.materialCode + '-COPY'
                    };
                    
                    const currentItems = this.getCurrentBomItems();
                    currentItems.push(newItem);
                    this.updateBomData();
                    alert('物料复制成功！');
                },
                
                deleteBomItem(item) {
                    if (confirm(`确定要删除物料 ${item.materialName} 吗？`)) {
                        const currentItems = this.getCurrentBomItems();
                        const index = currentItems.findIndex(i => i.id === item.id);
                        if (index > -1) {
                            currentItems.splice(index, 1);
                            this.updateBomData();
                            alert('物料删除成功');
                        }
                    }
                },
                
                importBom() {
                    alert('导入BOM功能');
                },
                
                exportBom() {
                    alert(`导出${this.getCurrentBomTypeName()}数据`);
                },
                
                getTransformOperations() {
                    if (this.transformDirection === 'sales_to_process') {
                        return [
                            '复制销售BOM结构到工艺BOM',
                            '为每个物料添加默认损耗率',
                            '展开复合材料的组成成分',
                            '添加工艺参数和加工要求',
                            '生成工艺文件关联'
                        ];
                    } else if (this.transformDirection === 'process_to_production') {
                        return [
                            '复制工艺BOM结构到生产BOM',
                            '为每个物料分配对应工序',
                            '计算实际生产用料（含损耗）',
                            '添加工作中心和设备信息',
                            '生成工艺路线卡'
                        ];
                    }
                    return [];
                },
                
                async executeBomTransform() {
                    this.transforming = true;
                    try {
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        
                        if (this.transformDirection === 'sales_to_process') {
                            this.bomStatus.process = true;
                            this.activeBomType = 'process';
                        } else if (this.transformDirection === 'process_to_production') {
                            this.bomStatus.production = true;
                            this.activeBomType = 'production';
                        }
                        
                        // 添加转换历史记录
                        const actionMap = {
                            'sales_to_process': '销售BOM转工艺BOM',
                            'process_to_production': '工艺BOM转生产BOM'
                        };
                        
                        this.transformHistory.unshift({
                            id: Date.now(),
                            action: actionMap[this.transformDirection],
                            description: `BOM转换完成，自动生成了${this.getTransformOperations().length}项操作`,
                            user: '系统自动',
                            time: new Date().toLocaleString('zh-CN')
                        });
                        
                        this.updateBomData();
                        this.showTransformModal = false;
                        alert('BOM转换成功！');
                    } catch (error) {
                        alert('转换失败，请重试');
                    } finally {
                        this.transforming = false;
                    }
                }
            }
        }
    </script>
</body>
</html>