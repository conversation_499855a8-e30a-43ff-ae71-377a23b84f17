<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计评审流程 - 玻璃深加工ERP系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1890FF',
                        success: '#52C41A',
                        warning: '#FAAD14',
                        error: '#F5222D',
                        'title-gray': '#262626',
                        'body-gray': '#595959',
                        'aux-gray': '#8C8C8C',
                        'disabled-gray': '#BFBFBF',
                        'border-gray': '#D9D9D9',
                        'bg-gray': '#FAFAFA'
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', 'sans-serif']
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-bg-gray font-chinese" x-data="reviewWorkflowApp()">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-border-gray h-16">
        <div class="flex items-center justify-between px-6 h-full">
            <div class="flex items-center space-x-4">
                <button @click="goBack()" class="text-aux-gray hover:text-title-gray transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                </button>
                <div>
                    <h1 class="text-xl font-medium text-title-gray">设计评审流程</h1>
                    <div class="flex items-center space-x-2 mt-1">
                        <span class="text-sm text-aux-gray" x-text="project.name"></span>
                        <span class="text-sm text-aux-gray">|</span>
                        <span class="text-sm text-aux-gray" x-text="project.code"></span>
                    </div>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <!-- 评审状态概览 -->
                <div class="flex items-center space-x-4 text-sm">
                    <div class="flex items-center space-x-1">
                        <div class="w-2 h-2 bg-success rounded-full"></div>
                        <span class="text-aux-gray">已完成:</span>
                        <span class="font-medium text-title-gray" x-text="reviewStats.completed"></span>
                    </div>
                    <div class="flex items-center space-x-1">
                        <div class="w-2 h-2 bg-primary rounded-full"></div>
                        <span class="text-aux-gray">进行中:</span>
                        <span class="font-medium text-title-gray" x-text="reviewStats.inProgress"></span>
                    </div>
                    <div class="flex items-center space-x-1">
                        <div class="w-2 h-2 bg-warning rounded-full"></div>
                        <span class="text-aux-gray">待审核:</span>
                        <span class="font-medium text-title-gray" x-text="reviewStats.pending"></span>
                    </div>
                </div>
                
                <!-- 操作按钮 -->
                <div class="flex space-x-2">
                    <button @click="showCreateReviewModal = true"
                            class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600 transition-colors">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        发起评审
                    </button>
                    <button @click="showTemplateModal = true"
                            class="px-4 py-2 bg-success text-white rounded-md hover:bg-green-600 transition-colors">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        评审模板
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main class="flex h-full">
        <!-- 左侧评审类型筛选 -->
        <div class="w-64 bg-white border-r border-border-gray">
            <div class="p-4">
                <h3 class="text-sm font-medium text-title-gray mb-4">评审类型</h3>
                <div class="space-y-2">
                    <template x-for="type in reviewTypes" :key="type.id">
                        <button @click="selectedReviewType = type.id; filterReviews()"
                                class="w-full text-left p-3 rounded-lg transition-colors"
                                :class="selectedReviewType === type.id ? 'bg-primary text-white' : 'hover:bg-gray-50'">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="font-medium" x-text="type.name"></div>
                                    <div class="text-xs mt-1" 
                                         :class="selectedReviewType === type.id ? 'text-blue-100' : 'text-aux-gray'"
                                         x-text="type.description"></div>
                                </div>
                                <span class="text-xs px-2 py-1 rounded-full"
                                      :class="selectedReviewType === type.id ? 'bg-blue-200 text-blue-800' : 'bg-gray-100 text-aux-gray'"
                                      x-text="type.count"></span>
                            </div>
                        </button>
                    </template>
                </div>
                
                <!-- 我的待办 -->
                <div class="mt-6">
                    <h4 class="text-sm font-medium text-title-gray mb-3">我的待办</h4>
                    <div class="space-y-2">
                        <div class="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <div class="text-sm font-medium text-yellow-800">待我审核</div>
                            <div class="text-xs text-yellow-600 mt-1">3个评审项目等待处理</div>
                        </div>
                        <div class="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                            <div class="text-sm font-medium text-blue-800">我发起的</div>
                            <div class="text-xs text-blue-600 mt-1">5个评审项目进行中</div>
                        </div>
                        <div class="p-3 bg-green-50 border border-green-200 rounded-lg">
                            <div class="text-sm font-medium text-green-800">已完成</div>
                            <div class="text-xs text-green-600 mt-1">12个评审项目已完成</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 中间评审列表区域 -->
        <div class="flex-1 flex flex-col">
            <!-- 工具栏 -->
            <div class="bg-white border-b border-border-gray px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <h3 class="text-lg font-medium text-title-gray" x-text="getSelectedReviewTypeName()"></h3>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-aux-gray">排序:</span>
                            <select x-model="sortBy" @change="sortReviews()"
                                    class="px-3 py-1 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                                <option value="createTime">创建时间</option>
                                <option value="deadline">截止时间</option>
                                <option value="priority">优先级</option>
                                <option value="status">状态</option>
                            </select>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <!-- 搜索框 -->
                        <div class="relative">
                            <input type="text" x-model="searchQuery" @input="filterReviews"
                                   placeholder="搜索评审项目..."
                                   class="w-64 pl-8 pr-4 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                            <svg class="absolute left-2.5 top-2.5 h-4 w-4 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                        
                        <!-- 状态筛选 -->
                        <select x-model="statusFilter" @change="filterReviews"
                                class="px-3 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                            <option value="">全部状态</option>
                            <option value="draft">草稿</option>
                            <option value="pending">待审核</option>
                            <option value="in_progress">进行中</option>
                            <option value="completed">已完成</option>
                            <option value="rejected">已驳回</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 评审列表内容 -->
            <div class="flex-1 overflow-auto bg-white">
                <div class="p-6 space-y-4">
                    <template x-for="review in filteredReviews" :key="review.id">
                        <div class="border border-border-gray rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer"
                             @click="selectReview(review)">
                            <!-- 评审头部信息 -->
                            <div class="flex items-start justify-between mb-4">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3 mb-2">
                                        <h4 class="text-lg font-medium text-title-gray" x-text="review.title"></h4>
                                        <span class="px-2 py-1 text-xs rounded-full"
                                              :class="getReviewStatusClass(review.status)"
                                              x-text="getReviewStatusText(review.status)"></span>
                                        <span class="px-2 py-1 text-xs rounded-full"
                                              :class="getPriorityClass(review.priority)"
                                              x-text="getPriorityText(review.priority)"></span>
                                    </div>
                                    <p class="text-sm text-body-gray mb-3" x-text="review.description"></p>
                                    <div class="flex items-center space-x-6 text-sm text-aux-gray">
                                        <div class="flex items-center space-x-1">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                            </svg>
                                            <span>发起人: <span class="text-body-gray" x-text="review.initiator"></span></span>
                                        </div>
                                        <div class="flex items-center space-x-1">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4h3a2 2 0 012 2v1H6V9a2 2 0 012-2h3zM2 21h20a2 2 0 002-2v-3H0v3a2 2 0 002 2z"/>
                                            </svg>
                                            <span>类型: <span class="text-body-gray" x-text="review.type"></span></span>
                                        </div>
                                        <div class="flex items-center space-x-1">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                            <span>截止: <span class="text-body-gray" x-text="review.deadline"></span></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <button @click.stop="quickApprove(review)" x-show="review.canApprove"
                                            class="px-3 py-1 text-sm bg-success text-white rounded-md hover:bg-green-600">
                                        快速通过
                                    </button>
                                    <button @click.stop="openReviewDetail(review)"
                                            class="px-3 py-1 text-sm bg-primary text-white rounded-md hover:bg-blue-600">
                                        查看详情
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 评审流程进度 -->
                            <div class="mb-4">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium text-title-gray">评审进度</span>
                                    <span class="text-sm text-aux-gray" x-text="`${review.completedSteps}/${review.totalSteps} 完成`"></span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="h-2 rounded-full transition-all"
                                         :class="review.status === 'completed' ? 'bg-success' : review.status === 'rejected' ? 'bg-error' : 'bg-primary'"
                                         :style="`width: ${(review.completedSteps / review.totalSteps) * 100}%`"></div>
                                </div>
                            </div>
                            
                            <!-- 当前评审步骤 -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <template x-for="(step, index) in review.steps.slice(0, 3)" :key="step.id">
                                    <div class="flex items-center space-x-3 p-3 rounded-lg"
                                         :class="getStepBackgroundClass(step.status)">
                                        <div class="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm"
                                             :class="getStepStatusColor(step.status)">
                                            <span x-text="index + 1"></span>
                                        </div>
                                        <div class="flex-1">
                                            <div class="text-sm font-medium" :class="getStepTextClass(step.status)" x-text="step.name"></div>
                                            <div class="text-xs" :class="getStepTextClass(step.status, true)" x-text="step.reviewer"></div>
                                            <div class="text-xs" :class="getStepTextClass(step.status, true)">
                                                <span x-show="step.status === 'completed'" x-text="step.completedTime"></span>
                                                <span x-show="step.status === 'in_progress'">进行中</span>
                                                <span x-show="step.status === 'pending'">等待中</span>
                                            </div>
                                        </div>
                                        <div x-show="step.status === 'completed'" class="text-success">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                            </svg>
                                        </div>
                                        <div x-show="step.status === 'in_progress'" class="text-primary">
                                            <svg class="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </template>
                            </div>
                            
                            <!-- 最近活动 -->
                            <div x-show="review.recentActivity" class="mt-4 pt-4 border-t border-border-gray">
                                <div class="flex items-start space-x-3">
                                    <div class="w-6 h-6 rounded-full flex items-center justify-center text-white text-xs"
                                         :class="getActivityColor(review.recentActivity?.type)">
                                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <div class="text-sm text-title-gray" x-text="review.recentActivity?.description"></div>
                                        <div class="flex items-center justify-between mt-1">
                                            <span class="text-xs text-aux-gray" x-text="review.recentActivity?.user"></span>
                                            <span class="text-xs text-aux-gray" x-text="review.recentActivity?.time"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
                
                <!-- 空状态 -->
                <div x-show="filteredReviews.length === 0" class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-title-gray">暂无评审项目</h3>
                    <p class="mt-1 text-sm text-aux-gray">发起您的第一个设计评审</p>
                    <div class="mt-6">
                        <button @click="showCreateReviewModal = true" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600">
                            发起评审
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧详情面板 -->
        <div class="w-80 bg-white border-l border-border-gray" x-show="selectedReview">
            <div class="p-6">
                <!-- 评审详情 -->
                <div class="mb-6">
                    <h3 class="text-sm font-medium text-title-gray mb-3">评审详情</h3>
                    <div x-show="selectedReview" class="space-y-3">
                        <div>
                            <h4 class="text-sm font-medium text-title-gray" x-text="selectedReview?.title"></h4>
                            <p class="text-xs text-aux-gray mt-1" x-text="selectedReview?.type"></p>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div>
                                <span class="text-aux-gray">状态:</span>
                                <span class="px-2 py-1 text-xs rounded-full ml-1"
                                      :class="getReviewStatusClass(selectedReview?.status)"
                                      x-text="getReviewStatusText(selectedReview?.status)"></span>
                            </div>
                            <div>
                                <span class="text-aux-gray">优先级:</span>
                                <span class="px-2 py-1 text-xs rounded-full ml-1"
                                      :class="getPriorityClass(selectedReview?.priority)"
                                      x-text="getPriorityText(selectedReview?.priority)"></span>
                            </div>
                        </div>
                        
                        <div class="text-sm">
                            <span class="text-aux-gray">描述:</span>
                            <p class="text-body-gray mt-1" x-text="selectedReview?.description"></p>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div>
                                <span class="text-aux-gray">发起人:</span>
                                <span class="text-body-gray ml-1" x-text="selectedReview?.initiator"></span>
                            </div>
                            <div>
                                <span class="text-aux-gray">截止时间:</span>
                                <span class="text-body-gray ml-1" x-text="selectedReview?.deadline"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 评审流程 -->
                <div class="mb-6">
                    <h3 class="text-sm font-medium text-title-gray mb-3">评审流程</h3>
                    <div class="space-y-3">
                        <template x-for="(step, index) in selectedReview?.steps" :key="step.id">
                            <div class="flex items-start space-x-3">
                                <div class="flex flex-col items-center">
                                    <div class="w-6 h-6 rounded-full flex items-center justify-center text-white text-xs"
                                         :class="getStepStatusColor(step.status)">
                                        <span x-text="index + 1"></span>
                                    </div>
                                    <div x-show="index < selectedReview.steps.length - 1"
                                         class="w-0.5 h-8 mt-2"
                                         :class="step.status === 'completed' ? 'bg-success' : 'bg-border-gray'"></div>
                                </div>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-title-gray" x-text="step.name"></div>
                                    <div class="text-xs text-aux-gray" x-text="step.reviewer"></div>
                                    <div class="text-xs text-aux-gray mt-1">
                                        <span x-show="step.status === 'completed'" x-text="`完成于 ${step.completedTime}`"></span>
                                        <span x-show="step.status === 'in_progress'">进行中</span>
                                        <span x-show="step.status === 'pending'">等待中</span>
                                    </div>
                                    <div x-show="step.comments" class="mt-2 p-2 bg-gray-50 rounded text-xs">
                                        <p class="text-body-gray" x-text="step.comments"></p>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- 相关文档 -->
                <div class="mb-6" x-show="selectedReview?.documents?.length > 0">
                    <h3 class="text-sm font-medium text-title-gray mb-3">相关文档</h3>
                    <div class="space-y-2">
                        <template x-for="document in selectedReview?.documents" :key="document.id">
                            <div class="flex items-center space-x-2 p-2 bg-gray-50 rounded text-xs">
                                <svg class="w-4 h-4 text-primary" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"/>
                                </svg>
                                <span class="flex-1 text-body-gray" x-text="document.name"></span>
                                <button class="text-primary hover:text-blue-600">查看</button>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="mb-6">
                    <h3 class="text-sm font-medium text-title-gray mb-3">快速操作</h3>
                    <div class="space-y-2">
                        <button @click="submitReview(selectedReview)" x-show="selectedReview?.canSubmit"
                                class="w-full flex items-center justify-center space-x-2 px-3 py-2 text-sm bg-primary text-white rounded-md hover:bg-blue-600">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                            </svg>
                            <span>提交评审</span>
                        </button>
                        <button @click="approveReview(selectedReview)" x-show="selectedReview?.canApprove"
                                class="w-full flex items-center justify-center space-x-2 px-3 py-2 text-sm bg-success text-white rounded-md hover:bg-green-600">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span>通过评审</span>
                        </button>
                        <button @click="rejectReview(selectedReview)" x-show="selectedReview?.canReject"
                                class="w-full flex items-center justify-center space-x-2 px-3 py-2 text-sm bg-error text-white rounded-md hover:bg-red-600">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                            <span>驳回评审</span>
                        </button>
                        <button @click="addComment(selectedReview)"
                                class="w-full flex items-center justify-center space-x-2 px-3 py-2 text-sm bg-warning text-white rounded-md hover:bg-yellow-600">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                            </svg>
                            <span>添加意见</span>
                        </button>
                    </div>
                </div>

                <!-- 评审历史 -->
                <div>
                    <h3 class="text-sm font-medium text-title-gray mb-3">评审历史</h3>
                    <div class="space-y-2 max-h-32 overflow-y-auto">
                        <template x-for="activity in selectedReview?.activities" :key="activity.id">
                            <div class="p-2 bg-gray-50 rounded text-xs">
                                <div class="flex items-center justify-between mb-1">
                                    <span class="font-medium text-title-gray" x-text="activity.action"></span>
                                    <span class="text-aux-gray" x-text="activity.time"></span>
                                </div>
                                <div class="text-aux-gray" x-text="activity.description"></div>
                                <div class="text-aux-gray mt-1" x-text="`by ${activity.user}`"></div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 创建评审模态框 -->
    <div x-show="showCreateReviewModal" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
         @click="showCreateReviewModal = false">
        <div class="relative top-20 mx-auto p-5 border max-w-4xl shadow-lg rounded-md bg-white" @click.stop>
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-title-gray">发起设计评审</h3>
                <button @click="showCreateReviewModal = false" class="text-aux-gray hover:text-title-gray">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            
            <form @submit.prevent="createReview()" class="space-y-6">
                <!-- 基本信息 -->
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-title-gray mb-2">评审标题 <span class="text-error">*</span></label>
                        <input type="text" x-model="createForm.title" required
                               class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                               placeholder="请输入评审标题">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-title-gray mb-2">评审类型 <span class="text-error">*</span></label>
                        <select x-model="createForm.type" required
                                class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                            <option value="">请选择</option>
                            <option value="方案评审">方案评审</option>
                            <option value="详细设计评审">详细设计评审</option>
                            <option value="技术评审">技术评审</option>
                            <option value="工艺评审">工艺评审</option>
                            <option value="最终评审">最终评审</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-title-gray mb-2">优先级</label>
                        <select x-model="createForm.priority"
                                class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                            <option value="normal">普通</option>
                            <option value="high">高</option>
                            <option value="urgent">紧急</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-title-gray mb-2">截止时间 <span class="text-error">*</span></label>
                        <input type="datetime-local" x-model="createForm.deadline" required
                               class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-title-gray mb-2">评审描述</label>
                    <textarea x-model="createForm.description" rows="3"
                              class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                              placeholder="请描述评审的目的、范围和要求..."></textarea>
                </div>

                <!-- 评审流程配置 -->
                <div>
                    <label class="block text-sm font-medium text-title-gray mb-3">评审流程配置</label>
                    <div class="space-y-3">
                        <template x-for="(step, index) in createForm.steps" :key="index">
                            <div class="flex items-center space-x-3 p-3 border border-border-gray rounded-md">
                                <div class="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm">
                                    <span x-text="index + 1"></span>
                                </div>
                                <div class="flex-1 grid grid-cols-3 gap-3">
                                    <input type="text" x-model="step.name" placeholder="步骤名称"
                                           class="px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                                    <select x-model="step.reviewer"
                                            class="px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                                        <option value="">选择评审人</option>
                                        <template x-for="member in teamMembers" :key="member.id">
                                            <option :value="member.name" x-text="`${member.name} (${member.role})`"></option>
                                        </template>
                                    </select>
                                    <input type="number" x-model="step.duration" placeholder="预计天数" min="1"
                                           class="px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                                </div>
                                <button type="button" @click="removeStep(index)" x-show="createForm.steps.length > 1"
                                        class="text-error hover:text-red-600">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                    </svg>
                                </button>
                            </div>
                        </template>
                        <button type="button" @click="addStep()"
                                class="w-full py-2 border-2 border-dashed border-border-gray rounded-md text-aux-gray hover:border-primary hover:text-primary transition-colors">
                            + 添加评审步骤
                        </button>
                    </div>
                </div>

                <!-- 相关文档 -->
                <div>
                    <label class="block text-sm font-medium text-title-gray mb-2">相关文档</label>
                    <div class="border-2 border-dashed border-border-gray rounded-lg p-4 text-center">
                        <svg class="mx-auto h-8 w-8 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        <p class="mt-2 text-sm text-aux-gray">点击添加评审文档</p>
                    </div>
                </div>

                <!-- 底部按钮 -->
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" @click="showCreateReviewModal = false"
                            class="px-4 py-2 text-sm font-medium text-body-gray bg-white border border-border-gray rounded-md hover:bg-gray-50">
                        取消
                    </button>
                    <button type="button" @click="saveDraft()"
                            class="px-4 py-2 text-sm font-medium text-aux-gray bg-white border border-border-gray rounded-md hover:bg-gray-50">
                        保存草稿
                    </button>
                    <button type="submit" :disabled="creating"
                            class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-blue-600 disabled:bg-disabled-gray">
                        <span x-show="creating" class="inline-block w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                        <span x-text="creating ? '创建中...' : '发起评审'"></span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function reviewWorkflowApp() {
            return {
                selectedReviewType: 'all',
                searchQuery: '',
                statusFilter: '',
                sortBy: 'createTime',
                selectedReview: null,
                showCreateReviewModal: false,
                showTemplateModal: false,
                creating: false,
                
                project: {
                    id: 1,
                    code: 'PRJ-2025-001',
                    name: '高强度钢化玻璃幕墙系统设计'
                },
                
                reviewStats: {
                    completed: 12,
                    inProgress: 5,
                    pending: 3
                },
                
                createForm: {
                    title: '',
                    type: '',
                    priority: 'normal',
                    deadline: '',
                    description: '',
                    steps: [
                        { name: '技术评审', reviewer: '', duration: 3 },
                        { name: '工艺评审', reviewer: '', duration: 2 },
                        { name: '最终确认', reviewer: '', duration: 1 }
                    ]
                },
                
                reviewTypes: [
                    { id: 'all', name: '全部评审', description: '所有评审项目', count: 20 },
                    { id: 'proposal', name: '方案评审', description: '设计方案评审', count: 8 },
                    { id: 'detailed', name: '详细设计评审', description: '详细设计方案评审', count: 6 },
                    { id: 'technical', name: '技术评审', description: '技术方案评审', count: 4 },
                    { id: 'process', name: '工艺评审', description: '工艺方案评审', count: 2 }
                ],
                
                reviews: [
                    {
                        id: 1,
                        title: '高强度钢化玻璃技术方案评审',
                        description: '对高强度钢化玻璃的技术方案进行全面评审，包括材料选择、工艺参数、性能指标等关键技术点',
                        type: '技术评审',
                        status: 'in_progress',
                        priority: 'high',
                        initiator: '张工',
                        deadline: '2025-02-20 18:00',
                        createTime: '2025-02-10 09:00',
                        completedSteps: 2,
                        totalSteps: 4,
                        canApprove: true,
                        canSubmit: false,
                        canReject: true,
                        steps: [
                            { id: 1, name: '初步审查', reviewer: '技术总监', status: 'completed', completedTime: '2025-02-12 15:30', comments: '技术方案整体可行，建议优化工艺参数' },
                            { id: 2, name: '专家评审', reviewer: '外部专家', status: 'completed', completedTime: '2025-02-14 10:20', comments: '材料选择合理，需要补充性能测试数据' },
                            { id: 3, name: '工艺评审', reviewer: '工艺主管', status: 'in_progress' },
                            { id: 4, name: '最终确认', reviewer: '项目经理', status: 'pending' }
                        ],
                        documents: [
                            { id: 1, name: '技术方案书v2.1.pdf' },
                            { id: 2, name: '材料性能测试报告.pdf' },
                            { id: 3, name: '工艺流程图.dwg' }
                        ],
                        recentActivity: {
                            type: 'review',
                            description: '工艺主管开始进行工艺评审',
                            user: '王工',
                            time: '2小时前'
                        },
                        activities: [
                            { id: 1, action: '专家评审完成', description: '外部专家完成技术方案评审', user: '外部专家', time: '2025-02-14 10:20' },
                            { id: 2, action: '初步审查完成', description: '技术总监完成初步审查', user: '技术总监', time: '2025-02-12 15:30' },
                            { id: 3, action: '评审启动', description: '张工发起技术方案评审', user: '张工', time: '2025-02-10 09:00' }
                        ]
                    },
                    {
                        id: 2,
                        title: '中空玻璃结构设计评审',
                        description: '对中空玻璃结构设计进行评审，重点关注密封性能、保温性能和结构强度',
                        type: '详细设计评审',
                        status: 'pending',
                        priority: 'normal',
                        initiator: '李工',
                        deadline: '2025-02-25 17:00',
                        createTime: '2025-02-13 14:00',
                        completedSteps: 0,
                        totalSteps: 3,
                        canApprove: false,
                        canSubmit: true,
                        canReject: false,
                        steps: [
                            { id: 1, name: '结构审查', reviewer: '结构工程师', status: 'pending' },
                            { id: 2, name: '性能评估', reviewer: '性能专家', status: 'pending' },
                            { id: 3, name: '最终确认', reviewer: '技术总监', status: 'pending' }
                        ],
                        documents: [
                            { id: 1, name: '结构设计图纸v1.5.dwg' },
                            { id: 2, name: '性能计算书.xlsx' }
                        ],
                        recentActivity: {
                            type: 'create',
                            description: '李工发起了中空玻璃结构设计评审',
                            user: '李工',
                            time: '1天前'
                        },
                        activities: [
                            { id: 1, action: '评审创建', description: '李工创建了结构设计评审', user: '李工', time: '2025-02-13 14:00' }
                        ]
                    },
                    {
                        id: 3,
                        title: '幕墙安装工艺评审',
                        description: '评审幕墙安装工艺流程，确保安装质量和施工安全',
                        type: '工艺评审',
                        status: 'completed',
                        priority: 'normal',
                        initiator: '王工',
                        deadline: '2025-02-15 16:00',
                        createTime: '2025-02-05 10:00',
                        completedSteps: 3,
                        totalSteps: 3,
                        canApprove: false,
                        canSubmit: false,
                        canReject: false,
                        steps: [
                            { id: 1, name: '工艺审查', reviewer: '工艺专家', status: 'completed', completedTime: '2025-02-08 11:30', comments: '工艺流程合理，建议增加质量检查点' },
                            { id: 2, name: '安全评估', reviewer: '安全工程师', status: 'completed', completedTime: '2025-02-10 14:15', comments: '安全措施完备，符合规范要求' },
                            { id: 3, name: '最终批准', reviewer: '项目经理', status: 'completed', completedTime: '2025-02-12 09:45', comments: '评审通过，可进入实施阶段' }
                        ],
                        documents: [
                            { id: 1, name: '安装工艺规程v1.2.docx' },
                            { id: 2, name: '安全作业指导书.pdf' }
                        ],
                        recentActivity: {
                            type: 'approve',
                            description: '项目经理批准了工艺评审',
                            user: '项目经理',
                            time: '3天前'
                        },
                        activities: [
                            { id: 1, action: '评审完成', description: '项目经理批准了安装工艺评审', user: '项目经理', time: '2025-02-12 09:45' },
                            { id: 2, action: '安全评估完成', description: '安全工程师完成安全评估', user: '安全工程师', time: '2025-02-10 14:15' },
                            { id: 3, action: '工艺审查完成', description: '工艺专家完成工艺审查', user: '工艺专家', time: '2025-02-08 11:30' }
                        ]
                    }
                ],
                
                teamMembers: [
                    { id: 1, name: '张工', role: '项目负责人' },
                    { id: 2, name: '李工', role: '结构设计师' },
                    { id: 3, name: '王工', role: '工艺工程师' },
                    { id: 4, name: '赵工', role: '质量工程师' },
                    { id: 5, name: '技术总监', role: '技术总监' },
                    { id: 6, name: '工艺主管', role: '工艺主管' },
                    { id: 7, name: '项目经理', role: '项目经理' }
                ],
                
                filteredReviews: [],
                
                init() {
                    this.filterReviews();
                    if (this.filteredReviews.length > 0) {
                        this.selectedReview = this.filteredReviews[0];
                    }
                },
                
                getSelectedReviewTypeName() {
                    const type = this.reviewTypes.find(t => t.id === this.selectedReviewType);
                    return type ? type.name : '全部评审';
                },
                
                filterReviews() {
                    let filtered = [...this.reviews];
                    
                    // 类型筛选
                    if (this.selectedReviewType && this.selectedReviewType !== 'all') {
                        const typeMap = {
                            'proposal': '方案评审',
                            'detailed': '详细设计评审',
                            'technical': '技术评审',
                            'process': '工艺评审'
                        };
                        const typeName = typeMap[this.selectedReviewType];
                        if (typeName) {
                            filtered = filtered.filter(review => review.type === typeName);
                        }
                    }
                    
                    // 搜索筛选
                    if (this.searchQuery) {
                        const query = this.searchQuery.toLowerCase();
                        filtered = filtered.filter(review =>
                            review.title.toLowerCase().includes(query) ||
                            review.description.toLowerCase().includes(query) ||
                            review.initiator.toLowerCase().includes(query)
                        );
                    }
                    
                    // 状态筛选
                    if (this.statusFilter) {
                        filtered = filtered.filter(review => review.status === this.statusFilter);
                    }
                    
                    this.filteredReviews = filtered;
                    this.sortReviews();
                },
                
                sortReviews() {
                    this.filteredReviews.sort((a, b) => {
                        switch (this.sortBy) {
                            case 'createTime':
                                return new Date(b.createTime) - new Date(a.createTime);
                            case 'deadline':
                                return new Date(a.deadline) - new Date(b.deadline);
                            case 'priority':
                                const priorityOrder = { 'urgent': 3, 'high': 2, 'normal': 1 };
                                return priorityOrder[b.priority] - priorityOrder[a.priority];
                            case 'status':
                                const statusOrder = { 'in_progress': 3, 'pending': 2, 'completed': 1 };
                                return statusOrder[b.status] - statusOrder[a.status];
                            default:
                                return 0;
                        }
                    });
                },
                
                selectReview(review) {
                    this.selectedReview = review;
                },
                
                getReviewStatusText(status) {
                    const statusMap = {
                        'draft': '草稿',
                        'pending': '待审核',
                        'in_progress': '进行中',
                        'completed': '已完成',
                        'rejected': '已驳回'
                    };
                    return statusMap[status] || '未知';
                },
                
                getReviewStatusClass(status) {
                    const classMap = {
                        'draft': 'bg-gray-100 text-gray-800',
                        'pending': 'bg-yellow-100 text-yellow-800',
                        'in_progress': 'bg-blue-100 text-blue-800',
                        'completed': 'bg-green-100 text-green-800',
                        'rejected': 'bg-red-100 text-red-800'
                    };
                    return classMap[status] || 'bg-gray-100 text-gray-800';
                },
                
                getPriorityText(priority) {
                    const priorityMap = {
                        'normal': '普通',
                        'high': '高',
                        'urgent': '紧急'
                    };
                    return priorityMap[priority] || '普通';
                },
                
                getPriorityClass(priority) {
                    const classMap = {
                        'normal': 'bg-gray-100 text-gray-800',
                        'high': 'bg-orange-100 text-orange-800',
                        'urgent': 'bg-red-100 text-red-800'
                    };
                    return classMap[priority] || 'bg-gray-100 text-gray-800';
                },
                
                getStepStatusColor(status) {
                    const colorMap = {
                        'pending': 'bg-gray-500',
                        'in_progress': 'bg-blue-500',
                        'completed': 'bg-green-500',
                        'rejected': 'bg-red-500'
                    };
                    return colorMap[status] || 'bg-gray-500';
                },
                
                getStepBackgroundClass(status) {
                    const classMap = {
                        'pending': 'bg-gray-50',
                        'in_progress': 'bg-blue-50',
                        'completed': 'bg-green-50',
                        'rejected': 'bg-red-50'
                    };
                    return classMap[status] || 'bg-gray-50';
                },
                
                getStepTextClass(status, isSecondary = false) {
                    const baseClass = isSecondary ? 'text-xs' : 'text-sm font-medium';
                    const colorMap = {
                        'pending': isSecondary ? 'text-gray-500' : 'text-gray-700',
                        'in_progress': isSecondary ? 'text-blue-600' : 'text-blue-800',
                        'completed': isSecondary ? 'text-green-600' : 'text-green-800',
                        'rejected': isSecondary ? 'text-red-600' : 'text-red-800'
                    };
                    return `${baseClass} ${colorMap[status] || colorMap.pending}`;
                },
                
                getActivityColor(type) {
                    const colorMap = {
                        'create': 'bg-blue-500',
                        'review': 'bg-purple-500',
                        'approve': 'bg-green-500',
                        'reject': 'bg-red-500',
                        'comment': 'bg-yellow-500'
                    };
                    return colorMap[type] || 'bg-gray-500';
                },
                
                quickApprove(review) {
                    if (confirm(`确定要快速通过评审"${review.title}"吗？`)) {
                        review.status = 'completed';
                        review.completedSteps = review.totalSteps;
                        this.reviewStats.completed++;
                        this.reviewStats.inProgress--;
                        alert('评审已通过！');
                    }
                },
                
                openReviewDetail(review) {
                    this.selectedReview = review;
                    alert(`查看评审详情: ${review.title}`);
                },
                
                submitReview(review) {
                    if (confirm(`确定要提交评审"${review.title}"吗？`)) {
                        review.status = 'pending';
                        alert('评审已提交！');
                    }
                },
                
                approveReview(review) {
                    const comments = prompt('请输入审核意见（可选）:');
                    if (comments !== null) {
                        // 找到当前进行中的步骤
                        const currentStep = review.steps.find(step => step.status === 'in_progress');
                        if (currentStep) {
                            currentStep.status = 'completed';
                            currentStep.completedTime = new Date().toLocaleString('zh-CN');
                            currentStep.comments = comments || '审核通过';
                            review.completedSteps++;
                            
                            // 激活下一步骤
                            const nextStep = review.steps.find(step => step.status === 'pending');
                            if (nextStep) {
                                nextStep.status = 'in_progress';
                            } else {
                                review.status = 'completed';
                                this.reviewStats.completed++;
                                this.reviewStats.inProgress--;
                            }
                        }
                        alert('评审通过！');
                    }
                },
                
                rejectReview(review) {
                    const reason = prompt('请输入驳回原因:');
                    if (reason) {
                        review.status = 'rejected';
                        const currentStep = review.steps.find(step => step.status === 'in_progress');
                        if (currentStep) {
                            currentStep.status = 'rejected';
                            currentStep.comments = reason;
                        }
                        this.reviewStats.inProgress--;
                        alert('评审已驳回！');
                    }
                },
                
                addComment(review) {
                    const comment = prompt('请输入评审意见:');
                    if (comment) {
                        // 添加到活动记录
                        review.activities.unshift({
                            id: Date.now(),
                            action: '添加意见',
                            description: comment,
                            user: '当前用户',
                            time: new Date().toLocaleString('zh-CN')
                        });
                        alert('意见已添加！');
                    }
                },
                
                addStep() {
                    this.createForm.steps.push({
                        name: '',
                        reviewer: '',
                        duration: 1
                    });
                },
                
                removeStep(index) {
                    this.createForm.steps.splice(index, 1);
                },
                
                async createReview() {
                    this.creating = true;
                    try {
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        
                        const newReview = {
                            id: Date.now(),
                            title: this.createForm.title,
                            description: this.createForm.description,
                            type: this.createForm.type,
                            status: 'pending',
                            priority: this.createForm.priority,
                            initiator: '当前用户',
                            deadline: this.createForm.deadline,
                            createTime: new Date().toLocaleString('zh-CN'),
                            completedSteps: 0,
                            totalSteps: this.createForm.steps.length,
                            canApprove: false,
                            canSubmit: true,
                            canReject: false,
                            steps: this.createForm.steps.map((step, index) => ({
                                id: index + 1,
                                name: step.name,
                                reviewer: step.reviewer,
                                status: index === 0 ? 'pending' : 'pending'
                            })),
                            documents: [],
                            activities: [{
                                id: 1,
                                action: '评审创建',
                                description: '创建了新的评审流程',
                                user: '当前用户',
                                time: new Date().toLocaleString('zh-CN')
                            }]
                        };
                        
                        this.reviews.unshift(newReview);
                        this.filterReviews();
                        this.reviewStats.pending++;
                        
                        // 重置表单
                        this.createForm = {
                            title: '',
                            type: '',
                            priority: 'normal',
                            deadline: '',
                            description: '',
                            steps: [
                                { name: '技术评审', reviewer: '', duration: 3 },
                                { name: '工艺评审', reviewer: '', duration: 2 },
                                { name: '最终确认', reviewer: '', duration: 1 }
                            ]
                        };
                        
                        this.showCreateReviewModal = false;
                        alert('评审创建成功！');
                    } catch (error) {
                        alert('创建失败，请重试');
                    } finally {
                        this.creating = false;
                    }
                },
                
                saveDraft() {
                    alert('草稿已保存');
                },
                
                goBack() {
                    history.back();
                }
            }
        }
    </script>
</body>
</html>