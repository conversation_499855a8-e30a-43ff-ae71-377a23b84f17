<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品设计项目管理 - PDM系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        [x-cloak] { display: none !important; }
        .font-chinese {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 6px;
        }
        .priority-high { background-color: #ef4444; }
        .priority-medium { background-color: #f59e0b; }
        .priority-low { background-color: #22c55e; }
        .project-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .gantt-bar {
            height: 20px;
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }
        .gantt-progress {
            height: 100%;
            border-radius: 10px;
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe', 
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8'
                        },
                        success: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            500: '#22c55e',
                            600: '#16a34a'
                        },
                        warning: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            500: '#f59e0b',
                            600: '#d97706'
                        },
                        error: {
                            50: '#fef2f2',
                            100: '#fee2e2',
                            500: '#ef4444',
                            600: '#dc2626'
                        },
                        'title-gray': '#1f2937'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 font-chinese" x-data="designApp()" x-cloak>
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-gray-200 h-16">
        <div class="flex items-center justify-between px-6 h-full">
            <div class="flex items-center space-x-4">
                <h1 class="text-xl font-semibold text-title-gray">产品设计项目管理</h1>
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-4">
                        <span class="text-sm text-gray-600">PDM系统: <span class="font-medium text-primary-600">玻璃深加工ERP</span></span>
                        <span class="text-sm text-gray-600">|</span>
                        <span class="text-sm text-gray-600">活跃项目: <span class="font-medium text-success-600" x-text="stats.active"></span></span>
                    </div>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <button @click="showAnalyticsModal = true" class="bg-purple-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-purple-700">
                    项目分析
                </button>
                <button @click="showImportModal = true" class="bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-700">
                    批量导入
                </button>
                <button @click="exportProjects" class="bg-success-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-success-700">
                    导出项目
                </button>
                <button @click="showCreateModal = true" class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    新建项目
                </button>
            </div>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main class="flex h-screen pt-1">
        <!-- 左侧筛选区域 -->
        <div class="w-72 bg-white border-r border-gray-200">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-title-gray">筛选条件</h3>
                    <button @click="resetFilters" class="text-xs text-primary-600 hover:text-primary-700">重置</button>
                </div>
                
                <!-- 快速筛选标签 -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">快速筛选</label>
                    <div class="flex flex-wrap gap-2">
                        <button @click="applyQuickFilter('urgent')" 
                                :class="quickFilter === 'urgent' ? 'bg-error-100 text-error-700' : 'bg-gray-100 text-gray-700'"
                                class="px-2 py-1 rounded-full text-xs font-medium">紧急项目</button>
                        <button @click="applyQuickFilter('delayed')" 
                                :class="quickFilter === 'delayed' ? 'bg-warning-100 text-warning-700' : 'bg-gray-100 text-gray-700'"
                                class="px-2 py-1 rounded-full text-xs font-medium">延期项目</button>
                        <button @click="applyQuickFilter('thisWeek')" 
                                :class="quickFilter === 'thisWeek' ? 'bg-primary-100 text-primary-700' : 'bg-gray-100 text-gray-700'"
                                class="px-2 py-1 rounded-full text-xs font-medium">本周截止</button>
                    </div>
                </div>

                <!-- 项目状态筛选 -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">项目状态</label>
                    <div class="space-y-2">
                        <template x-for="status in statusOptions" :key="status.value">
                            <label class="flex items-center">
                                <input type="checkbox" x-model="statusFilters" :value="status.value" @change="filterProjects" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                <span class="ml-2 text-sm text-gray-700" x-text="status.label"></span>
                                <span class="ml-auto text-xs text-gray-500" x-text="'(' + status.count + ')'" ></span>
                            </label>
                        </template>
                    </div>
                </div>

                <!-- 产品类型筛选 -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">产品类型</label>
                    <select x-model="productTypeFilter" @change="filterProjects" 
                            class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                        <option value="">全部类型</option>
                        <option value="curtain_wall">玻璃幕墙</option>
                        <option value="tempered_glass">钢化玻璃</option>
                        <option value="laminated_glass">夹胶玻璃</option>
                        <option value="insulated_glass">中空玻璃</option>
                        <option value="furniture_glass">家具玻璃</option>
                        <option value="decoration_glass">装饰玻璃</option>
                        <option value="special_glass">特种玻璃</option>
                    </select>
                </div>

                <!-- 优先级筛选 -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">项目优先级</label>
                    <div class="space-y-2">
                        <template x-for="priority in priorityOptions" :key="priority.value">
                            <label class="flex items-center">
                                <input type="radio" x-model="priorityFilter" :value="priority.value" @change="filterProjects" name="priority" class="text-primary-600 focus:ring-primary-500">
                                <div class="ml-2 flex items-center">
                                    <div :class="priority.color" class="w-2 h-2 rounded-full mr-2"></div>
                                    <span class="text-sm text-gray-700" x-text="priority.label"></span>
                                </div>
                                <span class="ml-auto text-xs text-gray-500" x-text="'(' + priority.count + ')'" ></span>
                            </label>
                        </template>
                    </div>
                </div>

                <!-- 负责人筛选 -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">设计负责人</label>
                    <select x-model="ownerFilter" @change="filterProjects"
                            class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                        <option value="">全部负责人</option>
                        <option value="王工程师">王工程师</option>
                        <option value="李设计师">李设计师</option>
                        <option value="张主管">张主管</option>
                        <option value="刘工程师">刘工程师</option>
                        <option value="陈设计师">陈设计师</option>
                    </select>
                </div>

                <!-- 客户筛选 -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">客户</label>
                    <select x-model="customerFilter" @change="filterProjects"
                            class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                        <option value="">全部客户</option>
                        <option value="华彩玻璃">华彩玻璃</option>
                        <option value="金辉建材">金辉建材</option>
                        <option value="蓝天装饰">蓝天装饰</option>
                        <option value="绿城地产">绿城地产</option>
                        <option value="星河置业">星河置业</option>
                    </select>
                </div>

                <!-- 时间筛选 -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">项目周期</label>
                    <div class="grid grid-cols-2 gap-2">
                        <div>
                            <label class="block text-xs text-gray-500 mb-1">开始时间</label>
                            <input type="date" x-model="dateRange.start" @change="filterProjects"
                                   class="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                        </div>
                        <div>
                            <label class="block text-xs text-gray-500 mb-1">结束时间</label>
                            <input type="date" x-model="dateRange.end" @change="filterProjects"
                                   class="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 中间项目列表区域 -->
        <div class="flex-1 flex flex-col">
            <!-- 搜索工具栏 -->
            <div class="bg-white border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4 flex-1">
                        <!-- 搜索框 -->
                        <div class="flex-1 max-w-md">
                            <div class="relative">
                                <input type="text" x-model="searchQuery" @input="handleSearch"
                                       placeholder="搜索项目名称、编号或客户..."
                                       class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                                <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                </svg>
                            </div>
                        </div>
                        
                        <!-- 视图切换 -->
                        <div class="flex border border-gray-300 rounded-md">
                            <button @click="viewMode = 'table'" 
                                    :class="viewMode === 'table' ? 'bg-primary-100 text-primary-600' : 'text-gray-600'"
                                    class="px-3 py-2 text-sm border-r border-gray-300">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                </svg>
                            </button>
                            <button @click="viewMode = 'cards'" 
                                    :class="viewMode === 'cards' ? 'bg-primary-100 text-primary-600' : 'text-gray-600'"
                                    class="px-3 py-2 text-sm border-r border-gray-300">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                                </svg>
                            </button>
                            <button @click="viewMode = 'gantt'" 
                                    :class="viewMode === 'gantt' ? 'bg-primary-100 text-primary-600' : 'text-gray-600'"
                                    class="px-3 py-2 text-sm">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-3">
                        <!-- 排序选择 -->
                        <select x-model="sortBy" @change="sortProjects"
                                class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 text-sm">
                            <option value="createTime">按创建时间</option>
                            <option value="updateTime">按更新时间</option>
                            <option value="progress">按进度</option>
                            <option value="deadline">按截止时间</option>
                            <option value="priority">按优先级</option>
                        </select>

                        <!-- 批量操作 -->
                        <div class="flex space-x-2" x-show="selectedProjects.length > 0">
                            <button @click="batchReview" class="px-3 py-2 text-sm bg-warning-600 text-white rounded-md hover:bg-warning-700">
                                批量评审 (<span x-text="selectedProjects.length"></span>)
                            </button>
                            <button @click="batchExport" class="px-3 py-2 text-sm bg-success-600 text-white rounded-md hover:bg-success-700">
                                批量导出
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 项目列表内容 -->
            <div class="flex-1 overflow-auto bg-white">
                <!-- 表格视图 -->
                <div x-show="viewMode === 'table'">
                    <table class="min-w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left">
                                    <input type="checkbox" @change="toggleSelectAll" :checked="isAllSelected" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目信息</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品类型</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">客户</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">优先级</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">进度</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">截止时间</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            <template x-for="project in paginatedProjects" :key="project.id">
                                <tr class="hover:bg-gray-50" :class="selectedProjects.includes(project.id) ? 'bg-blue-50' : ''">
                                    <td class="px-6 py-4">
                                        <input type="checkbox" :value="project.id" x-model="selectedProjects" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                    </td>
                                    <td class="px-6 py-4">
                                        <div>
                                            <div class="text-sm font-medium text-title-gray" x-text="project.name"></div>
                                            <div class="text-xs text-gray-500" x-text="project.code"></div>
                                            <div class="text-xs text-gray-600 mt-1 max-w-xs truncate" x-text="project.description"></div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full" x-text="getProductTypeText(project.productType)"></span>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm text-gray-900" x-text="project.customer"></div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <span class="px-2 py-1 text-xs rounded-full"
                                              :class="getStatusClass(project.status)"
                                              x-text="getStatusText(project.status)"></span>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            <div :class="getPriorityColor(project.priority)" class="w-2 h-2 rounded-full mr-2"></div>
                                            <span class="text-xs text-gray-700" x-text="getPriorityText(project.priority)"></span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            <div class="w-full bg-gray-200 rounded-full h-2 mr-2 max-w-16">
                                                <div class="bg-primary-500 h-2 rounded-full" :style="`width: ${project.progress}%`"></div>
                                            </div>
                                            <span class="text-xs text-gray-600" x-text="`${project.progress}%`"></span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                                                <span class="text-xs text-primary-600" x-text="project.owner.charAt(0)"></span>
                                            </div>
                                            <span class="text-sm text-gray-700" x-text="project.owner"></span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm text-gray-700" x-text="project.deadline"></div>
                                        <div class="text-xs" :class="isOverdue(project.deadline) ? 'text-error-600' : isUpcoming(project.deadline) ? 'text-warning-600' : 'text-gray-500'">
                                            <span x-text="getTimeStatus(project.deadline)"></span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 text-sm">
                                        <div class="flex space-x-2">
                                            <button @click="viewProject(project)" class="text-primary-600 hover:text-primary-700">详情</button>
                                            <button @click="editProject(project)" class="text-gray-600 hover:text-gray-700">编辑</button>
                                            <button @click="reviewProject(project)" class="text-warning-600 hover:text-warning-700">评审</button>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>

                <!-- 卡片视图 -->
                <div x-show="viewMode === 'cards'" class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <template x-for="project in paginatedProjects" :key="project.id">
                            <div class="project-card bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow">
                                <div class="flex items-start justify-between mb-4">
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-2 mb-2">
                                            <input type="checkbox" :value="project.id" x-model="selectedProjects" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                            <h3 class="text-lg font-medium text-title-gray truncate" x-text="project.name"></h3>
                                        </div>
                                        <p class="text-xs text-gray-500 mb-1" x-text="project.code"></p>
                                        <p class="text-sm text-gray-600 line-clamp-2" x-text="project.description"></p>
                                    </div>
                                    <div class="flex flex-col items-end space-y-2">
                                        <span :class="getStatusClass(project.status)" class="px-2 py-1 text-xs rounded-full" x-text="getStatusText(project.status)"></span>
                                        <div class="flex items-center">
                                            <div :class="getPriorityColor(project.priority)" class="w-2 h-2 rounded-full mr-1"></div>
                                            <span class="text-xs text-gray-600" x-text="getPriorityText(project.priority)"></span>
                                        </div>
                                    </div>
                                </div>

                                <div class="grid grid-cols-2 gap-4 mb-4 text-sm">
                                    <div>
                                        <span class="text-gray-500">客户:</span>
                                        <span class="text-gray-900 ml-1" x-text="project.customer"></span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">负责人:</span>
                                        <span class="text-gray-900 ml-1" x-text="project.owner"></span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">类型:</span>
                                        <span class="text-gray-900 ml-1" x-text="getProductTypeText(project.productType)"></span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">截止:</span>
                                        <span class="text-gray-900 ml-1" x-text="project.deadline"></span>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <div class="flex justify-between text-sm mb-1">
                                        <span class="text-gray-600">项目进度</span>
                                        <span class="text-gray-900" x-text="`${project.progress}%`"></span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-primary-500 h-2 rounded-full transition-all duration-300" :style="`width: ${project.progress}%`"></div>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center">
                                    <div class="text-xs text-gray-500">
                                        预算: ¥<span x-text="formatMoney(project.estimatedCost)"></span>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button @click="viewProject(project)" class="text-primary-600 hover:text-primary-700 text-sm">详情</button>
                                        <button @click="editProject(project)" class="text-gray-600 hover:text-gray-700 text-sm">编辑</button>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- 甘特图视图 -->
                <div x-show="viewMode === 'gantt'" class="p-6">
                    <div class="space-y-4">
                        <template x-for="project in paginatedProjects" :key="project.id">
                            <div class="bg-white border border-gray-200 rounded-lg p-4">
                                <div class="grid grid-cols-12 gap-4 items-center">
                                    <div class="col-span-4">
                                        <div class="flex items-center space-x-2">
                                            <input type="checkbox" :value="project.id" x-model="selectedProjects" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                            <div>
                                                <div class="text-sm font-medium text-title-gray" x-text="project.name"></div>
                                                <div class="text-xs text-gray-500" x-text="project.owner"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-span-2 text-sm text-gray-600">
                                        <div x-text="project.createTime"></div>
                                        <div x-text="project.deadline"></div>
                                    </div>
                                    <div class="col-span-4">
                                        <div class="gantt-bar bg-gray-200">
                                            <div class="gantt-progress" :style="`width: ${project.progress}%`"></div>
                                        </div>
                                    </div>
                                    <div class="col-span-2 text-right">
                                        <div class="text-sm text-gray-900" x-text="`${project.progress}%`"></div>
                                        <span :class="getStatusClass(project.status)" class="inline-block px-2 py-1 text-xs rounded-full mt-1" x-text="getStatusText(project.status)"></span>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>

            <!-- 分页 -->
            <div class="bg-white border-t border-gray-200 px-6 py-3">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-600">
                        显示 <span x-text="(currentPage - 1) * pageSize + 1"></span> 到 
                        <span x-text="Math.min(currentPage * pageSize, totalItems)"></span> 条，
                        共 <span x-text="totalItems"></span> 条记录
                    </div>
                    <div class="flex items-center space-x-2">
                        <select x-model="pageSize" @change="updatePagination" class="px-2 py-1 text-sm border border-gray-300 rounded">
                            <option value="10">10条/页</option>
                            <option value="20">20条/页</option>
                            <option value="50">50条/页</option>
                        </select>
                        <button @click="previousPage" :disabled="currentPage === 1"
                                class="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed">
                            上一页
                        </button>
                        <span class="text-sm text-gray-600">
                            第 <span x-text="currentPage"></span> 页，共 <span x-text="totalPages"></span> 页
                        </span>
                        <button @click="nextPage" :disabled="currentPage === totalPages"
                                class="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed">
                            下一页
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧统计信息区域 -->
        <div class="w-80 bg-white border-l border-gray-200">
            <div class="p-6">
                <h3 class="text-lg font-medium text-title-gray mb-4">项目统计</h3>
                
                <!-- 统计卡片 -->
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <div class="bg-primary-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-primary-600" x-text="stats.total"></div>
                        <div class="text-sm text-gray-600">总项目数</div>
                    </div>
                    <div class="bg-success-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-success-600" x-text="stats.completed"></div>
                        <div class="text-sm text-gray-600">已完成</div>
                    </div>
                    <div class="bg-warning-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-warning-600" x-text="stats.reviewing"></div>
                        <div class="text-sm text-gray-600">评审中</div>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-purple-600" x-text="stats.designing"></div>
                        <div class="text-sm text-gray-600">设计中</div>
                    </div>
                </div>

                <!-- 进度分析图表 -->
                <div class="mb-6">
                    <h4 class="text-sm font-medium text-title-gray mb-3">进度分布</h4>
                    <div class="h-48">
                        <canvas id="progressChart"></canvas>
                    </div>
                </div>

                <!-- 最近活动 -->
                <div>
                    <h4 class="text-sm font-medium text-title-gray mb-3">最近活动</h4>
                    <div class="space-y-3 max-h-64 overflow-y-auto">
                        <template x-for="activity in recentActivities" :key="activity.id">
                            <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-md">
                                <div class="w-8 h-8 rounded-full flex items-center justify-center text-white text-xs"
                                     :class="getActivityColor(activity.type)">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
                                    </svg>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="text-sm text-title-gray" x-text="activity.title"></div>
                                    <div class="text-xs text-gray-600 mt-1 truncate" x-text="activity.description"></div>
                                    <div class="flex items-center justify-between mt-2">
                                        <span class="text-xs text-gray-500" x-text="activity.user"></span>
                                        <span class="text-xs text-gray-500" x-text="activity.time"></span>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 新建项目模态框 -->
    <div x-show="showCreateModal" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
         @click="showCreateModal = false">
        <div class="relative top-20 mx-auto p-5 border max-w-4xl shadow-lg rounded-md bg-white" @click.stop>
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-medium text-title-gray">新建设计项目</h3>
                <button @click="showCreateModal = false" class="text-gray-400 hover:text-gray-600">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            
            <form @submit.prevent="createProject" class="space-y-6">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- 基本信息 -->
                    <div class="space-y-4">
                        <h4 class="text-md font-medium text-gray-900 border-b border-gray-200 pb-2">基本信息</h4>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">项目名称 <span class="text-error-500">*</span></label>
                            <input type="text" x-model="createForm.name" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500"
                                   placeholder="请输入项目名称">
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">产品类型 <span class="text-error-500">*</span></label>
                                <select x-model="createForm.productType" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500">
                                    <option value="">请选择产品类型</option>
                                    <option value="curtain_wall">玻璃幕墙</option>
                                    <option value="tempered_glass">钢化玻璃</option>
                                    <option value="laminated_glass">夹胶玻璃</option>
                                    <option value="insulated_glass">中空玻璃</option>
                                    <option value="furniture_glass">家具玻璃</option>
                                    <option value="decoration_glass">装饰玻璃</option>
                                    <option value="special_glass">特种玻璃</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">项目优先级</label>
                                <select x-model="createForm.priority"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500">
                                    <option value="low">低优先级</option>
                                    <option value="medium">中优先级</option>
                                    <option value="high">高优先级</option>
                                </select>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">客户名称 <span class="text-error-500">*</span></label>
                                <select x-model="createForm.customer" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500">
                                    <option value="">请选择客户</option>
                                    <option value="华彩玻璃">华彩玻璃</option>
                                    <option value="金辉建材">金辉建材</option>
                                    <option value="蓝天装饰">蓝天装饰</option>
                                    <option value="绿城地产">绿城地产</option>
                                    <option value="星河置业">星河置业</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">设计负责人 <span class="text-error-500">*</span></label>
                                <select x-model="createForm.owner" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500">
                                    <option value="">请选择负责人</option>
                                    <option value="王工程师">王工程师</option>
                                    <option value="李设计师">李设计师</option>
                                    <option value="张主管">张主管</option>
                                    <option value="刘工程师">刘工程师</option>
                                    <option value="陈设计师">陈设计师</option>
                                </select>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">计划开始时间</label>
                                <input type="date" x-model="createForm.startDate"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">计划完成时间 <span class="text-error-500">*</span></label>
                                <input type="date" x-model="createForm.deadline" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500">
                            </div>
                        </div>
                    </div>

                    <!-- 技术规格 -->
                    <div class="space-y-4">
                        <h4 class="text-md font-medium text-gray-900 border-b border-gray-200 pb-2">技术规格</h4>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">玻璃厚度</label>
                                <input type="text" x-model="createForm.specifications.thickness"
                                       placeholder="如: 6+12A+6mm"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">项目面积</label>
                                <input type="text" x-model="createForm.specifications.area"
                                       placeholder="如: 1200m²"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500">
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">玻璃类型</label>
                            <input type="text" x-model="createForm.specifications.glassType"
                                   placeholder="如: 钢化中空玻璃"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">性能指标</label>
                            <input type="text" x-model="createForm.specifications.performance"
                                   placeholder="如: 抗风压3.0kPa"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">预估成本</label>
                            <input type="number" x-model="createForm.estimatedCost" step="1000"
                                   placeholder="请输入预估成本"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500">
                        </div>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">设计要求</label>
                    <textarea x-model="createForm.requirements" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500"
                              placeholder="请详细描述设计技术要求..."></textarea>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">项目描述</label>
                    <textarea x-model="createForm.description" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500"
                              placeholder="请输入项目描述..."></textarea>
                </div>

                <!-- 底部按钮 -->
                <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                    <button type="button" @click="showCreateModal = false"
                            class="px-6 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                        取消
                    </button>
                    <button type="submit" :disabled="creating"
                            class="px-6 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 disabled:bg-gray-400 disabled:cursor-not-allowed">
                        <span x-show="creating" class="inline-block w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                        <span x-text="creating ? '创建中...' : '确定创建'"></span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 项目分析模态框 -->
    <div x-show="showAnalyticsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" style="display: none;">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/4 shadow-lg rounded-md bg-white max-h-screen overflow-y-auto">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-medium text-title-gray">项目数据分析</h3>
                <button @click="showAnalyticsModal = false" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 项目状态分布 -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-md font-medium text-gray-900 mb-4">项目状态分布</h4>
                    <div class="h-64">
                        <canvas id="statusChart"></canvas>
                    </div>
                </div>

                <!-- 产品类型分布 -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-md font-medium text-gray-900 mb-4">产品类型分布</h4>
                    <div class="h-64">
                        <canvas id="productTypeChart"></canvas>
                    </div>
                </div>

                <!-- 月度项目完成趋势 -->
                <div class="lg:col-span-2 bg-gray-50 rounded-lg p-4">
                    <h4 class="text-md font-medium text-gray-900 mb-4">月度项目完成趋势</h4>
                    <div class="h-64">
                        <canvas id="trendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function designApp() {
            return {
                // UI状态
                showCreateModal: false,
                showAnalyticsModal: false,
                showImportModal: false,
                creating: false,
                selectedProjects: [],
                viewMode: 'table', // table, cards, gantt
                
                // 筛选条件
                statusFilters: [],
                productTypeFilter: '',
                priorityFilter: '',
                customerFilter: '',
                ownerFilter: '',
                dateRange: { start: '', end: '' },
                searchQuery: '',
                sortBy: 'createTime',
                quickFilter: '',
                
                // 分页
                currentPage: 1,
                pageSize: 10,
                totalItems: 0,
                
                // 表单数据
                createForm: {
                    name: '',
                    productType: '',
                    priority: 'medium',
                    customer: '',
                    owner: '',
                    startDate: '',
                    deadline: '',
                    requirements: '',
                    description: '',
                    estimatedCost: '',
                    specifications: {
                        glassType: '',
                        thickness: '',
                        area: '',
                        performance: ''
                    }
                },
                
                // 筛选选项
                statusOptions: [
                    { value: 'designing', label: '设计中', count: 8 },
                    { value: 'reviewing', label: '评审中', count: 5 },
                    { value: 'production_ready', label: '生产就绪', count: 4 },
                    { value: 'completed', label: '已完成', count: 15 },
                    { value: 'paused', label: '已暂停', count: 3 },
                    { value: 'cancelled', label: '已取消', count: 2 }
                ],
                
                priorityOptions: [
                    { value: '', label: '全部优先级', count: 37, color: 'bg-gray-400' },
                    { value: 'high', label: '高优先级', count: 8, color: 'bg-error-500' },
                    { value: 'medium', label: '中优先级', count: 22, color: 'bg-warning-500' },
                    { value: 'low', label: '低优先级', count: 7, color: 'bg-success-500' }
                ],
                
                // 数据
                projects: [
                    {
                        id: 1,
                        code: 'GCP-2024-001',
                        name: '华彩大厦玻璃幕墙设计',
                        description: '50层高级商务楼玻璃幕墙系统设计，采用钢化中空玻璃',
                        productType: 'curtain_wall',
                        status: 'designing',
                        priority: 'high',
                        progress: 65,
                        owner: '王工程师',
                        customer: '华彩玻璃',
                        deadline: '2024-02-15',
                        createTime: '2024-01-05',
                        updateTime: '2024-01-20',
                        specifications: {
                            glassType: '钢化中空玻璃',
                            thickness: '6+12A+6mm',
                            area: '12,500m²',
                            windLoad: '3.0kPa'
                        },
                        teamMembers: ['王工程师', '李设计师', '张技术员'],
                        estimatedCost: 2800000,
                        actualCost: 1950000
                    },
                    {
                        id: 2,
                        code: 'TG-2024-002',
                        name: '防弹玻璃定制设计',
                        description: '银行营业厅防弹玻璃系统，符合GB 17565-2007标准',
                        productType: 'special_glass',
                        status: 'reviewing',
                        priority: 'high',
                        progress: 85,
                        owner: '李设计师',
                        customer: '金辉建材',
                        deadline: '2024-02-28',
                        createTime: '2024-01-08',
                        updateTime: '2024-01-22',
                        specifications: {
                            glassType: '防弹夹胶玻璃',
                            thickness: '32mm',
                            area: '280m²',
                            protectionLevel: 'FB6级'
                        },
                        teamMembers: ['李设计师', '王工程师'],
                        estimatedCost: 560000,
                        actualCost: 485000
                    },
                    {
                        id: 3,
                        code: 'LG-2024-003',
                        name: '汽车天窗夹胶玻璃',
                        description: '高端汽车天窗用夹胶玻璃，具备隔音降噪功能',
                        productType: 'laminated_glass',
                        status: 'production_ready',
                        priority: 'medium',
                        progress: 95,
                        owner: '张主管',
                        customer: '蓝天装饰',
                        deadline: '2024-03-10',
                        createTime: '2024-01-12',
                        updateTime: '2024-01-25',
                        specifications: {
                            glassType: 'PVB夹胶玻璃',
                            thickness: '4.38mm',
                            area: '15,600m²',
                            transmittance: '≥75%'
                        },
                        teamMembers: ['张主管', '陈设计师', '刘工程师'],
                        estimatedCost: 780000,
                        actualCost: 720000
                    },
                    {
                        id: 4,
                        code: 'IG-2024-004',
                        name: '节能中空玻璃系统',
                        description: '绿色建筑用高性能中空玻璃，达到节能65%标准',
                        productType: 'insulated_glass',
                        status: 'completed',
                        priority: 'medium',
                        progress: 100,
                        owner: '刘工程师',
                        customer: '绿城地产',
                        deadline: '2024-01-30',
                        createTime: '2023-12-15',
                        updateTime: '2024-01-30',
                        specifications: {
                            glassType: 'Low-E中空玻璃',
                            thickness: '6+16A+6mm',
                            area: '8,900m²',
                            uValue: '1.4W/(m²·K)'
                        },
                        teamMembers: ['刘工程师', '王工程师'],
                        estimatedCost: 1200000,
                        actualCost: 1150000
                    },
                    {
                        id: 5,
                        code: 'FG-2024-005',
                        name: '家具钢化玻璃台面',
                        description: '餐桌台面用钢化玻璃，厚度10mm，圆角处理',
                        productType: 'furniture_glass',
                        status: 'designing',
                        priority: 'low',
                        progress: 35,
                        owner: '陈设计师',
                        customer: '星河置业',
                        deadline: '2024-03-20',
                        createTime: '2024-01-18',
                        updateTime: '2024-01-26',
                        specifications: {
                            glassType: '钢化玻璃',
                            thickness: '10mm',
                            area: '450m²',
                            edgeWork: '精磨圆角'
                        },
                        teamMembers: ['陈设计师'],
                        estimatedCost: 135000,
                        actualCost: 95000
                    },
                    {
                        id: 6,
                        code: 'DG-2024-006',
                        name: '艺术装饰玻璃墙',
                        description: '酒店大堂装饰玻璃墙，融入传统文化元素',
                        productType: 'decoration_glass',
                        status: 'paused',
                        priority: 'low',
                        progress: 25,
                        owner: '王工程师',
                        customer: '华彩玻璃',
                        deadline: '2024-04-15',
                        createTime: '2024-01-20',
                        updateTime: '2024-01-28',
                        specifications: {
                            glassType: '彩釉钢化玻璃',
                            thickness: '8mm',
                            area: '320m²',
                            pattern: '传统云纹图案'
                        },
                        teamMembers: ['王工程师', '陈设计师'],
                        estimatedCost: 240000,
                        actualCost: 0
                    }
                ],
                
                filteredProjects: [],
                
                stats: {
                    total: 37,
                    active: 17,
                    completed: 15,
                    reviewing: 5,
                    designing: 8,
                    delayed: 4,
                    onTime: 28,
                    ahead: 5
                },
                
                recentActivities: [
                    {
                        id: 1,
                        type: 'review',
                        title: '设计评审通过',
                        description: '防弹玻璃定制设计通过技术评审',
                        user: '李设计师',
                        time: '1小时前',
                        project: 'TG-2024-002'
                    },
                    {
                        id: 2,
                        type: 'progress',
                        title: '项目进度更新',
                        description: '华彩大厦玻璃幕墙设计进度达到65%',
                        user: '王工程师',
                        time: '3小时前',
                        project: 'GCP-2024-001'
                    },
                    {
                        id: 3,
                        type: 'complete',
                        title: '项目完成',
                        description: '节能中空玻璃系统设计项目完成交付',
                        user: '刘工程师',
                        time: '6小时前',
                        project: 'IG-2024-004'
                    },
                    {
                        id: 4,
                        type: 'create',
                        title: '新建项目',
                        description: '创建艺术装饰玻璃墙设计项目',
                        user: '王工程师',
                        time: '1天前',
                        project: 'DG-2024-006'
                    },
                    {
                        id: 5,
                        type: 'milestone',
                        title: '里程碑达成',
                        description: '汽车天窗夹胶玻璃进入生产准备阶段',
                        user: '张主管',
                        time: '2天前',
                        project: 'LG-2024-003'
                    },
                    {
                        id: 6,
                        type: 'issue',
                        title: '问题反馈',
                        description: '家具钢化玻璃台面设计需要修改规格',
                        user: '陈设计师',
                        time: '3天前',
                        project: 'FG-2024-005'
                    }
                ],
                
                init() {
                    this.filteredProjects = [...this.projects];
                    this.totalItems = this.projects.length;
                    this.$nextTick(() => {
                        this.initCharts();
                    });
                },
                
                get totalPages() {
                    return Math.ceil(this.totalItems / this.pageSize);
                },
                
                get isAllSelected() {
                    return this.selectedProjects.length === this.paginatedProjects.length && this.paginatedProjects.length > 0;
                },
                
                get paginatedProjects() {
                    const start = (this.currentPage - 1) * this.pageSize;
                    const end = start + this.pageSize;
                    return this.filteredProjects.slice(start, end);
                },
                
                applyQuickFilter(filterType) {
                    this.quickFilter = this.quickFilter === filterType ? '' : filterType;
                    this.resetFilters();
                    
                    const today = new Date();
                    const oneWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
                    
                    switch (filterType) {
                        case 'urgent':
                            this.priorityFilter = 'high';
                            break;
                        case 'delayed':
                            this.filteredProjects = this.projects.filter(project => 
                                new Date(project.deadline) < today && project.status !== 'completed'
                            );
                            this.totalItems = this.filteredProjects.length;
                            this.currentPage = 1;
                            return;
                        case 'thisWeek':
                            this.filteredProjects = this.projects.filter(project => {
                                const deadline = new Date(project.deadline);
                                return deadline >= today && deadline <= oneWeek;
                            });
                            this.totalItems = this.filteredProjects.length;
                            this.currentPage = 1;
                            return;
                    }
                    
                    this.filterProjects();
                },
                
                resetFilters() {
                    this.statusFilters = [];
                    this.productTypeFilter = '';
                    this.priorityFilter = '';
                    this.customerFilter = '';
                    this.ownerFilter = '';
                    this.dateRange = { start: '', end: '' };
                    this.searchQuery = '';
                    if (!this.quickFilter) {
                        this.filterProjects();
                    }
                },
                
                filterProjects() {
                    let filtered = [...this.projects];
                    
                    // 状态筛选
                    if (this.statusFilters.length > 0) {
                        filtered = filtered.filter(project => this.statusFilters.includes(project.status));
                    }
                    
                    // 产品类型筛选
                    if (this.productTypeFilter) {
                        filtered = filtered.filter(project => project.productType === this.productTypeFilter);
                    }
                    
                    // 优先级筛选
                    if (this.priorityFilter) {
                        filtered = filtered.filter(project => project.priority === this.priorityFilter);
                    }
                    
                    // 客户筛选
                    if (this.customerFilter) {
                        filtered = filtered.filter(project => project.customer === this.customerFilter);
                    }
                    
                    // 负责人筛选
                    if (this.ownerFilter) {
                        filtered = filtered.filter(project => project.owner === this.ownerFilter);
                    }
                    
                    // 时间筛选
                    if (this.dateRange.start) {
                        filtered = filtered.filter(project => project.createTime >= this.dateRange.start);
                    }
                    if (this.dateRange.end) {
                        filtered = filtered.filter(project => project.createTime <= this.dateRange.end);
                    }
                    
                    // 搜索筛选
                    if (this.searchQuery) {
                        const query = this.searchQuery.toLowerCase();
                        filtered = filtered.filter(project => 
                            project.name.toLowerCase().includes(query) ||
                            project.code.toLowerCase().includes(query) ||
                            project.customer.toLowerCase().includes(query) ||
                            project.description.toLowerCase().includes(query)
                        );
                    }
                    
                    this.filteredProjects = filtered;
                    this.totalItems = filtered.length;
                    this.currentPage = 1;
                },
                
                handleSearch() {
                    this.filterProjects();
                },
                
                sortProjects() {
                    this.filteredProjects.sort((a, b) => {
                        switch (this.sortBy) {
                            case 'createTime':
                                return new Date(b.createTime) - new Date(a.createTime);
                            case 'updateTime':
                                return new Date(b.updateTime) - new Date(a.updateTime);
                            case 'progress':
                                return b.progress - a.progress;
                            case 'deadline':
                                return new Date(a.deadline) - new Date(b.deadline);
                            case 'priority':
                                const priorityOrder = { high: 3, medium: 2, low: 1 };
                                return priorityOrder[b.priority] - priorityOrder[a.priority];
                            default:
                                return 0;
                        }
                    });
                },
                
                toggleSelectAll() {
                    if (this.isAllSelected) {
                        this.selectedProjects = [];
                    } else {
                        this.selectedProjects = this.paginatedProjects.map(p => p.id);
                    }
                },
                
                updatePagination() {
                    this.currentPage = 1;
                },
                
                previousPage() {
                    if (this.currentPage > 1) {
                        this.currentPage--;
                    }
                },
                
                nextPage() {
                    if (this.currentPage < this.totalPages) {
                        this.currentPage++;
                    }
                },
                
                // 格式化方法
                formatMoney(amount) {
                    return new Intl.NumberFormat('zh-CN').format(amount);
                },
                
                isOverdue(deadline) {
                    return new Date(deadline) < new Date();
                },
                
                isUpcoming(deadline) {
                    const now = new Date();
                    const deadlineDate = new Date(deadline);
                    const diffTime = deadlineDate.getTime() - now.getTime();
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                    return diffDays > 0 && diffDays <= 7;
                },
                
                getTimeStatus(deadline) {
                    const now = new Date();
                    const deadlineDate = new Date(deadline);
                    const diffTime = deadlineDate.getTime() - now.getTime();
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                    
                    if (diffDays < 0) {
                        return `逾期${Math.abs(diffDays)}天`;
                    } else if (diffDays === 0) {
                        return '今天截止';
                    } else if (diffDays <= 7) {
                        return `${diffDays}天后截止`;
                    } else {
                        return `${diffDays}天后截止`;
                    }
                },
                
                getStatusText(status) {
                    const statusMap = {
                        'designing': '设计中',
                        'reviewing': '评审中',
                        'production_ready': '生产就绪',
                        'completed': '已完成',
                        'paused': '已暂停',
                        'cancelled': '已取消'
                    };
                    return statusMap[status] || '未知';
                },
                
                getStatusClass(status) {
                    const classMap = {
                        'designing': 'bg-blue-100 text-blue-800',
                        'reviewing': 'bg-warning-100 text-warning-800',
                        'production_ready': 'bg-purple-100 text-purple-800',
                        'completed': 'bg-success-100 text-success-800',
                        'paused': 'bg-gray-100 text-gray-800',
                        'cancelled': 'bg-error-100 text-error-800'
                    };
                    return classMap[status] || 'bg-gray-100 text-gray-800';
                },
                
                getPriorityText(priority) {
                    const priorityMap = {
                        'high': '高优先级',
                        'medium': '中优先级',
                        'low': '低优先级'
                    };
                    return priorityMap[priority] || '未知';
                },
                
                getPriorityColor(priority) {
                    const colorMap = {
                        'high': 'bg-error-500',
                        'medium': 'bg-warning-500',
                        'low': 'bg-success-500'
                    };
                    return colorMap[priority] || 'bg-gray-400';
                },
                
                getProductTypeText(type) {
                    const typeMap = {
                        'curtain_wall': '玻璃幕墙',
                        'tempered_glass': '钢化玻璃',
                        'laminated_glass': '夹胶玻璃',
                        'insulated_glass': '中空玻璃',
                        'furniture_glass': '家具玻璃',
                        'decoration_glass': '装饰玻璃',
                        'special_glass': '特种玻璃'
                    };
                    return typeMap[type] || '未知类型';
                },
                
                getActivityColor(type) {
                    const colorMap = {
                        'review': 'bg-warning-500',
                        'progress': 'bg-primary-500',
                        'complete': 'bg-success-500',
                        'create': 'bg-blue-500',
                        'milestone': 'bg-purple-500',
                        'issue': 'bg-error-500'
                    };
                    return colorMap[type] || 'bg-gray-500';
                },
                
                // 操作方法
                viewProject(project) {
                    window.open(`project-detail.html?id=${project.id}`, '_blank');
                },
                
                editProject(project) {
                    // 编辑项目逻辑
                    console.log('编辑项目:', project);
                },
                
                reviewProject(project) {
                    window.open(`review-workflow.html?id=${project.id}`, '_blank');
                },
                
                batchReview() {
                    if (this.selectedProjects.length === 0) return;
                    console.log(`批量评审 ${this.selectedProjects.length} 个项目`);
                },
                
                batchExport() {
                    if (this.selectedProjects.length === 0) return;
                    console.log(`批量导出 ${this.selectedProjects.length} 个项目`);
                },
                
                exportProjects() {
                    console.log('导出所有项目');
                },
                
                createProject() {
                    this.creating = true;
                    
                    // 生成项目编码
                    const now = new Date();
                    const year = now.getFullYear();
                    const month = String(now.getMonth() + 1).padStart(2, '0');
                    const day = String(now.getDate()).padStart(2, '0');
                    const random = String(Math.floor(Math.random() * 1000)).padStart(3, '0');
                    
                    const newProject = {
                        id: Date.now(),
                        code: `${this.createForm.productType.toUpperCase()}-${year}-${random}`,
                        name: this.createForm.name,
                        description: this.createForm.description,
                        productType: this.createForm.productType,
                        status: 'designing',
                        priority: this.createForm.priority,
                        progress: 0,
                        owner: this.createForm.owner,
                        customer: this.createForm.customer,
                        deadline: this.createForm.deadline,
                        createTime: `${year}-${month}-${day}`,
                        updateTime: `${year}-${month}-${day}`,
                        specifications: this.createForm.specifications,
                        teamMembers: [this.createForm.owner],
                        estimatedCost: parseInt(this.createForm.estimatedCost) || 0,
                        actualCost: 0
                    };
                    
                    // 模拟创建过程
                    setTimeout(() => {
                        this.projects.unshift(newProject);
                        this.filterProjects();
                        this.creating = false;
                        this.showCreateModal = false;
                        
                        // 重置表单
                        this.createForm = {
                            name: '',
                            productType: '',
                            priority: 'medium',
                            customer: '',
                            owner: '',
                            startDate: '',
                            deadline: '',
                            requirements: '',
                            description: '',
                            estimatedCost: '',
                            specifications: {
                                glassType: '',
                                thickness: '',
                                area: '',
                                performance: ''
                            }
                        };
                        
                        // 更新统计
                        this.stats.total++;
                        this.stats.active++;
                        this.stats.designing++;
                        
                        console.log('设计项目创建成功！');
                    }, 2000);
                },
                
                initCharts() {
                    // 进度分布图表
                    const progressCtx = document.getElementById('progressChart');
                    if (progressCtx) {
                        new Chart(progressCtx, {
                            type: 'doughnut',
                            data: {
                                labels: ['0-25%', '26-50%', '51-75%', '76-100%'],
                                datasets: [{
                                    data: [5, 8, 12, 12],
                                    backgroundColor: ['#fee2e2', '#fef3c7', '#dbeafe', '#dcfce7'],
                                    borderColor: ['#ef4444', '#f59e0b', '#3b82f6', '#22c55e'],
                                    borderWidth: 2
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        position: 'bottom'
                                    }
                                }
                            }
                        });
                    }
                }
            }
        }
    </script>
</body>
</html>