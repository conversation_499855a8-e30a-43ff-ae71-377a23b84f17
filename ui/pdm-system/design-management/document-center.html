<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计文档中心 - 玻璃深加工ERP系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1890FF',
                        success: '#52C41A',
                        warning: '#FAAD14',
                        error: '#F5222D',
                        'title-gray': '#262626',
                        'body-gray': '#595959',
                        'aux-gray': '#8C8C8C',
                        'disabled-gray': '#BFBFBF',
                        'border-gray': '#D9D9D9',
                        'bg-gray': '#FAFAFA'
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', 'sans-serif']
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-bg-gray font-chinese" x-data="documentCenterApp()">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-border-gray h-16">
        <div class="flex items-center justify-between px-6 h-full">
            <div class="flex items-center space-x-4">
                <button @click="goBack()" class="text-aux-gray hover:text-title-gray transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                </button>
                <div>
                    <h1 class="text-xl font-medium text-title-gray">设计文档中心</h1>
                    <div class="flex items-center space-x-2 mt-1">
                        <span class="text-sm text-aux-gray" x-text="project.name"></span>
                        <span class="text-sm text-aux-gray">|</span>
                        <span class="text-sm text-aux-gray" x-text="project.code"></span>
                    </div>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <!-- 文档统计 -->
                <div class="flex items-center space-x-4 text-sm">
                    <div class="flex items-center space-x-1">
                        <div class="w-2 h-2 bg-primary rounded-full"></div>
                        <span class="text-aux-gray">文档:</span>
                        <span class="font-medium text-title-gray" x-text="documentStats.total"></span>
                    </div>
                    <div class="flex items-center space-x-1">
                        <div class="w-2 h-2 bg-success rounded-full"></div>
                        <span class="text-aux-gray">已批准:</span>
                        <span class="font-medium text-title-gray" x-text="documentStats.approved"></span>
                    </div>
                    <div class="flex items-center space-x-1">
                        <div class="w-2 h-2 bg-warning rounded-full"></div>
                        <span class="text-aux-gray">评审中:</span>
                        <span class="font-medium text-title-gray" x-text="documentStats.reviewing"></span>
                    </div>
                </div>
                
                <!-- 操作按钮 -->
                <div class="flex space-x-2">
                    <button @click="showUploadModal = true"
                            class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600 transition-colors">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                        </svg>
                        上传文档
                    </button>
                    <button @click="createFolder()"
                            class="px-4 py-2 bg-success text-white rounded-md hover:bg-green-600 transition-colors">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        新建文件夹
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main class="flex h-full">
        <!-- 左侧文件夹树 -->
        <div class="w-64 bg-white border-r border-border-gray">
            <div class="p-4">
                <h3 class="text-sm font-medium text-title-gray mb-4">文档分类</h3>
                <div class="space-y-1">
                    <template x-for="folder in documentFolders" :key="folder.id">
                        <div class="select-none">
                            <div @click="selectFolder(folder)"
                                 class="flex items-center space-x-2 p-2 rounded cursor-pointer transition-colors"
                                 :class="selectedFolderId === folder.id ? 'bg-primary text-white' : 'hover:bg-gray-50'">
                                <button @click.stop="toggleFolder(folder)" x-show="folder.hasChildren">
                                    <svg class="w-4 h-4 transition-transform" 
                                         :class="folder.isExpanded ? 'rotate-90' : ''"
                                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                    </svg>
                                </button>
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"/>
                                </svg>
                                <span class="text-sm" x-text="folder.name"></span>
                                <span class="text-xs ml-auto" 
                                      :class="selectedFolderId === folder.id ? 'text-blue-100' : 'text-aux-gray'"
                                      x-text="folder.documentCount"></span>
                            </div>
                            
                            <!-- 子文件夹 -->
                            <div x-show="folder.isExpanded && folder.children" class="ml-6 mt-1 space-y-1">
                                <template x-for="child in folder.children" :key="child.id">
                                    <div @click="selectFolder(child)"
                                         class="flex items-center space-x-2 p-2 rounded cursor-pointer transition-colors"
                                         :class="selectedFolderId === child.id ? 'bg-primary text-white' : 'hover:bg-gray-50'">
                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"/>
                                        </svg>
                                        <span class="text-sm" x-text="child.name"></span>
                                        <span class="text-xs ml-auto"
                                              :class="selectedFolderId === child.id ? 'text-blue-100' : 'text-aux-gray'"
                                              x-text="child.documentCount"></span>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>

        <!-- 中间文档列表区域 -->
        <div class="flex-1 flex flex-col">
            <!-- 工具栏 -->
            <div class="bg-white border-b border-border-gray px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <h3 class="text-lg font-medium text-title-gray" x-text="getSelectedFolderName()"></h3>
                        <div class="flex items-center space-x-2">
                            <button @click="viewMode = 'list'"
                                    class="p-2 rounded transition-colors"
                                    :class="viewMode === 'list' ? 'bg-primary text-white' : 'text-aux-gray hover:bg-gray-50'">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"/>
                                </svg>
                            </button>
                            <button @click="viewMode = 'grid'"
                                    class="p-2 rounded transition-colors"
                                    :class="viewMode === 'grid' ? 'bg-primary text-white' : 'text-aux-gray hover:bg-gray-50'">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <!-- 搜索框 -->
                        <div class="relative">
                            <input type="text" x-model="searchQuery" @input="filterDocuments"
                                   placeholder="搜索文档..."
                                   class="w-64 pl-8 pr-4 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                            <svg class="absolute left-2.5 top-2.5 h-4 w-4 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                        
                        <!-- 筛选器 -->
                        <select x-model="statusFilter" @change="filterDocuments"
                                class="px-3 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                            <option value="">全部状态</option>
                            <option value="draft">草稿</option>
                            <option value="reviewing">评审中</option>
                            <option value="approved">已批准</option>
                            <option value="rejected">已驳回</option>
                        </select>
                        
                        <select x-model="typeFilter" @change="filterDocuments"
                                class="px-3 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                            <option value="">全部类型</option>
                            <option value="技术文档">技术文档</option>
                            <option value="CAD图纸">CAD图纸</option>
                            <option value="测试报告">测试报告</option>
                            <option value="规格书">规格书</option>
                            <option value="检验标准">检验标准</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 文档列表内容 -->
            <div class="flex-1 overflow-auto bg-white">
                <!-- 列表视图 -->
                <div x-show="viewMode === 'list'">
                    <table class="min-w-full">
                        <thead class="bg-bg-gray sticky top-0">
                            <tr>
                                <th class="px-6 py-3 text-left">
                                    <input type="checkbox" @change="toggleSelectAll" :checked="isAllSelected">
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">文档名称</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">类型</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">版本</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">状态</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">大小</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">修改时间</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">操作人</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-border-gray">
                            <template x-for="document in filteredDocuments" :key="document.id">
                                <tr class="hover:bg-gray-50" :class="selectedDocuments.includes(document.id) ? 'bg-blue-50' : ''">
                                    <td class="px-6 py-4">
                                        <input type="checkbox" :value="document.id" x-model="selectedDocuments">
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 rounded flex items-center justify-center"
                                                 :class="getFileIconColor(document.extension)">
                                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
                                                </svg>
                                            </div>
                                            <div>
                                                <div class="text-sm font-medium text-title-gray cursor-pointer hover:text-primary"
                                                     @click="previewDocument(document)"
                                                     x-text="document.name"></div>
                                                <div class="text-xs text-aux-gray" x-text="document.description"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-sm text-body-gray" x-text="document.type"></span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center space-x-2">
                                            <span class="text-sm font-medium text-title-gray" x-text="document.version"></span>
                                            <button x-show="document.hasHistory" @click="showVersionHistory(document)"
                                                    class="text-xs text-primary hover:text-blue-600">历史</button>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs rounded-full"
                                              :class="getDocumentStatusClass(document.status)"
                                              x-text="getDocumentStatusText(document.status)"></span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-body-gray" x-text="document.size"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-body-gray" x-text="document.updateTime"></td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                                                <span class="text-xs text-primary" x-text="document.author.charAt(0)"></span>
                                            </div>
                                            <span class="text-sm text-body-gray" x-text="document.author"></span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <div class="flex space-x-2">
                                            <button @click="previewDocument(document)" class="text-primary hover:text-blue-600">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                                </svg>
                                            </button>
                                            <button @click="downloadDocument(document)" class="text-success hover:text-green-600">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                                </svg>
                                            </button>
                                            <div class="relative" x-data="{ showMenu: false }">
                                                <button @click="showMenu = !showMenu" class="text-aux-gray hover:text-title-gray">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"/>
                                                    </svg>
                                                </button>
                                                <div x-show="showMenu" @click.away="showMenu = false"
                                                     class="absolute right-0 mt-2 w-32 bg-white border border-border-gray rounded-md shadow-lg z-10">
                                                    <button @click="editDocument(document); showMenu = false" 
                                                            class="block w-full text-left px-4 py-2 text-sm text-body-gray hover:bg-gray-50">编辑</button>
                                                    <button @click="shareDocument(document); showMenu = false"
                                                            class="block w-full text-left px-4 py-2 text-sm text-body-gray hover:bg-gray-50">分享</button>
                                                    <button @click="moveDocument(document); showMenu = false"
                                                            class="block w-full text-left px-4 py-2 text-sm text-body-gray hover:bg-gray-50">移动</button>
                                                    <hr class="my-1">
                                                    <button @click="deleteDocument(document); showMenu = false"
                                                            class="block w-full text-left px-4 py-2 text-sm text-error hover:bg-gray-50">删除</button>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>

                <!-- 网格视图 -->
                <div x-show="viewMode === 'grid'" class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                        <template x-for="document in filteredDocuments" :key="document.id">
                            <div class="bg-white border border-border-gray rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                                 @click="previewDocument(document)">
                                <div class="flex items-start justify-between mb-3">
                                    <div class="w-10 h-10 rounded-lg flex items-center justify-center"
                                         :class="getFileIconColor(document.extension)">
                                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    <input type="checkbox" :value="document.id" x-model="selectedDocuments" @click.stop>
                                </div>
                                <div class="mb-2">
                                    <h4 class="text-sm font-medium text-title-gray truncate" x-text="document.name"></h4>
                                    <p class="text-xs text-aux-gray mt-1 line-clamp-2" x-text="document.description"></p>
                                </div>
                                <div class="flex items-center justify-between text-xs text-aux-gray">
                                    <span x-text="document.size"></span>
                                    <span class="px-2 py-1 rounded-full"
                                          :class="getDocumentStatusClass(document.status)"
                                          x-text="getDocumentStatusText(document.status)"></span>
                                </div>
                                <div class="flex items-center justify-between mt-3">
                                    <div class="flex items-center">
                                        <div class="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center mr-1">
                                            <span class="text-xs text-primary" x-text="document.author.charAt(0)"></span>
                                        </div>
                                        <span class="text-xs text-aux-gray" x-text="document.author"></span>
                                    </div>
                                    <span class="text-xs text-aux-gray" x-text="document.updateTime"></span>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
                
                <!-- 空状态 -->
                <div x-show="filteredDocuments.length === 0" class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-title-gray">暂无文档</h3>
                    <p class="mt-1 text-sm text-aux-gray">开始上传您的第一个设计文档</p>
                    <div class="mt-6">
                        <button @click="showUploadModal = true" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600">
                            上传文档
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧属性面板 -->
        <div class="w-80 bg-white border-l border-border-gray" x-show="selectedDocument">
            <div class="p-6">
                <!-- 文档详情 -->
                <div class="mb-6">
                    <h3 class="text-sm font-medium text-title-gray mb-3">文档详情</h3>
                    <div x-show="selectedDocument" class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 rounded-lg flex items-center justify-center"
                                 :class="getFileIconColor(selectedDocument?.extension)">
                                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-title-gray" x-text="selectedDocument?.name"></h4>
                                <p class="text-xs text-aux-gray" x-text="selectedDocument?.type"></p>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div>
                                <span class="text-aux-gray">版本:</span>
                                <span class="text-body-gray ml-1" x-text="selectedDocument?.version"></span>
                            </div>
                            <div>
                                <span class="text-aux-gray">大小:</span>
                                <span class="text-body-gray ml-1" x-text="selectedDocument?.size"></span>
                            </div>
                            <div>
                                <span class="text-aux-gray">状态:</span>
                                <span class="px-2 py-1 text-xs rounded-full ml-1"
                                      :class="getDocumentStatusClass(selectedDocument?.status)"
                                      x-text="getDocumentStatusText(selectedDocument?.status)"></span>
                            </div>
                            <div>
                                <span class="text-aux-gray">作者:</span>
                                <span class="text-body-gray ml-1" x-text="selectedDocument?.author"></span>
                            </div>
                        </div>
                        
                        <div class="text-sm">
                            <span class="text-aux-gray">描述:</span>
                            <p class="text-body-gray mt-1" x-text="selectedDocument?.description || '暂无描述'"></p>
                        </div>
                        
                        <div class="text-sm">
                            <span class="text-aux-gray">创建时间:</span>
                            <span class="text-body-gray ml-1" x-text="selectedDocument?.createTime"></span>
                        </div>
                        
                        <div class="text-sm">
                            <span class="text-aux-gray">修改时间:</span>
                            <span class="text-body-gray ml-1" x-text="selectedDocument?.updateTime"></span>
                        </div>
                    </div>
                </div>

                <!-- 协作信息 -->
                <div class="mb-6" x-show="selectedDocument?.collaborators">
                    <h3 class="text-sm font-medium text-title-gray mb-3">协作人员</h3>
                    <div class="space-y-2">
                        <template x-for="collaborator in selectedDocument?.collaborators" :key="collaborator.id">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                                        <span class="text-xs text-primary" x-text="collaborator.name.charAt(0)"></span>
                                    </div>
                                    <span class="text-sm text-body-gray" x-text="collaborator.name"></span>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <span class="text-xs text-aux-gray" x-text="collaborator.role"></span>
                                    <div class="w-2 h-2 rounded-full" :class="collaborator.online ? 'bg-success' : 'bg-gray-300'"></div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="mb-6">
                    <h3 class="text-sm font-medium text-title-gray mb-3">快速操作</h3>
                    <div class="space-y-2">
                        <button @click="previewDocument(selectedDocument)" 
                                class="w-full flex items-center justify-center space-x-2 px-3 py-2 text-sm bg-primary text-white rounded-md hover:bg-blue-600">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                            </svg>
                            <span>预览</span>
                        </button>
                        <button @click="downloadDocument(selectedDocument)"
                                class="w-full flex items-center justify-center space-x-2 px-3 py-2 text-sm bg-success text-white rounded-md hover:bg-green-600">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                            <span>下载</span>
                        </button>
                        <button @click="shareDocument(selectedDocument)"
                                class="w-full flex items-center justify-center space-x-2 px-3 py-2 text-sm bg-warning text-white rounded-md hover:bg-yellow-600">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"/>
                            </svg>
                            <span>分享</span>
                        </button>
                    </div>
                </div>

                <!-- 版本历史 -->
                <div x-show="selectedDocument?.hasHistory">
                    <h3 class="text-sm font-medium text-title-gray mb-3">版本历史</h3>
                    <div class="space-y-2 max-h-32 overflow-y-auto">
                        <template x-for="version in documentVersions" :key="version.id">
                            <div class="flex items-center justify-between p-2 bg-gray-50 rounded text-xs">
                                <div>
                                    <div class="font-medium text-title-gray" x-text="version.version"></div>
                                    <div class="text-aux-gray" x-text="version.author"></div>
                                </div>
                                <div class="text-aux-gray" x-text="version.date"></div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 上传文档模态框 -->
    <div x-show="showUploadModal" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
         @click="showUploadModal = false">
        <div class="relative top-20 mx-auto p-5 border max-w-2xl shadow-lg rounded-md bg-white" @click.stop>
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-title-gray">上传文档</h3>
                <button @click="showUploadModal = false" class="text-aux-gray hover:text-title-gray">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            
            <form @submit.prevent="uploadDocument()" class="space-y-4">
                <!-- 文件上传区域 -->
                <div class="border-2 border-dashed border-border-gray rounded-lg p-8 text-center">
                    <svg class="mx-auto h-12 w-12 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                    </svg>
                    <div class="mt-4">
                        <label for="file-upload" class="cursor-pointer">
                            <span class="mt-2 block text-sm font-medium text-title-gray">
                                点击上传或拖拽文件到此处
                            </span>
                            <input id="file-upload" name="file-upload" type="file" class="sr-only" multiple>
                        </label>
                        <p class="mt-1 text-xs text-aux-gray">
                            支持 PDF, DOC, DOCX, DWG, JPG, PNG 等格式，单个文件最大50MB
                        </p>
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-title-gray mb-2">文档类型</label>
                        <select x-model="uploadForm.type" required
                                class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                            <option value="">请选择</option>
                            <option value="技术文档">技术文档</option>
                            <option value="CAD图纸">CAD图纸</option>
                            <option value="测试报告">测试报告</option>
                            <option value="规格书">规格书</option>
                            <option value="检验标准">检验标准</option>
                            <option value="工艺文件">工艺文件</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-title-gray mb-2">存储文件夹</label>
                        <select x-model="uploadForm.folderId" required
                                class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                            <option value="">请选择</option>
                            <template x-for="folder in documentFolders" :key="folder.id">
                                <option :value="folder.id" x-text="folder.name"></option>
                            </template>
                        </select>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-title-gray mb-2">文档描述</label>
                    <textarea x-model="uploadForm.description" rows="3"
                              class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                              placeholder="请描述文档的主要内容和用途..."></textarea>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-title-gray mb-2">版本号</label>
                        <input type="text" x-model="uploadForm.version"
                               class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                               placeholder="如: v1.0">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-title-gray mb-2">保密级别</label>
                        <select x-model="uploadForm.securityLevel"
                                class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                            <option value="public">公开</option>
                            <option value="internal">内部</option>
                            <option value="confidential">机密</option>
                        </select>
                    </div>
                </div>

                <!-- 协作设置 -->
                <div>
                    <label class="block text-sm font-medium text-title-gray mb-2">协作人员</label>
                    <div class="space-y-2">
                        <template x-for="user in teamMembers" :key="user.id">
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" :value="user.id" x-model="uploadForm.collaborators">
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                                        <span class="text-xs text-primary" x-text="user.name.charAt(0)"></span>
                                    </div>
                                    <span class="text-sm text-body-gray" x-text="user.name"></span>
                                    <span class="text-xs text-aux-gray" x-text="user.role"></span>
                                </div>
                            </label>
                        </template>
                    </div>
                </div>

                <!-- 底部按钮 -->
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" @click="showUploadModal = false"
                            class="px-4 py-2 text-sm font-medium text-body-gray bg-white border border-border-gray rounded-md hover:bg-gray-50">
                        取消
                    </button>
                    <button type="submit" :disabled="uploading"
                            class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-blue-600 disabled:bg-disabled-gray">
                        <span x-show="uploading" class="inline-block w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                        <span x-text="uploading ? '上传中...' : '确定上传'"></span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function documentCenterApp() {
            return {
                viewMode: 'list',
                searchQuery: '',
                statusFilter: '',
                typeFilter: '',
                selectedFolderId: 1,
                selectedDocuments: [],
                selectedDocument: null,
                showUploadModal: false,
                uploading: false,
                
                project: {
                    id: 1,
                    code: 'PRJ-2025-001',
                    name: '高强度钢化玻璃幕墙系统设计'
                },
                
                documentStats: {
                    total: 24,
                    approved: 15,
                    reviewing: 6,
                    draft: 3
                },
                
                uploadForm: {
                    type: '',
                    folderId: '',
                    description: '',
                    version: '',
                    securityLevel: 'internal',
                    collaborators: []
                },
                
                documentFolders: [
                    { id: 1, name: '技术文档', documentCount: 8, hasChildren: true, isExpanded: false,
                      children: [
                        { id: 11, name: '设计方案', documentCount: 4 },
                        { id: 12, name: '技术规格', documentCount: 3 },
                        { id: 13, name: '计算书', documentCount: 1 }
                      ]
                    },
                    { id: 2, name: 'CAD图纸', documentCount: 12, hasChildren: true, isExpanded: false,
                      children: [
                        { id: 21, name: '结构图', documentCount: 6 },
                        { id: 22, name: '详图', documentCount: 4 },
                        { id: 23, name: '装配图', documentCount: 2 }
                      ]
                    },
                    { id: 3, name: '测试报告', documentCount: 6, hasChildren: false },
                    { id: 4, name: '质量文件', documentCount: 4, hasChildren: false },
                    { id: 5, name: '工艺文件', documentCount: 3, hasChildren: false }
                ],
                
                documents: [
                    {
                        id: 1, name: '高强度钢化玻璃技术方案v2.1.pdf', type: '技术文档', extension: 'pdf',
                        version: 'v2.1', status: 'approved', size: '3.2MB', folderId: 11,
                        description: '采用Low-E镀膜技术的高强度钢化玻璃技术方案，包含材料规格、加工工艺、性能指标等详细说明',
                        author: '张工', createTime: '2025-01-20 10:30', updateTime: '2025-02-10 15:45',
                        hasHistory: true,
                        collaborators: [
                            { id: 1, name: '李工', role: '审核人', online: true },
                            { id: 2, name: '王工', role: '协作者', online: false }
                        ]
                    },
                    {
                        id: 2, name: '幕墙结构设计图v1.5.dwg', type: 'CAD图纸', extension: 'dwg',
                        version: 'v1.5', status: 'reviewing', size: '8.7MB', folderId: 21,
                        description: '幕墙主体结构设计图纸，包含框架尺寸、连接节点、承重计算等',
                        author: '李工', createTime: '2025-01-25 14:20', updateTime: '2025-02-12 09:30',
                        hasHistory: true,
                        collaborators: [
                            { id: 1, name: '张工', role: '审核人', online: true },
                            { id: 3, name: '赵工', role: '协作者', online: true }
                        ]
                    },
                    {
                        id: 3, name: '材料性能测试报告.pdf', type: '测试报告', extension: 'pdf',
                        version: 'v1.0', status: 'approved', size: '2.1MB', folderId: 3,
                        description: '6mm超白钢化玻璃的力学性能、热性能、光学性能测试报告',
                        author: '赵工', createTime: '2025-02-01 11:15', updateTime: '2025-02-01 11:15',
                        hasHistory: false,
                        collaborators: [
                            { id: 1, name: '张工', role: '审核人', online: true }
                        ]
                    },
                    {
                        id: 4, name: '中空玻璃工艺规程.docx', type: '工艺文件', extension: 'docx',
                        version: 'v1.2', status: 'approved', size: '1.8MB', folderId: 5,
                        description: '中空玻璃生产工艺流程、质量控制点、检验标准等详细规程',
                        author: '王工', createTime: '2025-01-28 16:40', updateTime: '2025-02-08 13:20',
                        hasHistory: true,
                        collaborators: [
                            { id: 2, name: '钱工', role: '协作者', online: false }
                        ]
                    },
                    {
                        id: 5, name: '安装节点详图.dwg', type: 'CAD图纸', extension: 'dwg',
                        version: 'v1.0', status: 'draft', size: '4.3MB', folderId: 22,
                        description: '幕墙安装关键节点详图，包含密封、固定、排水等构造细节',
                        author: '钱工', createTime: '2025-02-11 10:00', updateTime: '2025-02-12 16:30',
                        hasHistory: false,
                        collaborators: []
                    },
                    {
                        id: 6, name: '质量检验标准v1.1.pdf', type: '质量文件', extension: 'pdf',
                        version: 'v1.1', status: 'approved', size: '1.5MB', folderId: 4,
                        description: '玻璃幕墙工程质量检验标准，包含材料检验、安装检验、验收标准',
                        author: '孙工', createTime: '2025-01-30 09:45', updateTime: '2025-02-05 14:15',
                        hasHistory: true,
                        collaborators: [
                            { id: 1, name: '张工', role: '审核人', online: true },
                            { id: 4, name: '周工', role: '协作者', online: true }
                        ]
                    }
                ],
                
                teamMembers: [
                    { id: 1, name: '张工', role: '项目负责人' },
                    { id: 2, name: '李工', role: '结构设计师' },
                    { id: 3, name: '王工', role: '工艺工程师' },
                    { id: 4, name: '赵工', role: '质量工程师' },
                    { id: 5, name: '钱工', role: '测试工程师' },
                    { id: 6, name: '孙工', role: '技术专家' },
                    { id: 7, name: '周工', role: '工艺专家' }
                ],
                
                documentVersions: [
                    { id: 1, version: 'v2.1', author: '张工', date: '2025-02-10', comments: '优化Low-E镀膜工艺参数' },
                    { id: 2, version: 'v2.0', author: '张工', date: '2025-02-05', comments: '添加性能测试数据' },
                    { id: 3, version: 'v1.2', author: '李工', date: '2025-01-28', comments: '修订技术规格' },
                    { id: 4, version: 'v1.1', author: '张工', date: '2025-01-25', comments: '初版技术方案' }
                ],
                
                filteredDocuments: [],
                
                init() {
                    this.filterDocuments();
                    // 默认选择第一个文档
                    if (this.filteredDocuments.length > 0) {
                        this.selectedDocument = this.filteredDocuments[0];
                    }
                },
                
                get isAllSelected() {
                    return this.selectedDocuments.length === this.filteredDocuments.length && this.filteredDocuments.length > 0;
                },
                
                selectFolder(folder) {
                    this.selectedFolderId = folder.id;
                    this.filterDocuments();
                },
                
                toggleFolder(folder) {
                    folder.isExpanded = !folder.isExpanded;
                },
                
                getSelectedFolderName() {
                    const findFolder = (folders) => {
                        for (const folder of folders) {
                            if (folder.id === this.selectedFolderId) return folder.name;
                            if (folder.children) {
                                const found = findFolder(folder.children);
                                if (found) return found;
                            }
                        }
                        return null;
                    };
                    return findFolder(this.documentFolders) || '全部文档';
                },
                
                filterDocuments() {
                    let filtered = this.documents;
                    
                    // 文件夹筛选
                    if (this.selectedFolderId) {
                        filtered = filtered.filter(doc => doc.folderId === this.selectedFolderId);
                    }
                    
                    // 搜索筛选
                    if (this.searchQuery) {
                        const query = this.searchQuery.toLowerCase();
                        filtered = filtered.filter(doc =>
                            doc.name.toLowerCase().includes(query) ||
                            doc.description.toLowerCase().includes(query) ||
                            doc.author.toLowerCase().includes(query)
                        );
                    }
                    
                    // 状态筛选
                    if (this.statusFilter) {
                        filtered = filtered.filter(doc => doc.status === this.statusFilter);
                    }
                    
                    // 类型筛选
                    if (this.typeFilter) {
                        filtered = filtered.filter(doc => doc.type === this.typeFilter);
                    }
                    
                    this.filteredDocuments = filtered;
                },
                
                toggleSelectAll() {
                    if (this.isAllSelected) {
                        this.selectedDocuments = [];
                    } else {
                        this.selectedDocuments = this.filteredDocuments.map(doc => doc.id);
                    }
                },
                
                getFileIconColor(extension) {
                    const colorMap = {
                        'pdf': 'bg-red-500',
                        'docx': 'bg-blue-500',
                        'doc': 'bg-blue-500',
                        'dwg': 'bg-green-600',
                        'xlsx': 'bg-green-500',
                        'pptx': 'bg-orange-500',
                        'jpg': 'bg-purple-500',
                        'png': 'bg-purple-500',
                        'default': 'bg-gray-500'
                    };
                    return colorMap[extension] || colorMap.default;
                },
                
                getDocumentStatusText(status) {
                    const statusMap = {
                        'draft': '草稿',
                        'reviewing': '评审中',
                        'approved': '已批准',
                        'rejected': '已驳回'
                    };
                    return statusMap[status] || '未知';
                },
                
                getDocumentStatusClass(status) {
                    const classMap = {
                        'draft': 'bg-gray-100 text-gray-800',
                        'reviewing': 'bg-yellow-100 text-yellow-800',
                        'approved': 'bg-green-100 text-green-800',
                        'rejected': 'bg-red-100 text-red-800'
                    };
                    return classMap[status] || 'bg-gray-100 text-gray-800';
                },
                
                previewDocument(document) {
                    this.selectedDocument = document;
                    alert(`预览文档: ${document.name}`);
                },
                
                downloadDocument(document) {
                    alert(`下载文档: ${document.name}`);
                },
                
                editDocument(document) {
                    alert(`编辑文档: ${document.name}`);
                },
                
                shareDocument(document) {
                    alert(`分享文档: ${document.name}`);
                },
                
                moveDocument(document) {
                    alert(`移动文档: ${document.name}`);
                },
                
                deleteDocument(document) {
                    if (confirm(`确定要删除文档 ${document.name} 吗？`)) {
                        const index = this.documents.findIndex(d => d.id === document.id);
                        if (index > -1) {
                            this.documents.splice(index, 1);
                            this.filterDocuments();
                            // 更新统计
                            this.documentStats.total--;
                            if (document.status === 'approved') this.documentStats.approved--;
                            if (document.status === 'reviewing') this.documentStats.reviewing--;
                            if (document.status === 'draft') this.documentStats.draft--;
                            alert('文档删除成功');
                        }
                    }
                },
                
                showVersionHistory(document) {
                    alert(`查看版本历史: ${document.name}`);
                },
                
                createFolder() {
                    const folderName = prompt('请输入文件夹名称:');
                    if (folderName) {
                        const newFolder = {
                            id: Date.now(),
                            name: folderName,
                            documentCount: 0,
                            hasChildren: false
                        };
                        this.documentFolders.push(newFolder);
                        alert('文件夹创建成功');
                    }
                },
                
                async uploadDocument() {
                    this.uploading = true;
                    try {
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        
                        // 模拟创建新文档
                        const newDocument = {
                            id: Date.now(),
                            name: '新上传文档.pdf',
                            type: this.uploadForm.type,
                            extension: 'pdf',
                            version: this.uploadForm.version || 'v1.0',
                            status: 'draft',
                            size: '2.5MB',
                            folderId: parseInt(this.uploadForm.folderId),
                            description: this.uploadForm.description,
                            author: '当前用户',
                            createTime: new Date().toLocaleString('zh-CN'),
                            updateTime: new Date().toLocaleString('zh-CN'),
                            hasHistory: false,
                            collaborators: []
                        };
                        
                        this.documents.push(newDocument);
                        this.filterDocuments();
                        
                        // 更新统计
                        this.documentStats.total++;
                        this.documentStats.draft++;
                        
                        // 重置表单
                        this.uploadForm = {
                            type: '',
                            folderId: '',
                            description: '',
                            version: '',
                            securityLevel: 'internal',
                            collaborators: []
                        };
                        
                        this.showUploadModal = false;
                        alert('文档上传成功！');
                    } catch (error) {
                        alert('上传失败，请重试');
                    } finally {
                        this.uploading = false;
                    }
                },
                
                goBack() {
                    history.back();
                }
            }
        }
    </script>
</body>
</html>