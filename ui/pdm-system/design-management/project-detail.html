<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品设计详情 - 玻璃深加工ERP系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1890FF',
                        success: '#52C41A',
                        warning: '#FAAD14',
                        error: '#F5222D',
                        'title-gray': '#262626',
                        'body-gray': '#595959',
                        'aux-gray': '#8C8C8C',
                        'disabled-gray': '#BFBFBF',
                        'border-gray': '#D9D9D9',
                        'bg-gray': '#FAFAFA'
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', 'sans-serif']
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-bg-gray font-chinese" x-data="projectDetailApp()">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-border-gray h-16">
        <div class="flex items-center justify-between px-6 h-full">
            <div class="flex items-center space-x-4">
                <button @click="goBack()" class="text-aux-gray hover:text-title-gray transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                </button>
                <div>
                    <h1 class="text-xl font-medium text-title-gray" x-text="project.name"></h1>
                    <div class="flex items-center space-x-2 mt-1">
                        <span class="text-sm text-aux-gray" x-text="project.code"></span>
                        <span class="px-2 py-1 text-xs rounded-full"
                              :class="getStatusClass(project.status)"
                              x-text="getStatusText(project.status)"></span>
                    </div>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <div class="text-right">
                    <div class="text-sm font-medium text-title-gray">设计进度</div>
                    <div class="flex items-center mt-1">
                        <div class="w-24 bg-gray-200 rounded-full h-2 mr-2">
                            <div class="bg-primary h-2 rounded-full" :style="`width: ${project.progress}%`"></div>
                        </div>
                        <span class="text-sm text-aux-gray" x-text="`${project.progress}%`"></span>
                    </div>
                </div>
                <div class="flex space-x-2">
                    <button @click="saveProject()" :disabled="saving"
                            class="px-4 py-2 bg-success text-white rounded-md hover:bg-green-600 transition-colors disabled:bg-disabled-gray">
                        <span x-show="saving" class="inline-block w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                        <span x-text="saving ? '保存中...' : '保存'"></span>
                    </button>
                    <button @click="submitForReview()" :disabled="project.status === 'reviewing'"
                            class="px-4 py-2 bg-warning text-white rounded-md hover:bg-yellow-600 transition-colors disabled:bg-disabled-gray">
                        提交评审
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main class="flex h-full">
        <!-- 左侧主要内容 -->
        <div class="flex-1 flex flex-col">
            <!-- 标签导航 -->
            <div class="bg-white border-b border-border-gray">
                <nav class="flex space-x-8 px-6">
                    <template x-for="tab in tabs" :key="tab.id">
                        <button @click="activeTab = tab.id"
                                class="py-4 px-1 border-b-2 font-medium text-sm transition-colors"
                                :class="activeTab === tab.id ? 'border-primary text-primary' : 'border-transparent text-aux-gray hover:text-body-gray'"
                                x-text="tab.name"></button>
                    </template>
                </nav>
            </div>

            <!-- 标签内容区域 -->
            <div class="flex-1 overflow-auto">
                <!-- 基本信息标签 -->
                <div x-show="activeTab === 'basic'" class="p-6">
                    <div class="bg-white rounded-lg shadow-sm border border-border-gray p-6">
                        <h3 class="text-lg font-medium text-title-gray mb-6">项目基本信息</h3>
                        
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- 左侧基本信息 -->
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-title-gray mb-2">项目名称</label>
                                    <input type="text" x-model="project.name"
                                           class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-title-gray mb-2">项目编号</label>
                                    <input type="text" x-model="project.code" readonly
                                           class="w-full px-3 py-2 border border-border-gray rounded-md bg-bg-gray text-aux-gray">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-title-gray mb-2">产品类型</label>
                                    <select x-model="project.productType"
                                            class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                                        <option value="building">建筑玻璃</option>
                                        <option value="furniture">家具玻璃</option>
                                        <option value="decoration">装饰玻璃</option>
                                        <option value="special">特种玻璃</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-title-gray mb-2">设计负责人</label>
                                    <select x-model="project.owner"
                                            class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                                        <option value="张工">张工 (高级工艺工程师)</option>
                                        <option value="李工">李工 (资深设计师)</option>
                                        <option value="王工">王工 (技术主管)</option>
                                        <option value="赵工">赵工 (工艺专家)</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-title-gray mb-2">客户信息</label>
                                    <input type="text" x-model="project.customer"
                                           class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                                           placeholder="客户名称">
                                </div>
                            </div>
                            
                            <!-- 右侧时间和状态信息 -->
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-title-gray mb-2">创建时间</label>
                                    <input type="datetime-local" x-model="project.createTime" readonly
                                           class="w-full px-3 py-2 border border-border-gray rounded-md bg-bg-gray text-aux-gray">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-title-gray mb-2">计划开始时间</label>
                                    <input type="date" x-model="project.startDate"
                                           class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-title-gray mb-2">计划完成时间</label>
                                    <input type="date" x-model="project.deadline"
                                           class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-title-gray mb-2">优先级</label>
                                    <select x-model="project.priority"
                                            class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                                        <option value="high">高优先级</option>
                                        <option value="medium">中优先级</option>
                                        <option value="low">低优先级</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-title-gray mb-2">预估工时(小时)</label>
                                    <input type="number" x-model="project.estimatedHours"
                                           class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                                           placeholder="0">
                                </div>
                            </div>
                        </div>
                        
                        <!-- 项目描述 -->
                        <div class="mt-6">
                            <label class="block text-sm font-medium text-title-gray mb-2">项目描述</label>
                            <textarea x-model="project.description" rows="4"
                                      class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                                      placeholder="请输入项目描述..."></textarea>
                        </div>
                        
                        <!-- 技术要求 -->
                        <div class="mt-6">
                            <label class="block text-sm font-medium text-title-gray mb-2">技术要求</label>
                            <textarea x-model="project.requirements" rows="6"
                                      class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                                      placeholder="请详细描述技术要求和规格参数..."></textarea>
                        </div>
                    </div>
                </div>

                <!-- 设计阶段标签 -->
                <div x-show="activeTab === 'phases'" class="p-6">
                    <div class="bg-white rounded-lg shadow-sm border border-border-gray p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-medium text-title-gray">设计阶段管理</h3>
                            <button @click="addPhase()"
                                    class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600 transition-colors">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                </svg>
                                添加阶段
                            </button>
                        </div>
                        
                        <div class="space-y-4">
                            <template x-for="(phase, index) in project.phases" :key="phase.id">
                                <div class="border border-border-gray rounded-lg p-4">
                                    <div class="flex items-center justify-between mb-4">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm"
                                                 :class="getPhaseStatusColor(phase.status)">
                                                <span x-text="index + 1"></span>
                                            </div>
                                            <div>
                                                <h4 class="text-sm font-medium text-title-gray" x-text="phase.name"></h4>
                                                <p class="text-xs text-aux-gray" x-text="phase.description"></p>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <span class="px-2 py-1 text-xs rounded-full"
                                                  :class="getPhaseStatusClass(phase.status)"
                                                  x-text="getPhaseStatusText(phase.status)"></span>
                                            <button @click="editPhase(phase)" class="text-primary hover:text-blue-600">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                                        <div>
                                            <span class="text-aux-gray">负责人:</span>
                                            <span class="text-body-gray ml-1" x-text="phase.assignee"></span>
                                        </div>
                                        <div>
                                            <span class="text-aux-gray">计划开始:</span>
                                            <span class="text-body-gray ml-1" x-text="phase.startDate"></span>
                                        </div>
                                        <div>
                                            <span class="text-aux-gray">计划完成:</span>
                                            <span class="text-body-gray ml-1" x-text="phase.endDate"></span>
                                        </div>
                                        <div>
                                            <span class="text-aux-gray">进度:</span>
                                            <span class="text-body-gray ml-1" x-text="`${phase.progress}%`"></span>
                                        </div>
                                    </div>
                                    
                                    <!-- 进度条 -->
                                    <div class="mt-3">
                                        <div class="w-full bg-gray-200 rounded-full h-2">
                                            <div class="h-2 rounded-full transition-all"
                                                 :class="phase.status === 'completed' ? 'bg-success' : 'bg-primary'"
                                                 :style="`width: ${phase.progress}%`"></div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>

                <!-- BOM管理标签 -->
                <div x-show="activeTab === 'bom'" class="p-6">
                    <div class="space-y-6">
                        <!-- BOM类型切换 -->
                        <div class="bg-white rounded-lg shadow-sm border border-border-gray p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-medium text-title-gray">BOM管理</h3>
                                <div class="flex items-center space-x-4">
                                    <span class="text-sm text-aux-gray">BOM类型:</span>
                                    <div class="flex space-x-1 bg-bg-gray rounded-lg p-1">
                                        <template x-for="bomType in bomTypes" :key="bomType.id">
                                            <button @click="activeBomType = bomType.id"
                                                    class="px-3 py-1 text-sm rounded-md transition-colors"
                                                    :class="activeBomType === bomType.id ? 'bg-white text-primary shadow-sm' : 'text-aux-gray hover:text-body-gray'"
                                                    x-text="bomType.name"></button>
                                        </template>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- BOM流转状态 -->
                            <div class="flex items-center justify-center space-x-4 mb-6">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full"
                                         :class="project.bomStatus.sales ? 'bg-success' : 'bg-gray-300'"></div>
                                    <span class="ml-2 text-sm" 
                                          :class="project.bomStatus.sales ? 'text-success' : 'text-aux-gray'">销售BOM</span>
                                </div>
                                <div class="flex-1 h-0.5"
                                     :class="project.bomStatus.process ? 'bg-success' : 'bg-gray-300'"></div>
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full"
                                         :class="project.bomStatus.process ? 'bg-success' : 'bg-gray-300'"></div>
                                    <span class="ml-2 text-sm"
                                          :class="project.bomStatus.process ? 'text-success' : 'text-aux-gray'">工艺BOM</span>
                                </div>
                                <div class="flex-1 h-0.5"
                                     :class="project.bomStatus.production ? 'bg-success' : 'bg-gray-300'"></div>
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full"
                                         :class="project.bomStatus.production ? 'bg-success' : 'bg-gray-300'"></div>
                                    <span class="ml-2 text-sm"
                                          :class="project.bomStatus.production ? 'text-success' : 'text-aux-gray'">生产BOM</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- BOM树形结构 -->
                        <div class="bg-white rounded-lg shadow-sm border border-border-gray">
                            <div class="p-4 border-b border-border-gray">
                                <div class="flex items-center justify-between">
                                    <h4 class="text-sm font-medium text-title-gray" x-text="getBomTypeName(activeBomType)"></h4>
                                    <div class="flex space-x-2">
                                        <button @click="addBomItem()"
                                                class="px-3 py-1 text-sm bg-primary text-white rounded-md hover:bg-blue-600">
                                            添加物料
                                        </button>
                                        <button @click="importBom()"
                                                class="px-3 py-1 text-sm bg-success text-white rounded-md hover:bg-green-600">
                                            导入BOM
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="p-4">
                                <div class="space-y-2">
                                    <template x-for="item in getCurrentBomItems()" :key="item.id">
                                        <div class="flex items-center justify-between p-3 border border-border-gray rounded-md hover:bg-gray-50">
                                            <div class="flex items-center space-x-3">
                                                <div class="text-sm">
                                                    <span class="text-aux-gray mr-2" x-text="item.level"></span>
                                                    <span class="font-medium text-title-gray" x-text="item.materialCode"></span>
                                                    <span class="text-body-gray ml-2" x-text="item.materialName"></span>
                                                </div>
                                            </div>
                                            <div class="flex items-center space-x-4 text-sm">
                                                <span class="text-aux-gray">数量:</span>
                                                <span class="text-body-gray" x-text="item.quantity"></span>
                                                <span class="text-aux-gray" x-text="item.unit"></span>
                                                <div class="flex space-x-2">
                                                    <button @click="editBomItem(item)" class="text-primary hover:text-blue-600">编辑</button>
                                                    <button @click="deleteBomItem(item)" class="text-error hover:text-red-600">删除</button>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 技术规格标签 -->
                <div x-show="activeTab === 'specs'" class="p-6">
                    <div class="bg-white rounded-lg shadow-sm border border-border-gray p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-medium text-title-gray">技术规格参数</h3>
                            <button @click="addSpecification()"
                                    class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600 transition-colors">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                </svg>
                                添加规格
                            </button>
                        </div>
                        
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- 物理特性 -->
                            <div class="space-y-4">
                                <h4 class="text-sm font-medium text-title-gray border-b border-border-gray pb-2">物理特性</h4>
                                <template x-for="spec in project.specifications.physical" :key="spec.id">
                                    <div class="flex items-center justify-between p-3 border border-border-gray rounded-md">
                                        <div>
                                            <span class="text-sm font-medium text-title-gray" x-text="spec.name"></span>
                                            <p class="text-xs text-aux-gray mt-1" x-text="spec.description"></p>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <input type="text" x-model="spec.value"
                                                   class="w-20 px-2 py-1 text-sm border border-border-gray rounded focus:outline-none focus:ring-1 focus:ring-primary">
                                            <span class="text-xs text-aux-gray" x-text="spec.unit"></span>
                                            <button @click="removeSpecification(spec)" class="text-error hover:text-red-600">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </template>
                            </div>
                            
                            <!-- 加工工艺 -->
                            <div class="space-y-4">
                                <h4 class="text-sm font-medium text-title-gray border-b border-border-gray pb-2">加工工艺</h4>
                                <template x-for="spec in project.specifications.process" :key="spec.id">
                                    <div class="flex items-center justify-between p-3 border border-border-gray rounded-md">
                                        <div>
                                            <span class="text-sm font-medium text-title-gray" x-text="spec.name"></span>
                                            <p class="text-xs text-aux-gray mt-1" x-text="spec.description"></p>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <select x-model="spec.value" 
                                                    class="px-2 py-1 text-sm border border-border-gray rounded focus:outline-none focus:ring-1 focus:ring-primary">
                                                <template x-for="option in spec.options" :key="option">
                                                    <option :value="option" x-text="option"></option>
                                                </template>
                                            </select>
                                            <button @click="removeSpecification(spec)" class="text-error hover:text-red-600">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                        
                        <!-- 质量标准 -->
                        <div class="mt-6">
                            <h4 class="text-sm font-medium text-title-gray border-b border-border-gray pb-2 mb-4">质量标准</h4>
                            <div class="space-y-3">
                                <template x-for="standard in project.qualityStandards" :key="standard.id">
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                                        <div class="flex items-center space-x-3">
                                            <input type="checkbox" x-model="standard.required" class="rounded border-border-gray">
                                            <div>
                                                <span class="text-sm font-medium text-title-gray" x-text="standard.name"></span>
                                                <p class="text-xs text-aux-gray" x-text="standard.description"></p>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <span class="px-2 py-1 text-xs rounded-full"
                                                  :class="standard.required ? 'bg-primary text-white' : 'bg-gray-200 text-aux-gray'"
                                                  x-text="standard.required ? '必须' : '可选'"></span>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 文档管理标签 -->
                <div x-show="activeTab === 'documents'" class="p-6">
                    <div class="bg-white rounded-lg shadow-sm border border-border-gray p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-medium text-title-gray">设计文档</h3>
                            <button @click="uploadDocument()"
                                    class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600 transition-colors">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                                </svg>
                                上传文档
                            </button>
                        </div>
                        
                        <div class="space-y-4">
                            <template x-for="document in project.documents" :key="document.id">
                                <div class="flex items-center justify-between p-4 border border-border-gray rounded-lg hover:bg-gray-50">
                                    <div class="flex items-center space-x-4">
                                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 class="text-sm font-medium text-title-gray" x-text="document.name"></h4>
                                            <div class="flex items-center space-x-4 text-xs text-aux-gray mt-1">
                                                <span x-text="document.type"></span>
                                                <span x-text="document.size"></span>
                                                <span x-text="document.uploadTime"></span>
                                                <span x-text="document.uploader"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span class="px-2 py-1 text-xs rounded-full"
                                              :class="getDocumentStatusClass(document.status)"
                                              x-text="getDocumentStatusText(document.status)"></span>
                                        <div class="flex space-x-1">
                                            <button @click="previewDocument(document)" class="text-primary hover:text-blue-600">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                                </svg>
                                            </button>
                                            <button @click="downloadDocument(document)" class="text-success hover:text-green-600">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                                </svg>
                                            </button>
                                            <button @click="deleteDocument(document)" class="text-error hover:text-red-600">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                        
                        <!-- 文档版本历史 -->
                        <div class="mt-8">
                            <h4 class="text-sm font-medium text-title-gray mb-4">版本历史</h4>
                            <div class="space-y-2">
                                <template x-for="version in project.documentVersions" :key="version.id">
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                                        <div class="flex items-center space-x-3">
                                            <span class="text-sm font-medium text-title-gray" x-text="version.version"></span>
                                            <span class="text-sm text-body-gray" x-text="version.document"></span>
                                        </div>
                                        <div class="flex items-center space-x-4 text-xs text-aux-gray">
                                            <span x-text="version.author"></span>
                                            <span x-text="version.date"></span>
                                            <button class="text-primary hover:text-blue-600">查看</button>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 评审流程标签 -->
                <div x-show="activeTab === 'review'" class="p-6">
                    <div class="bg-white rounded-lg shadow-sm border border-border-gray p-6">
                        <h3 class="text-lg font-medium text-title-gray mb-6">评审流程</h3>
                        
                        <!-- 评审状态时间线 -->
                        <div class="space-y-6">
                            <template x-for="(review, index) in project.reviewProcess" :key="review.id">
                                <div class="flex items-start space-x-4">
                                    <div class="flex flex-col items-center">
                                        <div class="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm"
                                             :class="getReviewStatusColor(review.status)">
                                            <span x-text="index + 1"></span>
                                        </div>
                                        <div x-show="index < project.reviewProcess.length - 1"
                                             class="w-0.5 h-12 bg-border-gray mt-2"></div>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex items-center justify-between mb-2">
                                            <h4 class="text-sm font-medium text-title-gray" x-text="review.name"></h4>
                                            <span class="px-2 py-1 text-xs rounded-full"
                                                  :class="getReviewStatusClass(review.status)"
                                                  x-text="getReviewStatusText(review.status)"></span>
                                        </div>
                                        <p class="text-sm text-aux-gray mb-3" x-text="review.description"></p>
                                        <div class="grid grid-cols-2 gap-4 text-sm">
                                            <div>
                                                <span class="text-aux-gray">评审人:</span>
                                                <span class="text-body-gray ml-1" x-text="review.reviewer"></span>
                                            </div>
                                            <div>
                                                <span class="text-aux-gray">截止时间:</span>
                                                <span class="text-body-gray ml-1" x-text="review.deadline"></span>
                                            </div>
                                        </div>
                                        <div x-show="review.comments" class="mt-3 p-3 bg-gray-50 rounded-md">
                                            <p class="text-sm text-body-gray" x-text="review.comments"></p>
                                            <div class="flex items-center justify-between mt-2">
                                                <span class="text-xs text-aux-gray" x-text="review.reviewTime"></span>
                                                <div x-show="review.attachments" class="flex space-x-1">
                                                    <template x-for="attachment in review.attachments" :key="attachment">
                                                        <span class="text-xs text-primary hover:underline cursor-pointer" x-text="attachment"></span>
                                                    </template>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧信息面板 -->
        <div class="w-80 bg-white border-l border-border-gray">
            <div class="p-6">
                <!-- 项目进度概览 -->
                <div class="mb-6">
                    <h3 class="text-sm font-medium text-title-gray mb-3">项目进度概览</h3>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-aux-gray">整体进度</span>
                            <span class="text-sm font-medium text-title-gray" x-text="`${project.progress}%`"></span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-primary h-2 rounded-full transition-all" :style="`width: ${project.progress}%`"></div>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-3 mt-4">
                            <div class="text-center">
                                <div class="text-lg font-bold text-success" x-text="project.completedTasks"></div>
                                <div class="text-xs text-aux-gray">已完成任务</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-bold text-warning" x-text="project.pendingTasks"></div>
                                <div class="text-xs text-aux-gray">待完成任务</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 团队成员 -->
                <div class="mb-6">
                    <h3 class="text-sm font-medium text-title-gray mb-3">项目团队</h3>
                    <div class="space-y-3">
                        <template x-for="member in project.team" :key="member.id">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <span class="text-xs text-primary" x-text="member.name.charAt(0)"></span>
                                </div>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-title-gray" x-text="member.name"></div>
                                    <div class="text-xs text-aux-gray" x-text="member.role"></div>
                                </div>
                                <div class="w-2 h-2 rounded-full" :class="member.online ? 'bg-success' : 'bg-gray-300'"></div>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- 最近活动 -->
                <div>
                    <h3 class="text-sm font-medium text-title-gray mb-3">最近活动</h3>
                    <div class="space-y-3 max-h-64 overflow-y-auto">
                        <template x-for="activity in project.activities" :key="activity.id">
                            <div class="flex items-start space-x-3">
                                <div class="w-6 h-6 rounded-full flex items-center justify-center text-white text-xs"
                                     :class="getActivityColor(activity.type)">
                                    <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="text-xs text-title-gray" x-text="activity.title"></div>
                                    <div class="text-xs text-aux-gray mt-1" x-text="activity.description"></div>
                                    <div class="flex items-center justify-between mt-1">
                                        <span class="text-xs text-aux-gray" x-text="activity.user"></span>
                                        <span class="text-xs text-aux-gray" x-text="activity.time"></span>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function projectDetailApp() {
            return {
                saving: false,
                activeTab: 'basic',
                activeBomType: 'sales',
                
                tabs: [
                    { id: 'basic', name: '基本信息' },
                    { id: 'phases', name: '设计阶段' },
                    { id: 'bom', name: 'BOM管理' },
                    { id: 'specs', name: '技术规格' },
                    { id: 'documents', name: '文档管理' },
                    { id: 'review', name: '评审流程' }
                ],
                
                bomTypes: [
                    { id: 'sales', name: '销售BOM' },
                    { id: 'process', name: '工艺BOM' },
                    { id: 'production', name: '生产BOM' }
                ],
                
                project: {
                    id: 1,
                    code: 'PRJ-2025-001',
                    name: '高强度钢化玻璃幕墙系统设计',
                    description: '为上海陆家嘴金融中心设计的高强度钢化玻璃幕墙系统，采用Low-E中空玻璃技术，具备优异的隔热保温性能',
                    productType: 'building',
                    status: 'designing',
                    progress: 65,
                    owner: '张工',
                    customer: '上海建工集团',
                    createTime: '2025-01-15T09:00:00',
                    startDate: '2025-01-20',
                    deadline: '2025-03-15',
                    priority: 'high',
                    estimatedHours: 320,
                    requirements: `技术要求：
1. 玻璃规格：6+12A+6mm Low-E中空钢化玻璃
2. 结构胶：道康宁DC995结构密封胶
3. 密封胶：道康宁DC883耐候密封胶
4. 框料：6063-T5铝合金型材，表面氟碳喷涂
5. 抗风压性能：≥4.0kPa
6. 气密性能：≥8级
7. 水密性能：≥6级
8. 保温性能：K值≤2.8W/(m²·K)
9. 隔声性能：Rw≥35dB
10. 防火性能：A级不燃`,
                    
                    completedTasks: 12,
                    pendingTasks: 8,
                    
                    bomStatus: {
                        sales: true,
                        process: true,
                        production: false
                    },
                    
                    phases: [
                        {
                            id: 1,
                            name: '需求分析',
                            description: '收集客户需求，分析技术可行性',
                            status: 'completed',
                            progress: 100,
                            assignee: '张工',
                            startDate: '2025-01-20',
                            endDate: '2025-01-25'
                        },
                        {
                            id: 2,
                            name: '方案设计',
                            description: '制定技术方案，设计产品结构',
                            status: 'completed',
                            progress: 100,
                            assignee: '李工',
                            startDate: '2025-01-26',
                            endDate: '2025-02-05'
                        },
                        {
                            id: 3,
                            name: '详细设计',
                            description: '完成详细的工程设计图纸和规格',
                            status: 'in_progress',
                            progress: 75,
                            assignee: '王工',
                            startDate: '2025-02-06',
                            endDate: '2025-02-20'
                        },
                        {
                            id: 4,
                            name: '设计验证',
                            description: '进行设计验证和性能测试',
                            status: 'pending',
                            progress: 0,
                            assignee: '赵工',
                            startDate: '2025-02-21',
                            endDate: '2025-03-05'
                        },
                        {
                            id: 5,
                            name: '工艺设计',
                            description: '制定生产工艺流程和质量控制标准',
                            status: 'pending',
                            progress: 0,
                            assignee: '钱工',
                            startDate: '2025-03-06',
                            endDate: '2025-03-15'
                        }
                    ],
                    
                    specifications: {
                        physical: [
                            { id: 1, name: '玻璃厚度', description: '单片玻璃厚度', value: '6', unit: 'mm' },
                            { id: 2, name: '中空层厚度', description: '中空玻璃间隔层厚度', value: '12', unit: 'mm' },
                            { id: 3, name: '整体厚度', description: '中空玻璃总厚度', value: '24', unit: 'mm' },
                            { id: 4, name: '密度', description: '玻璃密度', value: '2.5', unit: 'g/cm³' },
                            { id: 5, name: '抗弯强度', description: '钢化玻璃抗弯强度', value: '120', unit: 'MPa' }
                        ],
                        process: [
                            { 
                                id: 1, 
                                name: '钢化工艺', 
                                description: '玻璃钢化处理工艺', 
                                value: '水平钢化', 
                                options: ['水平钢化', '垂直钢化', '弯钢化'] 
                            },
                            { 
                                id: 2, 
                                name: 'Low-E镀膜', 
                                description: '低辐射镀膜工艺', 
                                value: '离线镀膜', 
                                options: ['离线镀膜', '在线镀膜'] 
                            },
                            { 
                                id: 3, 
                                name: '中空填充气体', 
                                description: '中空层填充气体类型', 
                                value: '氩气', 
                                options: ['空气', '氩气', '氪气'] 
                            }
                        ]
                    },
                    
                    qualityStandards: [
                        { id: 1, name: 'GB15763.2-2005', description: '钢化玻璃国家标准', required: true },
                        { id: 2, name: 'GB/T11944-2012', description: '中空玻璃国家标准', required: true },
                        { id: 3, name: 'JGJ102-2003', description: '玻璃幕墙工程技术规范', required: true },
                        { id: 4, name: 'GB50210-2018', description: '建筑装饰装修工程质量验收标准', required: false },
                        { id: 5, name: 'ASTM E2190', description: '国际绝缘玻璃单元测试标准', required: false }
                    ],
                    
                    documents: [
                        {
                            id: 1,
                            name: '技术方案书v2.0.pdf',
                            type: '技术文档',
                            size: '2.3MB',
                            status: 'approved',
                            uploadTime: '2025-02-01 14:30',
                            uploader: '张工'
                        },
                        {
                            id: 2,
                            name: '结构设计图纸.dwg',
                            type: 'CAD图纸',
                            size: '5.7MB',
                            status: 'reviewing',
                            uploadTime: '2025-02-10 09:15',
                            uploader: '李工'
                        },
                        {
                            id: 3,
                            name: '材料清单.xlsx',
                            type: 'Excel文档',
                            size: '156KB',
                            status: 'draft',
                            uploadTime: '2025-02-12 16:45',
                            uploader: '王工'
                        },
                        {
                            id: 4,
                            name: '性能测试报告.pdf',
                            type: '测试报告',
                            size: '1.8MB',
                            status: 'approved',
                            uploadTime: '2025-02-08 11:20',
                            uploader: '赵工'
                        }
                    ],
                    
                    documentVersions: [
                        { id: 1, version: 'v2.0', document: '技术方案书', author: '张工', date: '2025-02-01' },
                        { id: 2, version: 'v1.2', document: '技术方案书', author: '张工', date: '2025-01-28' },
                        { id: 3, version: 'v1.1', document: '技术方案书', author: '李工', date: '2025-01-25' },
                        { id: 4, version: 'v1.0', document: '技术方案书', author: '张工', date: '2025-01-22' }
                    ],
                    
                    reviewProcess: [
                        {
                            id: 1,
                            name: '技术方案评审',
                            description: '评审技术方案的可行性和合理性',
                            status: 'completed',
                            reviewer: '技术总监',
                            deadline: '2025-01-30',
                            reviewTime: '2025-01-29 15:30',
                            comments: '技术方案整体可行，建议在保温性能方面进一步优化。Low-E镀膜的选择合理，建议采用双银Low-E以获得更好的节能效果。',
                            attachments: ['评审意见.pdf', '修改建议.docx']
                        },
                        {
                            id: 2,
                            name: '结构设计评审',
                            description: '评审结构设计的安全性和实用性',
                            status: 'in_progress',
                            reviewer: '结构工程师',
                            deadline: '2025-02-15',
                            reviewTime: null,
                            comments: null,
                            attachments: null
                        },
                        {
                            id: 3,
                            name: '工艺评审',
                            description: '评审生产工艺的可操作性',
                            status: 'pending',
                            reviewer: '工艺主管',
                            deadline: '2025-02-25',
                            reviewTime: null,
                            comments: null,
                            attachments: null
                        },
                        {
                            id: 4,
                            name: '最终评审',
                            description: '项目整体评审和批准',
                            status: 'pending',
                            reviewer: '项目经理',
                            deadline: '2025-03-10',
                            reviewTime: null,
                            comments: null,
                            attachments: null
                        }
                    ],
                    
                    team: [
                        { id: 1, name: '张工', role: '项目负责人', online: true },
                        { id: 2, name: '李工', role: '结构设计师', online: false },
                        { id: 3, name: '王工', role: '工艺工程师', online: true },
                        { id: 4, name: '赵工', role: '质量工程师', online: true },
                        { id: 5, name: '钱工', role: '测试工程师', online: false }
                    ],
                    
                    activities: [
                        {
                            id: 1,
                            type: 'update',
                            title: '更新了技术规格',
                            description: '修改了玻璃厚度规格参数',
                            user: '王工',
                            time: '30分钟前'
                        },
                        {
                            id: 2,
                            type: 'upload',
                            title: '上传了设计文档',
                            description: '上传了结构设计图纸v1.5版本',
                            user: '李工',
                            time: '2小时前'
                        },
                        {
                            id: 3,
                            type: 'review',
                            title: '完成技术评审',
                            description: '技术方案评审通过，可进入下一阶段',
                            user: '技术总监',
                            time: '4小时前'
                        },
                        {
                            id: 4,
                            type: 'comment',
                            title: '添加了评审意见',
                            description: '在材料选择方面提出了改进建议',
                            user: '张工',
                            time: '1天前'
                        }
                    ]
                },
                
                salesBomItems: [
                    { id: 1, level: '1', materialCode: 'BG-001', materialName: '6+12A+6mm Low-E中空钢化玻璃', quantity: 1, unit: '㎡' },
                    { id: 2, level: '2', materialCode: 'GL-001', materialName: '6mm超白钢化玻璃', quantity: 2, unit: '片' },
                    { id: 3, level: '2', materialCode: 'SP-001', materialName: '12mm铝隔条', quantity: 1, unit: '套' },
                    { id: 4, level: '2', materialCode: 'SL-001', materialName: '结构密封胶', quantity: 0.5, unit: 'kg' }
                ],
                
                processBomItems: [
                    { id: 1, level: '1', materialCode: 'BG-001', materialName: '6+12A+6mm Low-E中空钢化玻璃', quantity: 1, unit: '㎡' },
                    { id: 2, level: '2', materialCode: 'GL-RAW-001', materialName: '6mm超白浮法玻璃原片', quantity: 2.1, unit: '片' },
                    { id: 3, level: '2', materialCode: 'COAT-001', materialName: 'Low-E镀膜', quantity: 1, unit: '面' },
                    { id: 4, level: '2', materialCode: 'SP-001', materialName: '12mm铝隔条', quantity: 1, unit: '套' },
                    { id: 5, level: '2', materialCode: 'DS-001', materialName: '双道密封胶', quantity: 0.3, unit: 'kg' },
                    { id: 6, level: '2', materialCode: 'GAS-001', materialName: '氩气', quantity: 0.02, unit: '立方米' }
                ],
                
                productionBomItems: [
                    { id: 1, level: '1', materialCode: 'BG-001', materialName: '6+12A+6mm Low-E中空钢化玻璃', quantity: 1, unit: '㎡' },
                    { id: 2, level: '2', materialCode: 'GL-RAW-001', materialName: '6mm超白浮法玻璃原片', quantity: 2.1, unit: '片' },
                    { id: 3, level: '3', materialCode: 'CUT-001', materialName: '切割工序', quantity: 2, unit: '片' },
                    { id: 4, level: '3', materialCode: 'EDGE-001', materialName: '磨边工序', quantity: 2, unit: '片' },
                    { id: 5, level: '3', materialCode: 'TEMP-001', materialName: '钢化工序', quantity: 2, unit: '片' },
                    { id: 6, level: '2', materialCode: 'SP-PROC-001', materialName: '铝隔条加工', quantity: 1, unit: '套' },
                    { id: 7, level: '2', materialCode: 'ASM-001', materialName: '中空组装工序', quantity: 1, unit: '片' }
                ],
                
                init() {
                    // 初始化逻辑
                },
                
                goBack() {
                    history.back();
                },
                
                getStatusText(status) {
                    const statusMap = {
                        'designing': '设计中',
                        'reviewing': '评审中',
                        'completed': '已完成',
                        'paused': '已暂停'
                    };
                    return statusMap[status] || '未知';
                },
                
                getStatusClass(status) {
                    const classMap = {
                        'designing': 'bg-blue-100 text-blue-800',
                        'reviewing': 'bg-yellow-100 text-yellow-800',
                        'completed': 'bg-green-100 text-green-800',
                        'paused': 'bg-gray-100 text-gray-800'
                    };
                    return classMap[status] || 'bg-gray-100 text-gray-800';
                },
                
                getPhaseStatusText(status) {
                    const statusMap = {
                        'pending': '待开始',
                        'in_progress': '进行中',
                        'completed': '已完成',
                        'paused': '已暂停'
                    };
                    return statusMap[status] || '未知';
                },
                
                getPhaseStatusClass(status) {
                    const classMap = {
                        'pending': 'bg-gray-100 text-gray-800',
                        'in_progress': 'bg-blue-100 text-blue-800',
                        'completed': 'bg-green-100 text-green-800',
                        'paused': 'bg-yellow-100 text-yellow-800'
                    };
                    return classMap[status] || 'bg-gray-100 text-gray-800';
                },
                
                getPhaseStatusColor(status) {
                    const colorMap = {
                        'pending': 'bg-gray-500',
                        'in_progress': 'bg-blue-500',
                        'completed': 'bg-green-500',
                        'paused': 'bg-yellow-500'
                    };
                    return colorMap[status] || 'bg-gray-500';
                },
                
                getBomTypeName(type) {
                    const typeMap = {
                        'sales': '销售BOM',
                        'process': '工艺BOM',
                        'production': '生产BOM'
                    };
                    return typeMap[type] || '未知';
                },
                
                getCurrentBomItems() {
                    switch (this.activeBomType) {
                        case 'sales':
                            return this.salesBomItems;
                        case 'process':
                            return this.processBomItems;
                        case 'production':
                            return this.productionBomItems;
                        default:
                            return [];
                    }
                },
                
                getDocumentStatusText(status) {
                    const statusMap = {
                        'draft': '草稿',
                        'reviewing': '评审中',
                        'approved': '已批准',
                        'rejected': '已驳回'
                    };
                    return statusMap[status] || '未知';
                },
                
                getDocumentStatusClass(status) {
                    const classMap = {
                        'draft': 'bg-gray-100 text-gray-800',
                        'reviewing': 'bg-yellow-100 text-yellow-800',
                        'approved': 'bg-green-100 text-green-800',
                        'rejected': 'bg-red-100 text-red-800'
                    };
                    return classMap[status] || 'bg-gray-100 text-gray-800';
                },
                
                getReviewStatusText(status) {
                    const statusMap = {
                        'pending': '待评审',
                        'in_progress': '评审中',
                        'completed': '已完成',
                        'rejected': '已驳回'
                    };
                    return statusMap[status] || '未知';
                },
                
                getReviewStatusClass(status) {
                    const classMap = {
                        'pending': 'bg-gray-100 text-gray-800',
                        'in_progress': 'bg-blue-100 text-blue-800',
                        'completed': 'bg-green-100 text-green-800',
                        'rejected': 'bg-red-100 text-red-800'
                    };
                    return classMap[status] || 'bg-gray-100 text-gray-800';
                },
                
                getReviewStatusColor(status) {
                    const colorMap = {
                        'pending': 'bg-gray-500',
                        'in_progress': 'bg-blue-500',
                        'completed': 'bg-green-500',
                        'rejected': 'bg-red-500'
                    };
                    return colorMap[status] || 'bg-gray-500';
                },
                
                getActivityColor(type) {
                    const colorMap = {
                        'update': 'bg-blue-500',
                        'upload': 'bg-green-500',
                        'review': 'bg-purple-500',
                        'comment': 'bg-yellow-500'
                    };
                    return colorMap[type] || 'bg-gray-500';
                },
                
                async saveProject() {
                    this.saving = true;
                    try {
                        // 模拟保存API调用
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        alert('项目保存成功！');
                    } catch (error) {
                        alert('保存失败，请重试');
                    } finally {
                        this.saving = false;
                    }
                },
                
                submitForReview() {
                    if (confirm('确定要提交评审吗？提交后将无法编辑项目信息。')) {
                        this.project.status = 'reviewing';
                        alert('已提交评审，等待审核！');
                    }
                },
                
                // 阶段管理方法
                addPhase() {
                    const newPhase = {
                        id: Date.now(),
                        name: '新阶段',
                        description: '请输入阶段描述',
                        status: 'pending',
                        progress: 0,
                        assignee: this.project.owner,
                        startDate: '',
                        endDate: ''
                    };
                    this.project.phases.push(newPhase);
                },
                
                editPhase(phase) {
                    // 这里可以打开编辑模态框
                    alert(`编辑阶段: ${phase.name}`);
                },
                
                // BOM管理方法
                addBomItem() {
                    alert('添加BOM物料项');
                },
                
                importBom() {
                    alert('导入BOM数据');
                },
                
                editBomItem(item) {
                    alert(`编辑物料: ${item.materialName}`);
                },
                
                deleteBomItem(item) {
                    if (confirm(`确定要删除物料 ${item.materialName} 吗？`)) {
                        // 删除逻辑
                        alert('物料删除成功');
                    }
                },
                
                // 规格管理方法
                addSpecification() {
                    alert('添加技术规格');
                },
                
                removeSpecification(spec) {
                    if (confirm(`确定要删除规格 ${spec.name} 吗？`)) {
                        // 删除逻辑
                        alert('规格删除成功');
                    }
                },
                
                // 文档管理方法
                uploadDocument() {
                    alert('上传设计文档');
                },
                
                previewDocument(document) {
                    alert(`预览文档: ${document.name}`);
                },
                
                downloadDocument(document) {
                    alert(`下载文档: ${document.name}`);
                },
                
                deleteDocument(document) {
                    if (confirm(`确定要删除文档 ${document.name} 吗？`)) {
                        const index = this.project.documents.findIndex(d => d.id === document.id);
                        if (index > -1) {
                            this.project.documents.splice(index, 1);
                            alert('文档删除成功');
                        }
                    }
                }
            }
        }
    </script>
</body>
</html>