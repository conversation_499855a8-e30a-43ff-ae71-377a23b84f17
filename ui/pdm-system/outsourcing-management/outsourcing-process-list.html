<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>外协工序管理 - 玻璃深加工ERP系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1890FF',
                        success: '#52C41A',
                        warning: '#FAAD14',
                        error: '#F5222D',
                        'title-gray': '#262626',
                        'body-gray': '#595959',
                        'aux-gray': '#8C8C8C',
                        'disabled-gray': '#BFBFBF',
                        'border-gray': '#D9D9D9',
                        'bg-gray': '#FAFAFA'
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', 'sans-serif']
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-bg-gray font-chinese" x-data="outsourcingProcessApp()">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-border-gray h-16">
        <div class="flex items-center justify-between px-6 h-full">
            <div class="flex items-center space-x-4">
                <h1 class="text-xl font-medium text-title-gray">外协工序管理</h1>
                <span class="text-aux-gray text-sm">/ 工艺管理子系统 / 外协工序管理</span>
            </div>
            <div class="flex items-center space-x-3">
                <button @click="exportData" 
                        class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    导出
                </button>
                <button @click="showSupplierModal = true" 
                        class="px-4 py-2 bg-warning text-white rounded-md hover:bg-yellow-600 transition-colors">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                    </svg>
                    供应商管理
                </button>
                <button @click="showCreateModal = true" 
                        class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600 transition-colors">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    新建外协工序
                </button>
            </div>
        </div>
    </header>

    <!-- 搜索和筛选栏 -->
    <div class="bg-white border-b border-border-gray px-6 py-4">
        <div class="flex items-center space-x-4">
            <div class="flex-1 max-w-md">
                <div class="relative">
                    <input type="text" x-model="searchQuery" @input="filterProcesses"
                           placeholder="搜索工序编码、名称、供应商..."
                           class="w-full pl-8 pr-4 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                    <svg class="absolute left-2 top-2.5 h-4 w-4 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                    </svg>
                </div>
            </div>
            <select x-model="typeFilter" @change="filterProcesses"
                    class="px-3 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                <option value="">全部类型</option>
                <option value="完全外协">完全外协</option>
                <option value="部分外协">部分外协</option>
                <option value="可选外协">可选外协</option>
            </select>
            <select x-model="statusFilter" @change="filterProcesses"
                    class="px-3 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                <option value="">全部状态</option>
                <option value="生效">生效</option>
                <option value="停用">停用</option>
                <option value="待审核">待审核</option>
            </select>
            <select x-model="supplierFilter" @change="filterProcesses"
                    class="px-3 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                <option value="">全部供应商</option>
                <template x-for="supplier in suppliers" :key="supplier.id">
                    <option :value="supplier.id" x-text="supplier.name"></option>
                </template>
            </select>
            <button @click="resetFilters" class="px-3 py-2 text-sm text-aux-gray border border-border-gray rounded-md hover:bg-gray-50">
                重置
            </button>
        </div>
    </div>

    <!-- 主内容区域 -->
    <main class="flex h-screen pt-1">
        <!-- 左侧统计面板 -->
        <div class="w-80 bg-white border-r border-border-gray">
            <div class="p-4 border-b border-border-gray">
                <h3 class="text-sm font-medium text-title-gray">外协统计</h3>
            </div>
            
            <!-- 统计卡片 -->
            <div class="p-4 space-y-4">
                <div class="p-4 bg-blue-50 border border-blue-200 rounded-md">
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="text-2xl font-bold text-blue-600" x-text="outsourcingProcesses.length"></div>
                            <div class="text-sm text-blue-600">外协工序总数</div>
                        </div>
                        <svg class="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                        </svg>
                    </div>
                </div>
                
                <div class="p-4 bg-green-50 border border-green-200 rounded-md">
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="text-2xl font-bold text-green-600" x-text="suppliers.length"></div>
                            <div class="text-sm text-green-600">合作供应商</div>
                        </div>
                        <svg class="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                        </svg>
                    </div>
                </div>
                
                <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="text-2xl font-bold text-yellow-600" x-text="getActiveProcessCount()"></div>
                            <div class="text-sm text-yellow-600">生效工序</div>
                        </div>
                        <svg class="w-8 h-8 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                </div>
            </div>
            
            <!-- 外协类型分布 -->
            <div class="p-4 border-t border-border-gray">
                <h4 class="text-sm font-medium text-title-gray mb-3">外协类型分布</h4>
                <div class="space-y-2">
                    <template x-for="type in outsourcingTypes" :key="type.name">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 rounded-full" :style="`background-color: ${type.color}`"></div>
                                <span class="text-sm text-body-gray" x-text="type.name"></span>
                            </div>
                            <span class="text-sm font-medium text-title-gray" x-text="type.count"></span>
                        </div>
                    </template>
                </div>
            </div>
            
            <!-- 供应商排行 -->
            <div class="p-4 border-t border-border-gray">
                <h4 class="text-sm font-medium text-title-gray mb-3">主要供应商</h4>
                <div class="space-y-2">
                    <template x-for="(supplier, index) in topSuppliers" :key="supplier.id">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <span class="w-5 h-5 bg-primary text-white text-xs rounded-full flex items-center justify-center" 
                                      x-text="index + 1"></span>
                                <span class="text-sm text-body-gray" x-text="supplier.name"></span>
                            </div>
                            <span class="text-sm text-aux-gray" x-text="`${supplier.processCount}个工序`"></span>
                        </div>
                    </template>
                </div>
            </div>
        </div>

        <!-- 中间外协工序列表区域 -->
        <div class="flex-1 bg-white border-r border-border-gray">
            <div class="p-4 border-b border-border-gray">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium text-title-gray">外协工序列表</h3>
                        <p class="text-xs text-aux-gray mt-1" x-text="`共 ${filteredProcesses.length} 个外协工序`"></p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button @click="viewMode = 'list'" 
                                :class="viewMode === 'list' ? 'bg-primary text-white' : 'bg-gray-100 text-body-gray'"
                                class="px-3 py-1 text-sm rounded-md">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"/>
                            </svg>
                        </button>
                        <button @click="viewMode = 'card'" 
                                :class="viewMode === 'card' ? 'bg-primary text-white' : 'bg-gray-100 text-body-gray'"
                                class="px-3 py-1 text-sm rounded-md">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="flex-1 overflow-auto p-4">
                <!-- 列表视图 -->
                <template x-if="viewMode === 'list'">
                    <div class="space-y-2">
                        <template x-for="process in filteredProcesses" :key="process.id">
                            <div class="p-4 border border-border-gray rounded-md hover:border-primary cursor-pointer transition-colors"
                                 :class="selectedProcess?.id === process.id ? 'border-primary bg-blue-50' : ''"
                                 @click="selectProcess(process)">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-3 mb-2">
                                            <h4 class="text-sm font-medium text-title-gray" x-text="process.processName"></h4>
                                            <span class="px-2 py-1 text-xs rounded-full"
                                                  :class="getStatusClass(process.status)"
                                                  x-text="process.status"></span>
                                            <span class="px-2 py-1 text-xs rounded-full"
                                                  :class="getTypeClass(process.outsourcingType)"
                                                  x-text="process.outsourcingType"></span>
                                        </div>
                                        <div class="text-xs text-aux-gray mb-1" x-text="`工序编码: ${process.processCode}`"></div>
                                        <div class="text-sm text-body-gray" x-text="process.description"></div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm text-body-gray mb-1">主供应商</div>
                                        <div class="text-sm font-medium text-title-gray" x-text="process.primarySupplier"></div>
                                        <div class="text-xs text-aux-gray" x-text="`¥${process.unitPrice}/件`"></div>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between mt-3 pt-3 border-t border-border-gray">
                                    <div class="flex items-center space-x-4 text-xs text-aux-gray">
                                        <span x-text="`交期: ${process.leadTime}天`"></span>
                                        <span x-text="`质量等级: ${process.qualityLevel}`"></span>
                                        <span x-text="`更新: ${process.updateTime}`"></span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button @click.stop="editProcess(process)" class="text-primary hover:text-blue-600">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                            </svg>
                                        </button>
                                        <button @click.stop="manageSuppliers(process)" class="text-warning hover:text-yellow-600">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                            </svg>
                                        </button>
                                        <button @click.stop="deleteProcess(process)" class="text-error hover:text-red-600">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </template>

                <!-- 卡片视图 -->
                <template x-if="viewMode === 'card'">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <template x-for="process in filteredProcesses" :key="process.id">
                            <div class="p-4 border border-border-gray rounded-md hover:border-primary cursor-pointer transition-colors"
                                 :class="selectedProcess?.id === process.id ? 'border-primary bg-blue-50' : ''"
                                 @click="selectProcess(process)">
                                <div class="flex items-center justify-between mb-3">
                                    <h4 class="text-sm font-medium text-title-gray" x-text="process.processName"></h4>
                                    <span class="px-2 py-1 text-xs rounded-full"
                                          :class="getStatusClass(process.status)"
                                          x-text="process.status"></span>
                                </div>
                                <div class="text-xs text-aux-gray mb-2" x-text="`编码: ${process.processCode}`"></div>
                                <div class="text-sm text-body-gray mb-3" x-text="process.description"></div>
                                <div class="space-y-2 text-sm">
                                    <div class="flex items-center justify-between">
                                        <span class="text-aux-gray">外协类型:</span>
                                        <span class="text-body-gray" x-text="process.outsourcingType"></span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-aux-gray">主供应商:</span>
                                        <span class="text-body-gray" x-text="process.primarySupplier"></span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-aux-gray">单价:</span>
                                        <span class="font-medium text-title-gray" x-text="`¥${process.unitPrice}/件`"></span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-aux-gray">交期:</span>
                                        <span class="text-body-gray" x-text="`${process.leadTime}天`"></span>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </template>

                <!-- 空状态 -->
                <template x-if="filteredProcesses.length === 0">
                    <div class="text-center py-12 text-aux-gray">
                        <svg class="w-12 h-12 mx-auto mb-4 text-disabled-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                        </svg>
                        <p class="text-sm">暂无外协工序数据</p>
                        <p class="text-xs mt-1">请调整筛选条件或创建新的外协工序</p>
                    </div>
                </template>
            </div>
        </div>

        <!-- 右侧详情面板 -->
        <div class="w-96 bg-white" x-show="selectedProcess">
            <div class="p-4 border-b border-border-gray">
                <div class="flex items-center justify-between">
                    <h3 class="text-sm font-medium text-title-gray">外协工序详情</h3>
                    <button @click="selectedProcess = null" class="text-aux-gray hover:text-title-gray">
                        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
            </div>
            
            <div class="flex-1 overflow-auto p-4" x-show="selectedProcess">
                <template x-if="selectedProcess">
                    <div class="space-y-6">
                        <!-- 基本信息 -->
                        <div>
                            <h4 class="text-sm font-medium text-title-gray mb-3">基本信息</h4>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">工序名称</label>
                                    <div class="text-sm text-body-gray" x-text="selectedProcess.processName"></div>
                                </div>
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">工序编码</label>
                                    <div class="text-sm text-body-gray" x-text="selectedProcess.processCode"></div>
                                </div>
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">外协类型</label>
                                    <span class="px-2 py-1 text-xs rounded-full"
                                          :class="getTypeClass(selectedProcess.outsourcingType)"
                                          x-text="selectedProcess.outsourcingType"></span>
                                </div>
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">状态</label>
                                    <span class="px-2 py-1 text-xs rounded-full"
                                          :class="getStatusClass(selectedProcess.status)"
                                          x-text="selectedProcess.status"></span>
                                </div>
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">描述</label>
                                    <div class="text-sm text-body-gray" x-text="selectedProcess.description"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 供应商信息 -->
                        <div>
                            <h4 class="text-sm font-medium text-title-gray mb-3">供应商信息</h4>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">主供应商</label>
                                    <div class="text-sm font-medium text-title-gray" x-text="selectedProcess.primarySupplier"></div>
                                </div>
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">备选供应商</label>
                                    <div class="text-sm text-body-gray" x-text="selectedProcess.backupSuppliers?.join(', ') || '无'"></div>
                                </div>
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">供应商评级</label>
                                    <div class="flex items-center space-x-1">
                                        <template x-for="i in 5" :key="i">
                                            <svg class="w-4 h-4" :class="i <= (selectedProcess.supplierRating || 0) ? 'text-yellow-400' : 'text-gray-300'" 
                                                 fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                            </svg>
                                        </template>
                                        <span class="text-xs text-aux-gray ml-2" x-text="`${selectedProcess.supplierRating || 0}/5`"></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 成本与交期 -->
                        <div>
                            <h4 class="text-sm font-medium text-title-gray mb-3">成本与交期</h4>
                            <div class="space-y-2">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-body-gray">单价</span>
                                    <span class="text-sm font-medium text-title-gray" x-text="`¥${selectedProcess.unitPrice}/件`"></span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-body-gray">标准交期</span>
                                    <span class="text-sm text-body-gray" x-text="`${selectedProcess.leadTime}天`"></span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-body-gray">最小起订量</span>
                                    <span class="text-sm text-body-gray" x-text="`${selectedProcess.minOrderQty || 1}件`"></span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-body-gray">质量等级</span>
                                    <span class="text-sm text-body-gray" x-text="selectedProcess.qualityLevel"></span>
                                </div>
                            </div>
                        </div>

                        <!-- 技术要求 -->
                        <div>
                            <h4 class="text-sm font-medium text-title-gray mb-3">技术要求</h4>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">工艺要求</label>
                                    <div class="text-sm text-body-gray" x-text="selectedProcess.technicalRequirements || '按标准执行'"></div>
                                </div>
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">质量标准</label>
                                    <div class="text-sm text-body-gray" x-text="selectedProcess.qualityStandard || '按行业标准'"></div>
                                </div>
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">检验要求</label>
                                    <div class="text-sm text-body-gray" x-text="selectedProcess.inspectionRequirement || '出厂检验'"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="space-y-2">
                            <button @click="editProcess(selectedProcess)" 
                                    class="w-full px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600">
                                编辑工序
                            </button>
                            <button @click="manageSuppliers(selectedProcess)" 
                                    class="w-full px-4 py-2 bg-warning text-white rounded-md hover:bg-yellow-600">
                                管理供应商
                            </button>
                            <button @click="viewCostAnalysis(selectedProcess)" 
                                    class="w-full px-4 py-2 bg-success text-white rounded-md hover:bg-green-600">
                                成本分析
                            </button>
                            <button @click="viewHistory(selectedProcess)" 
                                    class="w-full px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
                                查看历史
                            </button>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </main>

    <script>
        function outsourcingProcessApp() {
            return {
                // UI状态
                viewMode: 'list',
                showCreateModal: false,
                showSupplierModal: false,
                selectedProcess: null,
                searchQuery: '',
                typeFilter: '',
                statusFilter: '',
                supplierFilter: '',

                // 供应商数据
                suppliers: [
                    { id: 'SUP001', name: '上海精密加工厂', processCount: 8 },
                    { id: 'SUP002', name: '苏州玻璃工艺公司', processCount: 6 },
                    { id: 'SUP003', name: '无锡表面处理中心', processCount: 4 },
                    { id: 'SUP004', name: '常州包装材料厂', processCount: 3 },
                    { id: 'SUP005', name: '南京质检服务中心', processCount: 2 }
                ],

                // 外协工序数据
                outsourcingProcesses: [
                    {
                        id: 'OUT001',
                        processCode: 'OP-OUT-001',
                        processName: '玻璃表面镀膜',
                        outsourcingType: '完全外协',
                        status: '生效',
                        description: '在玻璃表面镀制功能性薄膜',
                        primarySupplier: '上海精密加工厂',
                        backupSuppliers: ['苏州玻璃工艺公司'],
                        unitPrice: 25.50,
                        leadTime: 7,
                        minOrderQty: 50,
                        qualityLevel: 'A级',
                        supplierRating: 5,
                        technicalRequirements: '镀膜厚度均匀，透光率≥85%',
                        qualityStandard: 'GB/T 18915.1-2013',
                        inspectionRequirement: '逐片检验，提供检验报告',
                        updateTime: '2025-07-30'
                    },
                    {
                        id: 'OUT002',
                        processCode: 'OP-OUT-002',
                        processName: '异形切割加工',
                        outsourcingType: '部分外协',
                        status: '生效',
                        description: '复杂异形玻璃的精密切割',
                        primarySupplier: '苏州玻璃工艺公司',
                        backupSuppliers: ['上海精密加工厂'],
                        unitPrice: 18.00,
                        leadTime: 5,
                        minOrderQty: 20,
                        qualityLevel: 'A级',
                        supplierRating: 4,
                        technicalRequirements: '尺寸精度±0.5mm，边缘光滑',
                        qualityStandard: 'JC/T 2129-2012',
                        inspectionRequirement: '首件检验，批量抽检',
                        updateTime: '2025-07-29'
                    },
                    {
                        id: 'OUT003',
                        processCode: 'OP-OUT-003',
                        processName: '表面喷砂处理',
                        outsourcingType: '可选外协',
                        status: '生效',
                        description: '玻璃表面喷砂毛化处理',
                        primarySupplier: '无锡表面处理中心',
                        backupSuppliers: [],
                        unitPrice: 12.80,
                        leadTime: 3,
                        minOrderQty: 100,
                        qualityLevel: 'B级',
                        supplierRating: 4,
                        technicalRequirements: '表面粗糙度Ra 3.2-6.3μm',
                        qualityStandard: 'GB/T 1031-2009',
                        inspectionRequirement: '抽样检验',
                        updateTime: '2025-07-28'
                    },
                    {
                        id: 'OUT004',
                        processCode: 'OP-OUT-004',
                        processName: '特殊包装服务',
                        outsourcingType: '完全外协',
                        status: '生效',
                        description: '出口产品的特殊包装要求',
                        primarySupplier: '常州包装材料厂',
                        backupSuppliers: [],
                        unitPrice: 8.50,
                        leadTime: 2,
                        minOrderQty: 200,
                        qualityLevel: 'B级',
                        supplierRating: 3,
                        technicalRequirements: '防潮、防震、标识清晰',
                        qualityStandard: 'GB/T 13384-2008',
                        inspectionRequirement: '包装前检验',
                        updateTime: '2025-07-27'
                    },
                    {
                        id: 'OUT005',
                        processCode: 'OP-OUT-005',
                        processName: '第三方质检',
                        outsourcingType: '完全外协',
                        status: '待审核',
                        description: '委托第三方进行产品质量检验',
                        primarySupplier: '南京质检服务中心',
                        backupSuppliers: [],
                        unitPrice: 35.00,
                        leadTime: 1,
                        minOrderQty: 1,
                        qualityLevel: 'A级',
                        supplierRating: 5,
                        technicalRequirements: '按客户标准执行检验',
                        qualityStandard: '客户标准',
                        inspectionRequirement: '出具权威检验报告',
                        updateTime: '2025-07-26'
                    },
                    {
                        id: 'OUT006',
                        processCode: 'OP-OUT-006',
                        processName: '激光雕刻',
                        outsourcingType: '部分外协',
                        status: '停用',
                        description: '玻璃表面激光雕刻图案',
                        primarySupplier: '上海精密加工厂',
                        backupSuppliers: [],
                        unitPrice: 45.00,
                        leadTime: 4,
                        minOrderQty: 10,
                        qualityLevel: 'A级',
                        supplierRating: 4,
                        technicalRequirements: '图案清晰，深度一致',
                        qualityStandard: '企业标准',
                        inspectionRequirement: '全检',
                        updateTime: '2025-07-25'
                    }
                ],

                filteredProcesses: [],

                init() {
                    this.filteredProcesses = [...this.outsourcingProcesses];
                },

                // 计算属性
                get outsourcingTypes() {
                    const types = [
                        { name: '完全外协', color: '#1890FF', count: 0 },
                        { name: '部分外协', color: '#52C41A', count: 0 },
                        { name: '可选外协', color: '#FAAD14', count: 0 }
                    ];

                    this.outsourcingProcesses.forEach(process => {
                        const type = types.find(t => t.name === process.outsourcingType);
                        if (type) type.count++;
                    });

                    return types;
                },

                get topSuppliers() {
                    return this.suppliers
                        .sort((a, b) => b.processCount - a.processCount)
                        .slice(0, 5);
                },

                // 筛选方法
                filterProcesses() {
                    let filtered = [...this.outsourcingProcesses];

                    // 按搜索关键词筛选
                    if (this.searchQuery.trim()) {
                        const query = this.searchQuery.toLowerCase();
                        filtered = filtered.filter(process =>
                            process.processCode.toLowerCase().includes(query) ||
                            process.processName.toLowerCase().includes(query) ||
                            process.primarySupplier.toLowerCase().includes(query) ||
                            process.description.toLowerCase().includes(query)
                        );
                    }

                    // 按外协类型筛选
                    if (this.typeFilter) {
                        filtered = filtered.filter(process => process.outsourcingType === this.typeFilter);
                    }

                    // 按状态筛选
                    if (this.statusFilter) {
                        filtered = filtered.filter(process => process.status === this.statusFilter);
                    }

                    // 按供应商筛选
                    if (this.supplierFilter) {
                        const supplierName = this.suppliers.find(s => s.id === this.supplierFilter)?.name;
                        if (supplierName) {
                            filtered = filtered.filter(process =>
                                process.primarySupplier === supplierName ||
                                process.backupSuppliers?.includes(supplierName)
                            );
                        }
                    }

                    this.filteredProcesses = filtered;
                },

                resetFilters() {
                    this.searchQuery = '';
                    this.typeFilter = '';
                    this.statusFilter = '';
                    this.supplierFilter = '';
                    this.filterProcesses();
                },

                // 工序操作
                selectProcess(process) {
                    this.selectedProcess = process;
                },

                editProcess(process) {
                    alert(`编辑外协工序: ${process.processName}`);
                    // 这里应该打开编辑模态框
                },

                manageSuppliers(process) {
                    alert(`管理工序 ${process.processName} 的供应商`);
                    // 这里应该打开供应商管理界面
                },

                deleteProcess(process) {
                    if (confirm(`确定删除外协工序 ${process.processName} 吗？此操作不可撤销。`)) {
                        const index = this.outsourcingProcesses.findIndex(p => p.id === process.id);
                        if (index > -1) {
                            this.outsourcingProcesses.splice(index, 1);
                            this.filterProcesses();

                            if (this.selectedProcess?.id === process.id) {
                                this.selectedProcess = null;
                            }

                            alert('外协工序删除成功');
                        }
                    }
                },

                viewCostAnalysis(process) {
                    alert(`查看工序 ${process.processName} 的成本分析`);
                },

                viewHistory(process) {
                    alert(`查看工序 ${process.processName} 的变更历史`);
                },

                // 统计方法
                getActiveProcessCount() {
                    return this.outsourcingProcesses.filter(p => p.status === '生效').length;
                },

                // 批量操作
                exportData() {
                    alert('导出外协工序数据功能');
                },

                // 样式方法
                getStatusClass(status) {
                    const classMap = {
                        '生效': 'bg-green-100 text-green-800',
                        '停用': 'bg-red-100 text-red-800',
                        '待审核': 'bg-yellow-100 text-yellow-800'
                    };
                    return classMap[status] || 'bg-gray-100 text-gray-800';
                },

                getTypeClass(type) {
                    const classMap = {
                        '完全外协': 'bg-blue-100 text-blue-800',
                        '部分外协': 'bg-green-100 text-green-800',
                        '可选外协': 'bg-yellow-100 text-yellow-800'
                    };
                    return classMap[type] || 'bg-gray-100 text-gray-800';
                }
            }
        }
    </script>
</body>
</html>
