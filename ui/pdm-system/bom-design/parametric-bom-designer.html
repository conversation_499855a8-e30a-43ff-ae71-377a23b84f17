<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>参数化BOM设计器 - 玻璃深加工ERP系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1890FF',
                        success: '#52C41A',
                        warning: '#FAAD14',
                        error: '#F5222D',
                        'title-gray': '#262626',
                        'body-gray': '#595959',
                        'aux-gray': '#8C8C8C',
                        'disabled-gray': '#BFBFBF',
                        'border-gray': '#D9D9D9',
                        'bg-gray': '#FAFAFA'
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', 'sans-serif']
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-bg-gray font-chinese" x-data="bomDesignerApp()">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-border-gray h-16">
        <div class="flex items-center justify-between px-6 h-full">
            <div class="flex items-center space-x-4">
                <h1 class="text-xl font-medium text-title-gray">参数化BOM设计器</h1>
                <span class="text-aux-gray text-sm">/ 工艺管理子系统 / 参数化BOM设计</span>
                <template x-if="currentBom">
                    <div class="flex items-center space-x-2 ml-4">
                        <span class="text-sm text-body-gray">当前BOM:</span>
                        <span class="text-sm font-medium text-title-gray" x-text="currentBom.name"></span>
                        <span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800" x-text="currentBom.version"></span>
                    </div>
                </template>
            </div>
            <div class="flex items-center space-x-3">
                <button @click="testCalculation" 
                        class="px-4 py-2 bg-warning text-white rounded-md hover:bg-yellow-600 transition-colors">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                    </svg>
                    计算测试
                </button>
                <button @click="previewBom" 
                        class="px-4 py-2 bg-success text-white rounded-md hover:bg-green-600 transition-colors">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                    </svg>
                    预览BOM
                </button>
                <button @click="saveBom" :disabled="saving"
                        class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600 transition-colors disabled:bg-disabled-gray">
                    <span x-show="saving" class="inline-block w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                    <svg x-show="!saving" class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"/>
                    </svg>
                    <span x-text="saving ? '保存中...' : '保存BOM'"></span>
                </button>
            </div>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main class="flex h-screen pt-1">
        <!-- 左侧物料选择区域 -->
        <div class="w-80 bg-white border-r border-border-gray flex flex-col">
            <!-- 物料搜索 -->
            <div class="p-4 border-b border-border-gray">
                <div class="relative mb-3">
                    <input type="text" x-model="materialSearch" @input="filterMaterials"
                           placeholder="搜索物料编码、名称..."
                           class="w-full pl-8 pr-4 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                    <svg class="absolute left-2 top-2.5 h-4 w-4 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                    </svg>
                </div>
                <div class="flex space-x-2">
                    <select x-model="materialTypeFilter" @change="filterMaterials"
                            class="flex-1 px-2 py-1 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                        <option value="">全部类型</option>
                        <option value="原材料">原材料</option>
                        <option value="半成品">半成品</option>
                        <option value="辅料">辅料</option>
                    </select>
                </div>
            </div>

            <!-- 物料分类树 -->
            <div class="flex-1 overflow-auto p-4">
                <h3 class="text-sm font-medium text-title-gray mb-3">物料分类</h3>
                <div class="space-y-1">
                    <template x-for="category in materialCategories" :key="category.id">
                        <div class="space-y-1">
                            <div class="flex items-center p-2 rounded cursor-pointer hover:bg-gray-50"
                                 :class="selectedCategory?.id === category.id ? 'bg-blue-50 border-l-2 border-primary' : ''"
                                 @click="selectCategory(category)">
                                <svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                </svg>
                                <span class="text-sm text-body-gray" x-text="category.name"></span>
                                <span class="text-xs text-aux-gray ml-auto" x-text="`(${category.count})`"></span>
                            </div>
                        </div>
                    </template>
                </div>

                <!-- 物料列表 -->
                <div class="mt-6">
                    <h3 class="text-sm font-medium text-title-gray mb-3">可用物料</h3>
                    <div class="space-y-2 max-h-96 overflow-auto">
                        <template x-for="material in filteredMaterials" :key="material.id">
                            <div class="p-3 border border-border-gray rounded-md cursor-pointer hover:border-primary hover:bg-blue-50"
                                 draggable="true"
                                 @dragstart="startDrag($event, material)"
                                 @click="addMaterialToBom(material)">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <div class="text-sm font-medium text-title-gray" x-text="material.name"></div>
                                        <div class="text-xs text-aux-gray" x-text="material.code"></div>
                                        <div class="text-xs text-aux-gray" x-text="material.specification"></div>
                                    </div>
                                    <span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800" x-text="material.unit"></span>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>

        <!-- 中间BOM设计区域 -->
        <div class="flex-1 flex flex-col">
            <!-- BOM信息栏 -->
            <div class="bg-white border-b border-border-gray px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div>
                            <label class="block text-xs text-aux-gray mb-1">成品物料</label>
                            <select x-model="bomForm.productMaterialId" @change="onProductChange"
                                    class="px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                                <option value="">请选择成品物料</option>
                                <template x-for="product in productMaterials" :key="product.id">
                                    <option :value="product.id" x-text="`${product.name} (${product.code})`"></option>
                                </template>
                            </select>
                        </div>
                        <div>
                            <label class="block text-xs text-aux-gray mb-1">BOM名称</label>
                            <input type="text" x-model="bomForm.name"
                                   class="px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                                   placeholder="请输入BOM名称">
                        </div>
                        <div>
                            <label class="block text-xs text-aux-gray mb-1">版本号</label>
                            <input type="text" x-model="bomForm.version" readonly
                                   class="px-3 py-2 border border-border-gray rounded-md bg-gray-50"
                                   placeholder="自动生成">
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button @click="expandAll" class="px-3 py-1 text-sm text-primary border border-primary rounded-md hover:bg-blue-50">
                            全部展开
                        </button>
                        <button @click="collapseAll" class="px-3 py-1 text-sm text-primary border border-primary rounded-md hover:bg-blue-50">
                            全部收起
                        </button>
                    </div>
                </div>
            </div>

            <!-- BOM树状结构 -->
            <div class="flex-1 bg-white overflow-auto">
                <div class="p-6">
                    <template x-if="bomForm.productMaterialId">
                        <div class="space-y-2">
                            <!-- 根节点 -->
                            <div class="flex items-center p-3 bg-blue-50 border border-blue-200 rounded-md">
                                <svg class="w-5 h-5 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
                                </svg>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-title-gray" x-text="getProductMaterial()?.name"></div>
                                    <div class="text-xs text-aux-gray" x-text="getProductMaterial()?.code"></div>
                                </div>
                                <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">成品</span>
                            </div>

                            <!-- BOM子项 -->
                            <div class="ml-8 space-y-2" 
                                 @drop="handleDrop($event)" 
                                 @dragover.prevent 
                                 @dragenter.prevent>
                                <template x-for="(item, index) in bomItems" :key="item.id">
                                    <div class="bom-item" :data-level="item.level">
                                        <div class="flex items-center p-3 border border-border-gray rounded-md hover:border-primary"
                                             :class="selectedBomItem?.id === item.id ? 'border-primary bg-blue-50' : ''"
                                             @click="selectBomItem(item)">
                                            <!-- 层级缩进 -->
                                            <div :style="`margin-left: ${(item.level - 1) * 20}px`" class="flex items-center">
                                                <!-- 展开/收起图标 -->
                                                <template x-if="hasChildren(item)">
                                                    <button @click.stop="toggleExpand(item)" class="mr-2">
                                                        <svg class="w-4 h-4 text-aux-gray" :class="item.expanded ? 'transform rotate-90' : ''" 
                                                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                                        </svg>
                                                    </button>
                                                </template>
                                                <template x-if="!hasChildren(item)">
                                                    <div class="w-6 h-4 mr-2"></div>
                                                </template>

                                                <!-- 物料图标 -->
                                                <svg class="w-4 h-4 mr-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                                </svg>

                                                <!-- 物料信息 -->
                                                <div class="flex-1">
                                                    <div class="text-sm font-medium text-title-gray" x-text="item.material.name"></div>
                                                    <div class="text-xs text-aux-gray" x-text="item.material.code"></div>
                                                </div>

                                                <!-- 用量公式 -->
                                                <div class="mx-4 min-w-0 flex-1">
                                                    <div class="text-sm text-body-gray">
                                                        <span class="text-xs text-aux-gray">用量: </span>
                                                        <code class="px-2 py-1 bg-gray-100 rounded text-xs font-mono" x-text="item.quantityFormula || '1'"></code>
                                                        <span class="text-xs text-aux-gray ml-1" x-text="item.material.unit"></span>
                                                    </div>
                                                </div>

                                                <!-- 操作按钮 -->
                                                <div class="flex items-center space-x-2">
                                                    <button @click.stop="editBomItem(item)" class="text-primary hover:text-blue-600">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                                        </svg>
                                                    </button>
                                                    <button @click.stop="deleteBomItem(item)" class="text-error hover:text-red-600">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </template>

                                <!-- 空状态 -->
                                <template x-if="bomItems.length === 0">
                                    <div class="text-center py-12 text-aux-gray">
                                        <svg class="w-12 h-12 mx-auto mb-4 text-disabled-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                        </svg>
                                        <p class="text-sm">请从左侧拖拽物料到此处构建BOM结构</p>
                                        <p class="text-xs mt-1">或点击物料直接添加</p>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </template>

                    <!-- 未选择成品时的提示 -->
                    <template x-if="!bomForm.productMaterialId">
                        <div class="text-center py-12 text-aux-gray">
                            <svg class="w-12 h-12 mx-auto mb-4 text-disabled-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
                            </svg>
                            <p class="text-sm">请先选择成品物料开始设计BOM</p>
                        </div>
                    </template>
                </div>
            </div>
        </div>

        <!-- 右侧配置面板 -->
        <div class="w-96 bg-white border-l border-border-gray flex flex-col">
            <!-- 参数变量配置 -->
            <div class="p-4 border-b border-border-gray">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-sm font-medium text-title-gray">参数变量</h3>
                    <button @click="showVariableModal = true" class="text-primary hover:text-blue-600">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                    </button>
                </div>
                <div class="space-y-2 max-h-32 overflow-auto">
                    <template x-for="variable in variables" :key="variable.name">
                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <div>
                                <span class="text-sm font-medium text-title-gray" x-text="variable.name"></span>
                                <span class="text-xs text-aux-gray ml-2" x-text="variable.type"></span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <button @click="editVariable(variable)" class="text-primary hover:text-blue-600">
                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                    </svg>
                                </button>
                                <button @click="deleteVariable(variable)" class="text-error hover:text-red-600">
                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </template>
                    <template x-if="variables.length === 0">
                        <div class="text-center py-4 text-aux-gray">
                            <p class="text-xs">暂无参数变量</p>
                            <p class="text-xs">点击 + 添加变量</p>
                        </div>
                    </template>
                </div>
            </div>

            <!-- 公式编辑器 -->
            <div class="flex-1 p-4 border-b border-border-gray">
                <h3 class="text-sm font-medium text-title-gray mb-3">公式编辑器</h3>
                <template x-if="selectedBomItem">
                    <div class="space-y-3">
                        <div>
                            <label class="block text-xs text-aux-gray mb-1">选中物料</label>
                            <div class="text-sm text-body-gray" x-text="selectedBomItem.material.name"></div>
                        </div>
                        <div>
                            <label class="block text-xs text-aux-gray mb-1">用量公式</label>
                            <textarea x-model="selectedBomItem.quantityFormula" 
                                      @input="validateFormula"
                                      class="w-full h-20 px-3 py-2 text-sm font-mono border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary resize-none"
                                      placeholder="输入用量计算公式，如: L*W/1000000"></textarea>
                            <div class="text-xs text-aux-gray mt-1">
                                可用变量: <span x-text="variables.map(v => v.name).join(', ')"></span>
                            </div>
                            <template x-if="formulaError">
                                <div class="text-xs text-error mt-1" x-text="formulaError"></div>
                            </template>
                        </div>
                        <div>
                            <label class="block text-xs text-aux-gray mb-1">备注说明</label>
                            <input type="text" x-model="selectedBomItem.remark"
                                   class="w-full px-3 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                                   placeholder="可选的备注信息">
                        </div>
                    </div>
                </template>
                <template x-if="!selectedBomItem">
                    <div class="text-center py-8 text-aux-gray">
                        <svg class="w-8 h-8 mx-auto mb-2 text-disabled-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                        </svg>
                        <p class="text-xs">请选择BOM项进行编辑</p>
                    </div>
                </template>
            </div>

            <!-- 计算测试工具 -->
            <div class="p-4">
                <h3 class="text-sm font-medium text-title-gray mb-3">计算测试</h3>
                <div class="space-y-3">
                    <!-- 参数输入 -->
                    <template x-for="variable in variables" :key="variable.name">
                        <div>
                            <label class="block text-xs text-aux-gray mb-1" x-text="variable.name"></label>
                            <input type="number" x-model="testParams[variable.name]" :placeholder="variable.defaultValue"
                                   class="w-full px-3 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                        </div>
                    </template>
                    
                    <!-- 计算按钮 -->
                    <button @click="calculateTest" :disabled="calculating"
                            class="w-full px-3 py-2 text-sm bg-warning text-white rounded-md hover:bg-yellow-600 disabled:bg-disabled-gray">
                        <span x-show="calculating" class="inline-block w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                        <span x-text="calculating ? '计算中...' : '开始计算'"></span>
                    </button>

                    <!-- 计算结果 -->
                    <template x-if="calculationResults.length > 0">
                        <div class="mt-4">
                            <h4 class="text-xs font-medium text-title-gray mb-2">计算结果</h4>
                            <div class="space-y-1 max-h-32 overflow-auto">
                                <template x-for="result in calculationResults" :key="result.materialId">
                                    <div class="flex justify-between text-xs">
                                        <span class="text-body-gray" x-text="result.materialName"></span>
                                        <span class="font-medium" x-text="`${result.quantity} ${result.unit}`"></span>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </main>

    <script>
        function bomDesignerApp() {
            return {
                // UI状态
                saving: false,
                calculating: false,
                showVariableModal: false,
                selectedCategory: null,
                selectedBomItem: null,
                formulaError: '',

                // 搜索和筛选
                materialSearch: '',
                materialTypeFilter: '',

                // 表单数据
                bomForm: {
                    productMaterialId: '',
                    name: '',
                    version: 'V1.0',
                    description: ''
                },

                // 参数变量
                variables: [
                    { name: 'L', type: '数值', range: '100-3000', defaultValue: '1000', description: '长度(mm)' },
                    { name: 'W', type: '数值', range: '100-2000', defaultValue: '800', description: '宽度(mm)' },
                    { name: 'T', type: '数值', range: '3-25', defaultValue: '6', description: '厚度(mm)' }
                ],

                // BOM项目
                bomItems: [],

                // 测试参数
                testParams: {
                    L: 1000,
                    W: 800,
                    T: 6
                },

                // 计算结果
                calculationResults: [],

                // 当前BOM
                currentBom: {
                    name: '钢化玻璃BOM',
                    version: 'V1.0'
                },

                // 物料分类
                materialCategories: [
                    { id: 1, name: '玻璃原片', count: 25 },
                    { id: 2, name: '胶水辅料', count: 12 },
                    { id: 3, name: '五金配件', count: 8 },
                    { id: 4, name: '包装材料', count: 6 }
                ],

                // 成品物料
                productMaterials: [
                    { id: 'P001', name: '钢化玻璃', code: 'FP-001', specification: '定制规格' },
                    { id: 'P002', name: '夹胶玻璃', code: 'FP-002', specification: '定制规格' },
                    { id: 'P003', name: '中空玻璃', code: 'FP-003', specification: '定制规格' }
                ],

                // 物料列表
                materials: [
                    {
                        id: 'M001',
                        name: '超白玻璃原片',
                        code: 'RM-001',
                        type: '原材料',
                        specification: '2440×3660×6mm',
                        unit: '片',
                        category: '玻璃原片'
                    },
                    {
                        id: 'M002',
                        name: 'PVB胶片',
                        code: 'RM-002',
                        type: '原材料',
                        specification: '0.38mm透明',
                        unit: '平方米',
                        category: '胶水辅料'
                    },
                    {
                        id: 'M003',
                        name: '结构胶',
                        code: 'RM-003',
                        type: '辅料',
                        specification: '590ml/支',
                        unit: '支',
                        category: '胶水辅料'
                    },
                    {
                        id: 'M004',
                        name: '铝合金边框',
                        code: 'RM-004',
                        type: '原材料',
                        specification: '20×30mm',
                        unit: '米',
                        category: '五金配件'
                    }
                ],

                filteredMaterials: [],

                init() {
                    this.filteredMaterials = [...this.materials];
                    // 初始化一些示例BOM项
                    this.bomItems = [
                        {
                            id: 'B001',
                            material: this.materials[0],
                            quantityFormula: 'L*W/1000000*1.1',
                            level: 1,
                            parentId: null,
                            expanded: true,
                            remark: '玻璃原片，含10%损耗'
                        },
                        {
                            id: 'B002',
                            material: this.materials[1],
                            quantityFormula: 'L*W/1000000*0.9',
                            level: 1,
                            parentId: null,
                            expanded: true,
                            remark: 'PVB胶片'
                        }
                    ];
                },

                // 物料筛选
                filterMaterials() {
                    let filtered = [...this.materials];

                    if (this.materialSearch) {
                        const query = this.materialSearch.toLowerCase();
                        filtered = filtered.filter(material =>
                            material.name.toLowerCase().includes(query) ||
                            material.code.toLowerCase().includes(query)
                        );
                    }

                    if (this.materialTypeFilter) {
                        filtered = filtered.filter(material => material.type === this.materialTypeFilter);
                    }

                    if (this.selectedCategory) {
                        filtered = filtered.filter(material => material.category === this.selectedCategory.name);
                    }

                    this.filteredMaterials = filtered;
                },

                selectCategory(category) {
                    this.selectedCategory = category;
                    this.filterMaterials();
                },

                // 获取成品物料
                getProductMaterial() {
                    return this.productMaterials.find(p => p.id === this.bomForm.productMaterialId);
                },

                onProductChange() {
                    const product = this.getProductMaterial();
                    if (product) {
                        this.bomForm.name = `${product.name}参数化BOM`;
                    }
                },

                // 拖拽处理
                startDrag(event, material) {
                    event.dataTransfer.setData('material', JSON.stringify(material));
                },

                handleDrop(event) {
                    event.preventDefault();
                    const materialData = event.dataTransfer.getData('material');
                    if (materialData) {
                        const material = JSON.parse(materialData);
                        this.addMaterialToBom(material);
                    }
                },

                // 添加物料到BOM
                addMaterialToBom(material) {
                    if (!this.bomForm.productMaterialId) {
                        alert('请先选择成品物料');
                        return;
                    }

                    // 检查是否已存在
                    const exists = this.bomItems.find(item => item.material.id === material.id);
                    if (exists) {
                        alert('该物料已存在于BOM中');
                        return;
                    }

                    const newItem = {
                        id: 'B' + Date.now(),
                        material: material,
                        quantityFormula: '1',
                        level: 1,
                        parentId: null,
                        expanded: true,
                        remark: ''
                    };

                    this.bomItems.push(newItem);
                },

                // BOM项操作
                selectBomItem(item) {
                    this.selectedBomItem = item;
                    this.formulaError = '';
                },

                editBomItem(item) {
                    this.selectedBomItem = item;
                },

                deleteBomItem(item) {
                    if (confirm(`确定删除物料 ${item.material.name} 吗？`)) {
                        const index = this.bomItems.findIndex(i => i.id === item.id);
                        if (index > -1) {
                            this.bomItems.splice(index, 1);
                        }
                        if (this.selectedBomItem?.id === item.id) {
                            this.selectedBomItem = null;
                        }
                    }
                },

                hasChildren(item) {
                    return this.bomItems.some(i => i.parentId === item.id);
                },

                toggleExpand(item) {
                    item.expanded = !item.expanded;
                },

                expandAll() {
                    this.bomItems.forEach(item => item.expanded = true);
                },

                collapseAll() {
                    this.bomItems.forEach(item => item.expanded = false);
                },

                // 公式验证
                validateFormula() {
                    if (!this.selectedBomItem?.quantityFormula) {
                        this.formulaError = '';
                        return;
                    }

                    const formula = this.selectedBomItem.quantityFormula;

                    // 检查变量引用
                    const variableNames = this.variables.map(v => v.name);
                    const usedVariables = formula.match(/[A-Z]+/g) || [];
                    const undefinedVars = usedVariables.filter(v => !variableNames.includes(v));

                    if (undefinedVars.length > 0) {
                        this.formulaError = `引用了未定义的变量: ${undefinedVars.join(', ')}`;
                        return;
                    }

                    // 简单语法检查
                    try {
                        // 替换变量为数值进行测试
                        let testFormula = formula;
                        variableNames.forEach(varName => {
                            testFormula = testFormula.replace(new RegExp(varName, 'g'), '1');
                        });

                        // 检查是否为有效表达式
                        if (!/^[\d+\-*/().\s]+$/.test(testFormula)) {
                            this.formulaError = '公式包含无效字符';
                            return;
                        }

                        // 尝试计算
                        eval(testFormula);
                        this.formulaError = '';
                    } catch (error) {
                        this.formulaError = '公式语法错误';
                    }
                },

                // 变量管理
                editVariable(variable) {
                    alert(`编辑变量: ${variable.name}`);
                },

                deleteVariable(variable) {
                    if (confirm(`确定删除变量 ${variable.name} 吗？`)) {
                        const index = this.variables.findIndex(v => v.name === variable.name);
                        if (index > -1) {
                            this.variables.splice(index, 1);
                        }
                    }
                },

                // 计算测试
                async calculateTest() {
                    this.calculating = true;
                    try {
                        await new Promise(resolve => setTimeout(resolve, 1000));

                        this.calculationResults = [];

                        for (const item of this.bomItems) {
                            try {
                                let formula = item.quantityFormula || '1';

                                // 替换变量为实际值
                                this.variables.forEach(variable => {
                                    const value = this.testParams[variable.name] || variable.defaultValue || 0;
                                    formula = formula.replace(new RegExp(variable.name, 'g'), value);
                                });

                                // 计算结果
                                const quantity = eval(formula);

                                this.calculationResults.push({
                                    materialId: item.material.id,
                                    materialName: item.material.name,
                                    quantity: quantity.toFixed(3),
                                    unit: item.material.unit
                                });
                            } catch (error) {
                                console.error('计算错误:', error);
                            }
                        }
                    } finally {
                        this.calculating = false;
                    }
                },

                // BOM操作
                async saveBom() {
                    if (!this.bomForm.productMaterialId) {
                        alert('请选择成品物料');
                        return;
                    }

                    if (this.bomItems.length === 0) {
                        alert('请添加BOM项');
                        return;
                    }

                    this.saving = true;
                    try {
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        alert('BOM保存成功！');
                    } catch (error) {
                        alert('保存失败，请重试');
                    } finally {
                        this.saving = false;
                    }
                },

                testCalculation() {
                    if (this.variables.length === 0) {
                        alert('请先定义参数变量');
                        return;
                    }
                    this.calculateTest();
                },

                previewBom() {
                    if (this.bomItems.length === 0) {
                        alert('BOM结构为空，无法预览');
                        return;
                    }
                    alert('打开BOM预览窗口');
                }
            }
        }
    </script>
</body>
</html>
