<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BOM固化流程 - 玻璃深加工ERP系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1890FF',
                        success: '#52C41A',
                        warning: '#FAAD14',
                        error: '#F5222D',
                        'title-gray': '#262626',
                        'body-gray': '#595959',
                        'aux-gray': '#8C8C8C',
                        'disabled-gray': '#BFBFBF',
                        'border-gray': '#D9D9D9',
                        'bg-gray': '#FAFAFA'
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', 'sans-serif']
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-bg-gray font-chinese" x-data="bomSolidificationApp()">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-border-gray h-16">
        <div class="flex items-center justify-between px-6 h-full">
            <div class="flex items-center space-x-4">
                <h1 class="text-xl font-medium text-title-gray">BOM固化流程</h1>
                <span class="text-aux-gray text-sm">/ 工艺管理子系统 / BOM固化流程</span>
            </div>
            <div class="flex items-center space-x-3">
                <button @click="previewChanges" 
                        class="px-4 py-2 bg-warning text-white rounded-md hover:bg-yellow-600 transition-colors">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                    </svg>
                    预览变更
                </button>
                <button @click="rejectBom" :disabled="currentBom.status !== '待审核'"
                        class="px-4 py-2 bg-error text-white rounded-md hover:bg-red-600 transition-colors disabled:bg-disabled-gray">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                    驳回
                </button>
                <button @click="solidifyBom" :disabled="currentBom.status !== '待审核'"
                        class="px-4 py-2 bg-success text-white rounded-md hover:bg-green-600 transition-colors disabled:bg-disabled-gray">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    固化BOM
                </button>
            </div>
        </div>
    </header>

    <!-- 订单信息栏 -->
    <div class="bg-white border-b border-border-gray px-6 py-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-6">
                <div>
                    <label class="block text-xs text-aux-gray mb-1">销售订单</label>
                    <div class="text-sm font-medium text-title-gray" x-text="orderInfo.orderNo"></div>
                </div>
                <div>
                    <label class="block text-xs text-aux-gray mb-1">客户名称</label>
                    <div class="text-sm text-body-gray" x-text="orderInfo.customerName"></div>
                </div>
                <div>
                    <label class="block text-xs text-aux-gray mb-1">产品规格</label>
                    <div class="text-sm text-body-gray" x-text="orderInfo.productSpec"></div>
                </div>
                <div>
                    <label class="block text-xs text-aux-gray mb-1">订单数量</label>
                    <div class="text-sm text-body-gray" x-text="`${orderInfo.quantity} ${orderInfo.unit}`"></div>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <div>
                    <label class="block text-xs text-aux-gray mb-1">BOM状态</label>
                    <span class="px-3 py-1 text-sm rounded-full"
                          :class="getBomStatusClass(currentBom.status)"
                          x-text="currentBom.status"></span>
                </div>
                <div>
                    <label class="block text-xs text-aux-gray mb-1">生成时间</label>
                    <div class="text-sm text-body-gray" x-text="currentBom.createTime"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <main class="flex h-screen pt-1">
        <!-- 左侧原始BOM区域 -->
        <div class="w-1/3 bg-white border-r border-border-gray">
            <div class="p-4 border-b border-border-gray">
                <h3 class="text-sm font-medium text-title-gray">原始参数化BOM</h3>
                <p class="text-xs text-aux-gray mt-1">模板版本: <span x-text="originalBom.version"></span></p>
            </div>
            <div class="flex-1 overflow-auto p-4">
                <div class="space-y-2">
                    <template x-for="item in originalBom.items" :key="item.id">
                        <div class="p-3 border border-border-gray rounded-md bg-gray-50">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                    </svg>
                                    <div>
                                        <div class="text-sm font-medium text-title-gray" x-text="item.materialName"></div>
                                        <div class="text-xs text-aux-gray" x-text="item.materialCode"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-aux-gray">公式:</span>
                                <code class="px-2 py-1 bg-white rounded text-xs font-mono" x-text="item.formula"></code>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>

        <!-- 中间BOM快照区域 -->
        <div class="flex-1 bg-white border-r border-border-gray">
            <div class="p-4 border-b border-border-gray">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium text-title-gray">当前BOM快照</h3>
                        <p class="text-xs text-aux-gray mt-1">可进行微调操作</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button @click="showAddMaterialModal = true" 
                                class="px-3 py-1 text-sm bg-primary text-white rounded-md hover:bg-blue-600">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                            </svg>
                            添加物料
                        </button>
                    </div>
                </div>
            </div>
            <div class="flex-1 overflow-auto p-4">
                <div class="space-y-2">
                    <template x-for="(item, index) in currentBom.items" :key="item.id">
                        <div class="p-3 border border-border-gray rounded-md hover:border-primary transition-colors"
                             :class="getItemChangeClass(item.changeType)">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4" :class="getItemIconClass(item.changeType)" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                    </svg>
                                    <div>
                                        <div class="text-sm font-medium text-title-gray" x-text="item.materialName"></div>
                                        <div class="text-xs text-aux-gray" x-text="item.materialCode"></div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <template x-if="item.changeType">
                                        <span class="px-2 py-1 text-xs rounded-full"
                                              :class="getChangeTypeClass(item.changeType)"
                                              x-text="getChangeTypeText(item.changeType)"></span>
                                    </template>
                                    <button @click="editItem(item)" class="text-primary hover:text-blue-600">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                        </svg>
                                    </button>
                                    <button @click="removeItem(index)" class="text-error hover:text-red-600">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div class="grid grid-cols-3 gap-4 text-sm">
                                <div>
                                    <span class="text-aux-gray">用量:</span>
                                    <span class="font-medium ml-1" x-text="`${item.quantity} ${item.unit}`"></span>
                                </div>
                                <div>
                                    <span class="text-aux-gray">单价:</span>
                                    <span class="font-medium ml-1" x-text="`¥${item.unitPrice}`"></span>
                                </div>
                                <div>
                                    <span class="text-aux-gray">小计:</span>
                                    <span class="font-medium ml-1" x-text="`¥${(item.quantity * item.unitPrice).toFixed(2)}`"></span>
                                </div>
                            </div>
                            <template x-if="item.adjustmentReason">
                                <div class="mt-2 pt-2 border-t border-border-gray">
                                    <div class="text-xs text-aux-gray">调整说明:</div>
                                    <div class="text-xs text-body-gray" x-text="item.adjustmentReason"></div>
                                </div>
                            </template>
                        </div>
                    </template>
                </div>
                
                <!-- 成本汇总 -->
                <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
                    <h4 class="text-sm font-medium text-title-gray mb-3">成本汇总</h4>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="flex justify-between">
                            <span class="text-body-gray">物料总成本:</span>
                            <span class="font-medium" x-text="`¥${totalCost.toFixed(2)}`"></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-body-gray">与原始BOM差异:</span>
                            <span class="font-medium" :class="costDifference >= 0 ? 'text-error' : 'text-success'" 
                                  x-text="`${costDifference >= 0 ? '+' : ''}¥${costDifference.toFixed(2)}`"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧调整历史区域 -->
        <div class="w-80 bg-white">
            <div class="p-4 border-b border-border-gray">
                <h3 class="text-sm font-medium text-title-gray">调整历史</h3>
                <p class="text-xs text-aux-gray mt-1">记录所有微调操作</p>
            </div>
            <div class="flex-1 overflow-auto p-4">
                <div class="space-y-3">
                    <template x-for="log in adjustmentLogs" :key="log.id">
                        <div class="p-3 border border-border-gray rounded-md">
                            <div class="flex items-center justify-between mb-2">
                                <span class="px-2 py-1 text-xs rounded-full"
                                      :class="getLogTypeClass(log.type)"
                                      x-text="log.type"></span>
                                <span class="text-xs text-aux-gray" x-text="log.time"></span>
                            </div>
                            <div class="text-sm text-body-gray mb-1" x-text="log.description"></div>
                            <template x-if="log.reason">
                                <div class="text-xs text-aux-gray" x-text="`原因: ${log.reason}`"></div>
                            </template>
                            <div class="text-xs text-aux-gray" x-text="`操作人: ${log.operator}`"></div>
                        </div>
                    </template>
                    
                    <template x-if="adjustmentLogs.length === 0">
                        <div class="text-center py-8 text-aux-gray">
                            <svg class="w-8 h-8 mx-auto mb-2 text-disabled-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                            </svg>
                            <p class="text-xs">暂无调整记录</p>
                        </div>
                    </template>
                </div>
            </div>
            
            <!-- 审核意见区域 -->
            <div class="p-4 border-t border-border-gray">
                <h4 class="text-sm font-medium text-title-gray mb-3">审核意见</h4>
                <textarea x-model="reviewComment" 
                          class="w-full h-20 px-3 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary resize-none"
                          placeholder="请输入审核意见..."></textarea>
                <div class="flex items-center justify-between mt-3 text-xs text-aux-gray">
                    <span>审核人: 当前用户</span>
                    <span x-text="new Date().toLocaleString('zh-CN')"></span>
                </div>
            </div>
        </div>
    </main>

    <script>
        function bomSolidificationApp() {
            return {
                // UI状态
                showAddMaterialModal: false,
                reviewComment: '',

                // 订单信息
                orderInfo: {
                    orderNo: 'SO-2025-001',
                    customerName: '上海建筑玻璃有限公司',
                    productSpec: '钢化玻璃 2000×1500×6mm',
                    quantity: 100,
                    unit: '片'
                },

                // 当前BOM快照
                currentBom: {
                    id: 'BOM-SNAP-001',
                    status: '待审核',
                    createTime: '2025-07-31 14:30',
                    items: [
                        {
                            id: 'ITEM001',
                            materialName: '超白玻璃原片',
                            materialCode: 'RM-001',
                            quantity: 110,
                            unit: '片',
                            unitPrice: 85.50,
                            changeType: 'modified',
                            adjustmentReason: '增加10%损耗系数'
                        },
                        {
                            id: 'ITEM002',
                            materialName: 'PVB胶片',
                            materialCode: 'RM-002',
                            quantity: 300,
                            unit: '平方米',
                            unitPrice: 12.80,
                            changeType: null
                        },
                        {
                            id: 'ITEM003',
                            materialName: '结构胶',
                            materialCode: 'RM-003',
                            quantity: 5,
                            unit: '支',
                            unitPrice: 45.00,
                            changeType: 'added',
                            adjustmentReason: '增加密封胶用于边缘处理'
                        },
                        {
                            id: 'ITEM004',
                            materialName: '包装膜',
                            materialCode: 'RM-004',
                            quantity: 200,
                            unit: '米',
                            unitPrice: 2.50,
                            changeType: 'added',
                            adjustmentReason: '客户要求特殊包装'
                        }
                    ]
                },

                // 原始参数化BOM
                originalBom: {
                    version: 'V2.1',
                    items: [
                        {
                            id: 'ORIG001',
                            materialName: '超白玻璃原片',
                            materialCode: 'RM-001',
                            formula: 'L*W/1000000*1.05'
                        },
                        {
                            id: 'ORIG002',
                            materialName: 'PVB胶片',
                            materialCode: 'RM-002',
                            formula: 'L*W/1000000*0.9'
                        }
                    ]
                },

                // 调整日志
                adjustmentLogs: [
                    {
                        id: 'LOG001',
                        type: '修改用量',
                        description: '超白玻璃原片用量从100片调整为110片',
                        reason: '增加10%损耗系数',
                        operator: '张工程师',
                        time: '2025-07-31 14:35'
                    },
                    {
                        id: 'LOG002',
                        type: '添加物料',
                        description: '新增结构胶 5支',
                        reason: '增加密封胶用于边缘处理',
                        operator: '张工程师',
                        time: '2025-07-31 14:40'
                    },
                    {
                        id: 'LOG003',
                        type: '添加物料',
                        description: '新增包装膜 200米',
                        reason: '客户要求特殊包装',
                        operator: '张工程师',
                        time: '2025-07-31 14:45'
                    }
                ],

                init() {
                    // 初始化逻辑
                },

                // 计算属性
                get totalCost() {
                    return this.currentBom.items.reduce((sum, item) => {
                        return sum + (item.quantity * item.unitPrice);
                    }, 0);
                },

                get costDifference() {
                    // 模拟原始成本
                    const originalCost = 9500;
                    return this.totalCost - originalCost;
                },

                // 状态样式方法
                getBomStatusClass(status) {
                    const classMap = {
                        '待审核': 'bg-yellow-100 text-yellow-800',
                        '审核中': 'bg-blue-100 text-blue-800',
                        '已固化': 'bg-green-100 text-green-800',
                        '已驳回': 'bg-red-100 text-red-800'
                    };
                    return classMap[status] || 'bg-gray-100 text-gray-800';
                },

                getItemChangeClass(changeType) {
                    const classMap = {
                        'added': 'bg-green-50 border-green-200',
                        'modified': 'bg-yellow-50 border-yellow-200',
                        'removed': 'bg-red-50 border-red-200'
                    };
                    return classMap[changeType] || '';
                },

                getItemIconClass(changeType) {
                    const classMap = {
                        'added': 'text-green-500',
                        'modified': 'text-yellow-500',
                        'removed': 'text-red-500'
                    };
                    return classMap[changeType] || 'text-blue-500';
                },

                getChangeTypeClass(changeType) {
                    const classMap = {
                        'added': 'bg-green-100 text-green-800',
                        'modified': 'bg-yellow-100 text-yellow-800',
                        'removed': 'bg-red-100 text-red-800'
                    };
                    return classMap[changeType] || 'bg-gray-100 text-gray-800';
                },

                getChangeTypeText(changeType) {
                    const textMap = {
                        'added': '新增',
                        'modified': '修改',
                        'removed': '删除'
                    };
                    return textMap[changeType] || '';
                },

                getLogTypeClass(type) {
                    const classMap = {
                        '修改用量': 'bg-yellow-100 text-yellow-800',
                        '添加物料': 'bg-green-100 text-green-800',
                        '删除物料': 'bg-red-100 text-red-800',
                        '替换物料': 'bg-blue-100 text-blue-800'
                    };
                    return classMap[type] || 'bg-gray-100 text-gray-800';
                },

                // 物料操作方法
                editItem(item) {
                    const newQuantity = prompt(`请输入新的用量 (当前: ${item.quantity} ${item.unit}):`);
                    if (newQuantity && !isNaN(newQuantity) && newQuantity > 0) {
                        const oldQuantity = item.quantity;
                        item.quantity = parseFloat(newQuantity);

                        if (oldQuantity !== item.quantity) {
                            item.changeType = 'modified';

                            const reason = prompt('请输入调整原因:');
                            if (reason) {
                                item.adjustmentReason = reason;

                                // 添加调整日志
                                this.adjustmentLogs.unshift({
                                    id: 'LOG' + Date.now(),
                                    type: '修改用量',
                                    description: `${item.materialName}用量从${oldQuantity}${item.unit}调整为${item.quantity}${item.unit}`,
                                    reason: reason,
                                    operator: '当前用户',
                                    time: new Date().toLocaleString('zh-CN')
                                });
                            }
                        }
                    }
                },

                removeItem(index) {
                    const item = this.currentBom.items[index];
                    if (confirm(`确定删除物料 ${item.materialName} 吗？`)) {
                        const reason = prompt('请输入删除原因:');
                        if (reason) {
                            // 添加删除日志
                            this.adjustmentLogs.unshift({
                                id: 'LOG' + Date.now(),
                                type: '删除物料',
                                description: `删除物料 ${item.materialName} ${item.quantity}${item.unit}`,
                                reason: reason,
                                operator: '当前用户',
                                time: new Date().toLocaleString('zh-CN')
                            });

                            this.currentBom.items.splice(index, 1);
                        }
                    }
                },

                // BOM操作方法
                previewChanges() {
                    alert('打开变更预览窗口，显示与原始BOM的详细对比');
                },

                async solidifyBom() {
                    if (!this.reviewComment.trim()) {
                        alert('请填写审核意见');
                        return;
                    }

                    if (confirm('确定固化此BOM吗？固化后将无法修改。')) {
                        try {
                            // 模拟固化过程
                            this.currentBom.status = '处理中';
                            await new Promise(resolve => setTimeout(resolve, 2000));

                            this.currentBom.status = '已固化';

                            // 添加固化日志
                            this.adjustmentLogs.unshift({
                                id: 'LOG' + Date.now(),
                                type: 'BOM固化',
                                description: 'BOM已成功固化并传递给生产系统',
                                reason: this.reviewComment,
                                operator: '当前用户',
                                time: new Date().toLocaleString('zh-CN')
                            });

                            alert('BOM固化成功！已传递给MRP和生产系统。');
                        } catch (error) {
                            this.currentBom.status = '待审核';
                            alert('固化失败，请重试');
                        }
                    }
                },

                async rejectBom() {
                    if (!this.reviewComment.trim()) {
                        alert('请填写驳回原因');
                        return;
                    }

                    if (confirm('确定驳回此BOM吗？')) {
                        this.currentBom.status = '已驳回';

                        // 添加驳回日志
                        this.adjustmentLogs.unshift({
                            id: 'LOG' + Date.now(),
                            type: 'BOM驳回',
                            description: 'BOM审核被驳回',
                            reason: this.reviewComment,
                            operator: '当前用户',
                            time: new Date().toLocaleString('zh-CN')
                        });

                        alert('BOM已驳回，将返回给工艺工程师重新处理');
                    }
                }
            }
        }
    </script>
</body>
</html>
