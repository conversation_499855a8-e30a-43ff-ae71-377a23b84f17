<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工艺路线设计器 - 玻璃深加工ERP系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1890FF',
                        success: '#52C41A',
                        warning: '#FAAD14',
                        error: '#F5222D',
                        'title-gray': '#262626',
                        'body-gray': '#595959',
                        'aux-gray': '#8C8C8C',
                        'disabled-gray': '#BFBFBF',
                        'border-gray': '#D9D9D9',
                        'bg-gray': '#FAFAFA'
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        .canvas-grid {
            background-image: 
                linear-gradient(to right, #f0f0f0 1px, transparent 1px),
                linear-gradient(to bottom, #f0f0f0 1px, transparent 1px);
            background-size: 20px 20px;
        }
        
        .process-node {
            transition: all 0.2s ease;
        }
        
        .process-node:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .process-node.selected {
            border: 2px solid #1890FF;
            box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.2);
        }
        
        .connection-line {
            stroke: #1890FF;
            stroke-width: 2;
            fill: none;
            marker-end: url(#arrowhead);
        }
        
        .dragging {
            cursor: grabbing;
        }
    </style>
</head>
<body class="bg-bg-gray font-chinese" x-data="processDesignerApp()">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-border-gray h-16">
        <div class="flex items-center justify-between px-6 h-full">
            <div class="flex items-center space-x-4">
                <h1 class="text-xl font-medium text-title-gray">工艺路线设计器</h1>
                <span class="text-aux-gray text-sm">/ 工艺管理子系统 / 工艺路线设计</span>
            </div>
            <div class="flex items-center space-x-3">
                <button @click="validateRoute" class="px-4 py-2 bg-warning text-white rounded-md hover:bg-yellow-600 transition-colors">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    验证路线
                </button>
                <button @click="previewRoute" class="px-4 py-2 bg-success text-white rounded-md hover:bg-green-600 transition-colors">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                    </svg>
                    预览
                </button>
                <div class="flex space-x-2">
                    <button @click="importRoute" class="px-3 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors">
                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"/>
                        </svg>
                        导入
                    </button>
                    <button @click="exportRoute" class="px-3 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors">
                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                        </svg>
                        导出
                    </button>
                    <button @click="saveRoute" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600 transition-colors">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"/>
                        </svg>
                        保存路线
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main class="flex h-screen pt-1">
        <!-- 左侧工具面板 -->
        <div class="w-64 bg-white border-r border-border-gray">
            <div class="p-4">
                <!-- 工序模板库 -->
                <div class="mb-6">
                    <h3 class="text-sm font-medium text-title-gray mb-3">工序模板库</h3>
                    <div class="space-y-2">
                        <div class="text-xs font-medium text-aux-gray mb-2">切割工序</div>
                        <template x-for="template in processTemplates.cutting" :key="template.id">
                            <div class="p-2 bg-blue-50 border border-blue-200 rounded cursor-pointer hover:bg-blue-100"
                                 draggable="true" @dragstart="startDrag($event, template)">
                                <div class="text-sm font-medium text-blue-800" x-text="template.name"></div>
                                <div class="text-xs text-blue-600" x-text="template.description"></div>
                            </div>
                        </template>
                        
                        <div class="text-xs font-medium text-aux-gray mb-2 mt-4">磨边工序</div>
                        <template x-for="template in processTemplates.grinding" :key="template.id">
                            <div class="p-2 bg-green-50 border border-green-200 rounded cursor-pointer hover:bg-green-100"
                                 draggable="true" @dragstart="startDrag($event, template)">
                                <div class="text-sm font-medium text-green-800" x-text="template.name"></div>
                                <div class="text-xs text-green-600" x-text="template.description"></div>
                            </div>
                        </template>
                        
                        <div class="text-xs font-medium text-aux-gray mb-2 mt-4">钢化工序</div>
                        <template x-for="template in processTemplates.tempering" :key="template.id">
                            <div class="p-2 bg-orange-50 border border-orange-200 rounded cursor-pointer hover:bg-orange-100"
                                 draggable="true" @dragstart="startDrag($event, template)">
                                <div class="text-sm font-medium text-orange-800" x-text="template.name"></div>
                                <div class="text-xs text-orange-600" x-text="template.description"></div>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- 特殊节点 -->
                <div class="mb-6">
                    <h3 class="text-sm font-medium text-title-gray mb-3">特殊节点</h3>
                    <div class="space-y-2">
                        <div class="p-2 bg-gray-50 border border-gray-200 rounded cursor-pointer hover:bg-gray-100"
                             draggable="true" @dragstart="startDrag($event, {type: 'start', name: '开始节点'})">
                            <div class="text-sm font-medium text-gray-800">开始节点</div>
                        </div>
                        <div class="p-2 bg-gray-50 border border-gray-200 rounded cursor-pointer hover:bg-gray-100"
                             draggable="true" @dragstart="startDrag($event, {type: 'end', name: '结束节点'})">
                            <div class="text-sm font-medium text-gray-800">结束节点</div>
                        </div>
                        <div class="p-2 bg-purple-50 border border-purple-200 rounded cursor-pointer hover:bg-purple-100"
                             draggable="true" @dragstart="startDrag($event, {type: 'parallel', name: '并行节点'})">
                            <div class="text-sm font-medium text-purple-800">并行节点</div>
                        </div>
                        <div class="p-2 bg-yellow-50 border border-yellow-200 rounded cursor-pointer hover:bg-yellow-100"
                             draggable="true" @dragstart="startDrag($event, {type: 'decision', name: '决策节点'})">
                            <div class="text-sm font-medium text-yellow-800">决策节点</div>
                        </div>
                    </div>
                </div>

                <!-- 工作中心列表 -->
                <div>
                    <h3 class="text-sm font-medium text-title-gray mb-3">工作中心</h3>
                    <div class="space-y-1">
                        <template x-for="center in workCenters" :key="center.id">
                            <div class="p-2 text-sm text-body-gray hover:bg-gray-50 rounded cursor-pointer"
                                 x-text="center.name"></div>
                        </template>
                    </div>
                </div>
            </div>
        </div>

        <!-- 中间设计画布区域 -->
        <div class="flex-1 flex flex-col">
            <!-- 画布工具栏 -->
            <div class="bg-white border-b border-border-gray px-4 py-2">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2">
                            <button @click="zoomOut" class="p-1 text-aux-gray hover:text-title-gray">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM13 10H7"/>
                                </svg>
                            </button>
                            <span class="text-sm text-aux-gray" x-text="`${Math.round(zoomLevel * 100)}%`"></span>
                            <button @click="zoomIn" class="p-1 text-aux-gray hover:text-title-gray">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"/>
                                </svg>
                            </button>
                        </div>
                        
                        <div class="h-4 w-px bg-border-gray"></div>
                        
                        <div class="flex items-center space-x-2">
                            <button @click="connectionMode = !connectionMode"
                                    :class="connectionMode ? 'bg-primary text-white' : 'text-primary hover:bg-blue-50'"
                                    class="px-3 py-1 text-sm rounded">
                                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
                                </svg>
                                连接模式
                            </button>
                            <button @click="clearCanvas" class="px-3 py-1 text-sm text-error hover:bg-red-50 rounded">
                                清空画布
                            </button>
                            <button @click="autoLayout" class="px-3 py-1 text-sm text-primary hover:bg-blue-50 rounded">
                                自动布局
                            </button>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <div class="text-sm text-aux-gray">
                            节点数: <span x-text="processNodes.length"></span>
                        </div>
                        <div class="text-sm text-aux-gray">
                            连接数: <span x-text="connections.length"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 设计画布 -->
            <div class="flex-1 overflow-hidden relative">
                <div class="w-full h-full canvas-grid overflow-auto" 
                     @drop="handleDrop($event)" 
                     @dragover.prevent
                     @click="deselectAll">
                    
                    <!-- SVG连接线层 -->
                    <svg class="absolute inset-0 w-full h-full pointer-events-none" style="z-index: 1;">
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                                    refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#1890FF"/>
                            </marker>
                        </defs>
                        <template x-for="connection in connections" :key="connection.id">
                            <g>
                                <line class="connection-line cursor-pointer"
                                      :x1="connection.x1" :y1="connection.y1"
                                      :x2="connection.x2" :y2="connection.y2"
                                      @click="deleteConnection(connection)"/>
                                <!-- 连接线中点删除按钮 -->
                                <circle :cx="(connection.x1 + connection.x2) / 2"
                                        :cy="(connection.y1 + connection.y2) / 2"
                                        r="8" fill="white" stroke="#ff4d4f" stroke-width="2"
                                        class="cursor-pointer hover:fill-red-50"
                                        @click="deleteConnection(connection)">
                                    <title>删除连接</title>
                                </circle>
                                <text :x="(connection.x1 + connection.x2) / 2"
                                      :y="(connection.y1 + connection.y2) / 2 + 1"
                                      text-anchor="middle" font-size="10" fill="#ff4d4f"
                                      class="pointer-events-none">×</text>
                            </g>
                        </template>
                    </svg>
                    
                    <!-- 工序节点层 -->
                    <div class="relative" style="z-index: 2;">
                        <template x-for="node in processNodes" :key="node.id">
                            <div class="absolute process-node cursor-pointer"
                                 :style="`left: ${node.x}px; top: ${node.y}px; transform: scale(${zoomLevel})`"
                                 :class="[
                                     'bg-white border-2 rounded-lg p-3 min-w-32 shadow-sm',
                                     selectedNode?.id === node.id ? 'selected' : '',
                                     getNodeBorderClass(node.type)
                                 ]"
                                 @click.stop="connectionMode ? completeConnection(node) : selectNode(node)"
                                 @mousedown="startDragNode($event, node)">
                                
                                <!-- 节点内容 -->
                                <div class="text-center">
                                    <div class="text-sm font-medium text-title-gray" x-text="node.name"></div>
                                    <div class="text-xs text-aux-gray mt-1" x-text="node.operationNumber"></div>
                                    <div class="text-xs text-aux-gray" x-text="node.workCenter"></div>
                                </div>
                                
                                <!-- 连接点 -->
                                <div class="absolute -top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary rounded-full border-2 border-white connection-point input-point hover:bg-blue-600 cursor-pointer"
                                     @click.stop="connectionMode && completeConnection(node)"></div>
                                <div class="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary rounded-full border-2 border-white connection-point output-point hover:bg-blue-600 cursor-pointer"
                                     @click.stop="connectionMode && startConnection(node)"></div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧属性面板 -->
        <div class="w-80 bg-white border-l border-border-gray">
            <div class="p-6">
                <h3 class="text-lg font-medium text-title-gray mb-4">工序属性</h3>
                
                <template x-if="selectedNode">
                    <div class="space-y-4">
                        <!-- 基本信息 -->
                        <div>
                            <label class="block text-sm font-medium text-title-gray mb-2">工序号</label>
                            <input type="text" x-model="selectedNode.operationNumber"
                                   class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-title-gray mb-2">工序名称</label>
                            <input type="text" x-model="selectedNode.name"
                                   class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-title-gray mb-2">工作中心</label>
                            <select x-model="selectedNode.workCenter"
                                    class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                                <option value="">请选择工作中心</option>
                                <template x-for="center in workCenters" :key="center.id">
                                    <option :value="center.name" x-text="center.name"></option>
                                </template>
                            </select>
                        </div>
                        
                        <!-- 工时配置 -->
                        <div class="border-t border-border-gray pt-4">
                            <h4 class="text-sm font-medium text-title-gray mb-3">工时配置</h4>
                            
                            <div class="grid grid-cols-2 gap-3">
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">准备工时(分钟)</label>
                                    <input type="number" x-model="selectedNode.setupTime" min="0"
                                           class="w-full px-2 py-1 text-sm border border-border-gray rounded focus:outline-none focus:ring-1 focus:ring-primary">
                                </div>
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">单件工时(分钟)</label>
                                    <input type="number" x-model="selectedNode.unitTime" min="0"
                                           class="w-full px-2 py-1 text-sm border border-border-gray rounded focus:outline-none focus:ring-1 focus:ring-primary">
                                </div>
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">拆卸工时(分钟)</label>
                                    <input type="number" x-model="selectedNode.teardownTime" min="0"
                                           class="w-full px-2 py-1 text-sm border border-border-gray rounded focus:outline-none focus:ring-1 focus:ring-primary">
                                </div>
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">总工时</label>
                                    <div class="px-2 py-1 text-sm bg-gray-50 border border-border-gray rounded"
                                         x-text="`${(selectedNode.setupTime || 0) + (selectedNode.unitTime || 0) + (selectedNode.teardownTime || 0)}分钟`"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 工序参数 -->
                        <div class="border-t border-border-gray pt-4">
                            <h4 class="text-sm font-medium text-title-gray mb-3">工序参数</h4>
                            <textarea x-model="selectedNode.parameters" rows="3"
                                      class="w-full px-3 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                                      placeholder="请输入工序参数和要求..."></textarea>
                        </div>
                        
                        <!-- 质量要求 -->
                        <div class="border-t border-border-gray pt-4">
                            <h4 class="text-sm font-medium text-title-gray mb-3">质量要求</h4>
                            <textarea x-model="selectedNode.qualityRequirements" rows="3"
                                      class="w-full px-3 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                                      placeholder="请输入质量标准和检验要求..."></textarea>
                        </div>
                        
                        <!-- 外协标识 -->
                        <div class="border-t border-border-gray pt-4">
                            <label class="flex items-center">
                                <input type="checkbox" x-model="selectedNode.isOutsourced" class="mr-2">
                                <span class="text-sm text-title-gray">外协工序</span>
                            </label>
                            <div x-show="selectedNode.isOutsourced" class="mt-2">
                                <label class="block text-xs text-aux-gray mb-1">外协供应商</label>
                                <select x-model="selectedNode.supplier"
                                        class="w-full px-3 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                                    <option value="">请选择供应商</option>
                                    <option value="供应商A">供应商A</option>
                                    <option value="供应商B">供应商B</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- 操作按钮 -->
                        <div class="border-t border-border-gray pt-4">
                            <div class="flex space-x-2">
                                <button @click="duplicateNode" class="flex-1 px-3 py-2 text-sm bg-primary text-white rounded hover:bg-blue-600">
                                    复制节点
                                </button>
                                <button @click="deleteNode" class="flex-1 px-3 py-2 text-sm bg-error text-white rounded hover:bg-red-600">
                                    删除节点
                                </button>
                            </div>
                        </div>
                    </div>
                </template>
                
                <template x-if="!selectedNode">
                    <div class="text-center text-aux-gray py-8">
                        <svg class="w-16 h-16 mx-auto mb-4 text-aux-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                        </svg>
                        <p class="text-sm">请选择一个工序节点</p>
                        <p class="text-xs mt-1">在左侧画布中点击节点查看和编辑属性</p>
                    </div>
                </template>
            </div>
        </div>
    </main>

    <script>
        function processDesignerApp() {
            return {
                // UI状态
                selectedNode: null,
                zoomLevel: 1,
                draggedTemplate: null,
                draggedNode: null,
                dragOffset: { x: 0, y: 0 },
                connectionMode: false,
                connectionStart: null,
                
                // 数据
                processNodes: [
                    {
                        id: 1,
                        name: '玻璃切割',
                        type: 'cutting',
                        operationNumber: '010',
                        workCenter: '切割工作中心',
                        x: 200,
                        y: 100,
                        setupTime: 10,
                        unitTime: 5,
                        teardownTime: 5,
                        parameters: '切割精度±0.5mm',
                        qualityRequirements: '边缘平整，无崩边',
                        isOutsourced: false,
                        supplier: ''
                    },
                    {
                        id: 2,
                        name: '边缘磨边',
                        type: 'grinding',
                        operationNumber: '020',
                        workCenter: '磨边工作中心',
                        x: 200,
                        y: 250,
                        setupTime: 15,
                        unitTime: 8,
                        teardownTime: 5,
                        parameters: '磨边粗糙度Ra≤1.6',
                        qualityRequirements: '边缘光滑，无划痕',
                        isOutsourced: false,
                        supplier: ''
                    }
                ],
                
                connections: [
                    {
                        id: 1,
                        fromNode: 1,
                        toNode: 2,
                        x1: 232, y1: 140,
                        x2: 232, y2: 250
                    }
                ],
                
                processTemplates: {
                    cutting: [
                        { id: 'cut1', name: '直线切割', description: '标准直线切割工序' },
                        { id: 'cut2', name: '异形切割', description: '异形玻璃切割工序' }
                    ],
                    grinding: [
                        { id: 'grind1', name: '直边磨边', description: '直边磨边工序' },
                        { id: 'grind2', name: '圆边磨边', description: '圆边磨边工序' }
                    ],
                    tempering: [
                        { id: 'temp1', name: '钢化处理', description: '玻璃钢化工序' },
                        { id: 'temp2', name: '半钢化', description: '半钢化处理工序' }
                    ]
                },
                
                workCenters: [
                    { id: 1, name: '切割工作中心' },
                    { id: 2, name: '磨边工作中心' },
                    { id: 3, name: '钢化工作中心' },
                    { id: 4, name: '合片工作中心' }
                ],
                
                init() {
                    // 初始化
                },
                
                startDrag(event, template) {
                    this.draggedTemplate = template;
                    event.dataTransfer.effectAllowed = 'copy';
                },
                
                handleDrop(event) {
                    event.preventDefault();
                    if (this.draggedTemplate) {
                        const rect = event.currentTarget.getBoundingClientRect();
                        const x = event.clientX - rect.left;
                        const y = event.clientY - rect.top;
                        
                        this.addNode(this.draggedTemplate, x, y);
                        this.draggedTemplate = null;
                    }
                },
                
                addNode(template, x, y) {
                    const newNode = {
                        id: Date.now(),
                        name: template.name,
                        type: template.type || 'process',
                        operationNumber: String((this.processNodes.length + 1) * 10).padStart(3, '0'),
                        workCenter: '',
                        x: x - 64, // 居中
                        y: y - 32,
                        setupTime: 0,
                        unitTime: 0,
                        teardownTime: 0,
                        parameters: '',
                        qualityRequirements: '',
                        isOutsourced: false,
                        supplier: ''
                    };
                    
                    this.processNodes.push(newNode);
                },
                
                selectNode(node) {
                    this.selectedNode = node;
                },
                
                deselectAll() {
                    this.selectedNode = null;
                },
                
                startDragNode(event, node) {
                    this.draggedNode = node;
                    const rect = event.currentTarget.getBoundingClientRect();
                    this.dragOffset = {
                        x: event.clientX - rect.left,
                        y: event.clientY - rect.top
                    };
                    
                    document.addEventListener('mousemove', this.dragNode);
                    document.addEventListener('mouseup', this.stopDragNode);
                },
                
                dragNode(event) {
                    if (this.draggedNode) {
                        const canvas = document.querySelector('.canvas-grid');
                        const rect = canvas.getBoundingClientRect();
                        this.draggedNode.x = (event.clientX - rect.left - this.dragOffset.x) / this.zoomLevel;
                        this.draggedNode.y = (event.clientY - rect.top - this.dragOffset.y) / this.zoomLevel;
                        this.updateConnections();
                    }
                },

                stopDragNode() {
                    this.draggedNode = null;
                    document.removeEventListener('mousemove', this.dragNode);
                    document.removeEventListener('mouseup', this.stopDragNode);
                },

                // 连接节点功能
                startConnection(fromNode) {
                    this.connectionMode = true;
                    this.connectionStart = fromNode;
                },

                completeConnection(toNode) {
                    if (this.connectionMode && this.connectionStart && this.connectionStart.id !== toNode.id) {
                        // 检查是否已存在连接
                        const existingConnection = this.connections.find(c =>
                            c.fromNode === this.connectionStart.id && c.toNode === toNode.id
                        );

                        if (!existingConnection) {
                            const newConnection = {
                                id: Date.now(),
                                fromNode: this.connectionStart.id,
                                toNode: toNode.id,
                                x1: this.connectionStart.x + 64,
                                y1: this.connectionStart.y + 64,
                                x2: toNode.x + 64,
                                y2: toNode.y + 32
                            };
                            this.connections.push(newConnection);
                        }
                    }
                    this.connectionMode = false;
                    this.connectionStart = null;
                },

                deleteConnection(connection) {
                    const index = this.connections.findIndex(c => c.id === connection.id);
                    if (index > -1) {
                        this.connections.splice(index, 1);
                    }
                },
                
                updateConnections() {
                    // 更新连接线位置
                    this.connections.forEach(conn => {
                        const fromNode = this.processNodes.find(n => n.id === conn.fromNode);
                        const toNode = this.processNodes.find(n => n.id === conn.toNode);
                        
                        if (fromNode && toNode) {
                            conn.x1 = fromNode.x + 64; // 节点中心
                            conn.y1 = fromNode.y + 64;
                            conn.x2 = toNode.x + 64;
                            conn.y2 = toNode.y + 32;
                        }
                    });
                },
                
                getNodeBorderClass(type) {
                    const classMap = {
                        'cutting': 'border-blue-300',
                        'grinding': 'border-green-300',
                        'tempering': 'border-orange-300',
                        'start': 'border-gray-300',
                        'end': 'border-gray-300',
                        'parallel': 'border-purple-300',
                        'decision': 'border-yellow-300'
                    };
                    return classMap[type] || 'border-gray-300';
                },
                
                duplicateNode() {
                    if (this.selectedNode) {
                        const newNode = {
                            ...this.selectedNode,
                            id: Date.now(),
                            x: this.selectedNode.x + 50,
                            y: this.selectedNode.y + 50,
                            operationNumber: String((this.processNodes.length + 1) * 10).padStart(3, '0')
                        };
                        this.processNodes.push(newNode);
                    }
                },
                
                deleteNode() {
                    if (this.selectedNode) {
                        const index = this.processNodes.findIndex(n => n.id === this.selectedNode.id);
                        if (index > -1) {
                            this.processNodes.splice(index, 1);
                            // 删除相关连接
                            this.connections = this.connections.filter(c => 
                                c.fromNode !== this.selectedNode.id && c.toNode !== this.selectedNode.id
                            );
                            this.selectedNode = null;
                        }
                    }
                },
                
                zoomIn() {
                    this.zoomLevel = Math.min(this.zoomLevel + 0.1, 2);
                },
                
                zoomOut() {
                    this.zoomLevel = Math.max(this.zoomLevel - 0.1, 0.5);
                },
                
                clearCanvas() {
                    if (confirm('确定要清空画布吗？此操作不可撤销。')) {
                        this.processNodes = [];
                        this.connections = [];
                        this.selectedNode = null;
                    }
                },
                
                autoLayout() {
                    // 简单的自动布局算法
                    this.processNodes.forEach((node, index) => {
                        node.x = 100 + (index % 3) * 200;
                        node.y = 100 + Math.floor(index / 3) * 150;
                    });
                    this.updateConnections();
                },
                
                validateRoute() {
                    const errors = [];
                    
                    // 检查是否有开始和结束节点
                    const hasStart = this.processNodes.some(n => n.type === 'start');
                    const hasEnd = this.processNodes.some(n => n.type === 'end');
                    
                    if (!hasStart) errors.push('缺少开始节点');
                    if (!hasEnd) errors.push('缺少结束节点');
                    
                    // 检查工序号是否重复
                    const operationNumbers = this.processNodes.map(n => n.operationNumber);
                    const duplicates = operationNumbers.filter((num, index) => operationNumbers.indexOf(num) !== index);
                    if (duplicates.length > 0) {
                        errors.push(`工序号重复: ${duplicates.join(', ')}`);
                    }
                    
                    if (errors.length > 0) {
                        alert('验证失败:\n' + errors.join('\n'));
                    } else {
                        alert('工艺路线验证通过！');
                    }
                },
                
                previewRoute() {
                    alert('预览功能开发中...');
                },
                
                saveRoute() {
                    const routeData = {
                        nodes: this.processNodes,
                        connections: this.connections,
                        metadata: {
                            name: '工艺路线_' + new Date().toISOString().slice(0, 10),
                            version: '1.0',
                            createTime: new Date().toISOString(),
                            creator: '当前用户'
                        }
                    };

                    // 这里应该调用API保存到后端
                    console.log('保存工艺路线数据:', routeData);
                    alert('工艺路线保存成功！');
                },

                exportRoute() {
                    const routeData = {
                        nodes: this.processNodes,
                        connections: this.connections,
                        metadata: {
                            name: '工艺路线_' + new Date().toISOString().slice(0, 10),
                            version: '1.0',
                            exportTime: new Date().toISOString()
                        }
                    };

                    const dataStr = JSON.stringify(routeData, null, 2);
                    const dataBlob = new Blob([dataStr], {type: 'application/json'});
                    const url = URL.createObjectURL(dataBlob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `工艺路线_${new Date().toISOString().slice(0, 10)}.json`;
                    link.click();
                    URL.revokeObjectURL(url);
                },

                importRoute() {
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = '.json';
                    input.onchange = (event) => {
                        const file = event.target.files[0];
                        if (file) {
                            const reader = new FileReader();
                            reader.onload = (e) => {
                                try {
                                    const routeData = JSON.parse(e.target.result);
                                    if (routeData.nodes && routeData.connections) {
                                        this.processNodes = routeData.nodes;
                                        this.connections = routeData.connections;
                                        this.selectedNode = null;
                                        alert('工艺路线导入成功！');
                                    } else {
                                        alert('文件格式不正确！');
                                    }
                                } catch (error) {
                                    alert('文件解析失败：' + error.message);
                                }
                            };
                            reader.readAsText(file);
                        }
                    };
                    input.click();
                }
            }
        }
    </script>
</body>
</html>
