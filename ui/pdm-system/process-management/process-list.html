<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工序管理 - 玻璃深加工ERP系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1890FF',
                        success: '#52C41A',
                        warning: '#FAAD14',
                        error: '#F5222D',
                        'title-gray': '#262626',
                        'body-gray': '#595959',
                        'aux-gray': '#8C8C8C',
                        'disabled-gray': '#BFBFBF',
                        'border-gray': '#D9D9D9',
                        'bg-gray': '#FAFAFA'
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', 'sans-serif']
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-bg-gray font-chinese" x-data="processManagementApp()">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-border-gray h-16">
        <div class="flex items-center justify-between px-6 h-full">
            <div class="flex items-center space-x-4">
                <h1 class="text-xl font-medium text-title-gray">工序管理</h1>
                <span class="text-aux-gray text-sm">/ 工艺管理子系统 / 工序管理</span>
            </div>
            <div class="flex items-center space-x-3">
                <button @click="exportProcesses" 
                        class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    导出
                </button>
                <button @click="importProcesses" 
                        class="px-4 py-2 bg-warning text-white rounded-md hover:bg-yellow-600 transition-colors">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"/>
                    </svg>
                    导入
                </button>
                <button @click="showCreateModal = true" 
                        class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600 transition-colors">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    新建工序
                </button>
            </div>
        </div>
    </header>

    <!-- 搜索和筛选栏 -->
    <div class="bg-white border-b border-border-gray px-6 py-4">
        <div class="flex items-center space-x-4">
            <div class="flex-1 max-w-md">
                <div class="relative">
                    <input type="text" x-model="searchQuery" @input="filterProcesses"
                           placeholder="搜索工序编码、名称..."
                           class="w-full pl-8 pr-4 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                    <svg class="absolute left-2 top-2.5 h-4 w-4 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                    </svg>
                </div>
            </div>
            <select x-model="typeFilter" @change="filterProcesses"
                    class="px-3 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                <option value="">全部类型</option>
                <option value="加工工序">加工工序</option>
                <option value="检验工序">检验工序</option>
                <option value="包装工序">包装工序</option>
                <option value="运输工序">运输工序</option>
            </select>
            <select x-model="statusFilter" @change="filterProcesses"
                    class="px-3 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                <option value="">全部状态</option>
                <option value="草稿">草稿</option>
                <option value="生效">生效</option>
                <option value="停用">停用</option>
            </select>
            <button @click="resetFilters" class="px-3 py-2 text-sm text-aux-gray border border-border-gray rounded-md hover:bg-gray-50">
                重置
            </button>
        </div>
    </div>

    <!-- 主内容区域 -->
    <main class="flex h-screen pt-1">
        <!-- 左侧分类树区域 -->
        <div class="w-80 bg-white border-r border-border-gray">
            <div class="p-4 border-b border-border-gray">
                <h3 class="text-sm font-medium text-title-gray">工序分类</h3>
            </div>
            <div class="flex-1 overflow-auto p-4">
                <div class="space-y-1">
                    <template x-for="category in processCategories" :key="category.id">
                        <div>
                            <div class="flex items-center p-2 rounded cursor-pointer hover:bg-gray-50"
                                 :class="selectedCategory?.id === category.id ? 'bg-blue-50 border-l-2 border-primary' : ''"
                                 @click="selectCategory(category)">
                                <template x-if="category.children && category.children.length > 0">
                                    <button @click.stop="toggleCategory(category)" class="mr-2">
                                        <svg class="w-4 h-4 text-aux-gray transition-transform" 
                                             :class="category.expanded ? 'transform rotate-90' : ''"
                                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                        </svg>
                                    </button>
                                </template>
                                <template x-if="!category.children || category.children.length === 0">
                                    <div class="w-6 h-4 mr-2"></div>
                                </template>
                                <svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                </svg>
                                <span class="text-sm text-body-gray" x-text="category.name"></span>
                                <span class="text-xs text-aux-gray ml-auto" x-text="`(${category.count})`"></span>
                            </div>
                            
                            <!-- 子分类 -->
                            <template x-if="category.expanded && category.children">
                                <div class="ml-6 mt-1 space-y-1">
                                    <template x-for="child in category.children" :key="child.id">
                                        <div class="flex items-center p-2 rounded cursor-pointer hover:bg-gray-50"
                                             :class="selectedCategory?.id === child.id ? 'bg-blue-50 border-l-2 border-primary' : ''"
                                             @click="selectCategory(child)">
                                            <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                                            </svg>
                                            <span class="text-sm text-body-gray" x-text="child.name"></span>
                                            <span class="text-xs text-aux-gray ml-auto" x-text="`(${child.count})`"></span>
                                        </div>
                                    </template>
                                </div>
                            </template>
                        </div>
                    </template>
                </div>
            </div>
        </div>

        <!-- 中间工序列表区域 -->
        <div class="flex-1 bg-white border-r border-border-gray">
            <div class="p-4 border-b border-border-gray">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium text-title-gray">工序列表</h3>
                        <p class="text-xs text-aux-gray mt-1" x-text="`共 ${filteredProcesses.length} 个工序`"></p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button @click="viewMode = 'list'" 
                                :class="viewMode === 'list' ? 'bg-primary text-white' : 'bg-gray-100 text-body-gray'"
                                class="px-3 py-1 text-sm rounded-md">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"/>
                            </svg>
                        </button>
                        <button @click="viewMode = 'grid'" 
                                :class="viewMode === 'grid' ? 'bg-primary text-white' : 'bg-gray-100 text-body-gray'"
                                class="px-3 py-1 text-sm rounded-md">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="flex-1 overflow-auto p-4">
                <!-- 列表视图 -->
                <template x-if="viewMode === 'list'">
                    <div class="space-y-2">
                        <template x-for="process in filteredProcesses" :key="process.id">
                            <div class="p-4 border border-border-gray rounded-md hover:border-primary cursor-pointer transition-colors"
                                 :class="selectedProcess?.id === process.id ? 'border-primary bg-blue-50' : ''"
                                 @click="selectProcess(process)">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-3 mb-2">
                                            <h4 class="text-sm font-medium text-title-gray" x-text="process.name"></h4>
                                            <span class="px-2 py-1 text-xs rounded-full"
                                                  :class="getStatusClass(process.status)"
                                                  x-text="process.status"></span>
                                            <span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full" x-text="process.type"></span>
                                        </div>
                                        <div class="text-xs text-aux-gray mb-1" x-text="`编码: ${process.code}`"></div>
                                        <div class="text-sm text-body-gray" x-text="process.description"></div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm text-body-gray mb-1">标准工时</div>
                                        <div class="text-lg font-medium text-title-gray" x-text="`${process.standardTime}分钟`"></div>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between mt-3 pt-3 border-t border-border-gray">
                                    <div class="flex items-center space-x-4 text-xs text-aux-gray">
                                        <span x-text="`设备: ${process.equipment || '无'}`"></span>
                                        <span x-text="`技能等级: ${process.skillLevel}`"></span>
                                        <span x-text="`更新: ${process.updateTime}`"></span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button @click.stop="editProcess(process)" class="text-primary hover:text-blue-600">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                            </svg>
                                        </button>
                                        <button @click.stop="copyProcess(process)" class="text-success hover:text-green-600">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                            </svg>
                                        </button>
                                        <button @click.stop="deleteProcess(process)" class="text-error hover:text-red-600">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </template>

                <!-- 网格视图 -->
                <template x-if="viewMode === 'grid'">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <template x-for="process in filteredProcesses" :key="process.id">
                            <div class="p-4 border border-border-gray rounded-md hover:border-primary cursor-pointer transition-colors"
                                 :class="selectedProcess?.id === process.id ? 'border-primary bg-blue-50' : ''"
                                 @click="selectProcess(process)">
                                <div class="flex items-center justify-between mb-3">
                                    <h4 class="text-sm font-medium text-title-gray" x-text="process.name"></h4>
                                    <span class="px-2 py-1 text-xs rounded-full"
                                          :class="getStatusClass(process.status)"
                                          x-text="process.status"></span>
                                </div>
                                <div class="text-xs text-aux-gray mb-2" x-text="`编码: ${process.code}`"></div>
                                <div class="text-sm text-body-gray mb-3" x-text="process.description"></div>
                                <div class="flex items-center justify-between text-sm">
                                    <span class="text-aux-gray">标准工时:</span>
                                    <span class="font-medium text-title-gray" x-text="`${process.standardTime}分钟`"></span>
                                </div>
                                <div class="flex items-center justify-between mt-2 text-xs text-aux-gray">
                                    <span x-text="process.type"></span>
                                    <span x-text="process.skillLevel"></span>
                                </div>
                            </div>
                        </template>
                    </div>
                </template>

                <!-- 空状态 -->
                <template x-if="filteredProcesses.length === 0">
                    <div class="text-center py-12 text-aux-gray">
                        <svg class="w-12 h-12 mx-auto mb-4 text-disabled-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                        </svg>
                        <p class="text-sm">暂无工序数据</p>
                        <p class="text-xs mt-1">请调整筛选条件或创建新工序</p>
                    </div>
                </template>
            </div>
        </div>

        <!-- 右侧详情面板 -->
        <div class="w-96 bg-white" x-show="selectedProcess">
            <div class="p-4 border-b border-border-gray">
                <div class="flex items-center justify-between">
                    <h3 class="text-sm font-medium text-title-gray">工序详情</h3>
                    <button @click="selectedProcess = null" class="text-aux-gray hover:text-title-gray">
                        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
            </div>
            
            <div class="flex-1 overflow-auto p-4" x-show="selectedProcess">
                <template x-if="selectedProcess">
                    <div class="space-y-6">
                        <!-- 基本信息 -->
                        <div>
                            <h4 class="text-sm font-medium text-title-gray mb-3">基本信息</h4>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">工序名称</label>
                                    <div class="text-sm text-body-gray" x-text="selectedProcess.name"></div>
                                </div>
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">工序编码</label>
                                    <div class="text-sm text-body-gray" x-text="selectedProcess.code"></div>
                                </div>
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">工序类型</label>
                                    <div class="text-sm text-body-gray" x-text="selectedProcess.type"></div>
                                </div>
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">状态</label>
                                    <span class="px-2 py-1 text-xs rounded-full"
                                          :class="getStatusClass(selectedProcess.status)"
                                          x-text="selectedProcess.status"></span>
                                </div>
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">描述</label>
                                    <div class="text-sm text-body-gray" x-text="selectedProcess.description"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 工艺参数 -->
                        <div>
                            <h4 class="text-sm font-medium text-title-gray mb-3">工艺参数</h4>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">标准工时</label>
                                    <div class="text-sm text-body-gray" x-text="`${selectedProcess.standardTime} 分钟`"></div>
                                </div>
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">技能等级要求</label>
                                    <div class="text-sm text-body-gray" x-text="selectedProcess.skillLevel"></div>
                                </div>
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">设备要求</label>
                                    <div class="text-sm text-body-gray" x-text="selectedProcess.equipment || '无特殊要求'"></div>
                                </div>
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">质量标准</label>
                                    <div class="text-sm text-body-gray" x-text="selectedProcess.qualityStandard || '按标准执行'"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 成本信息 -->
                        <div>
                            <h4 class="text-sm font-medium text-title-gray mb-3">成本信息</h4>
                            <div class="space-y-2">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-body-gray">人工成本</span>
                                    <span class="text-sm font-medium text-title-gray" x-text="`¥${selectedProcess.laborCost || 0}/小时`"></span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-body-gray">设备成本</span>
                                    <span class="text-sm text-body-gray" x-text="`¥${selectedProcess.equipmentCost || 0}/小时`"></span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-body-gray">其他成本</span>
                                    <span class="text-sm text-body-gray" x-text="`¥${selectedProcess.otherCost || 0}/小时`"></span>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="space-y-2">
                            <button @click="editProcess(selectedProcess)" 
                                    class="w-full px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600">
                                编辑工序
                            </button>
                            <button @click="copyProcess(selectedProcess)" 
                                    class="w-full px-4 py-2 bg-success text-white rounded-md hover:bg-green-600">
                                复制工序
                            </button>
                            <button @click="viewHistory(selectedProcess)" 
                                    class="w-full px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
                                查看历史
                            </button>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </main>

    <script>
        function processManagementApp() {
            return {
                // UI状态
                viewMode: 'list',
                showCreateModal: false,
                selectedCategory: null,
                selectedProcess: null,
                searchQuery: '',
                typeFilter: '',
                statusFilter: '',

                // 工序分类
                processCategories: [
                    {
                        id: 'CAT001',
                        name: '玻璃加工工序',
                        count: 15,
                        expanded: true,
                        children: [
                            { id: 'CAT001-1', name: '切割工序', count: 5 },
                            { id: 'CAT001-2', name: '磨边工序', count: 4 },
                            { id: 'CAT001-3', name: '钢化工序', count: 3 },
                            { id: 'CAT001-4', name: '夹胶工序', count: 3 }
                        ]
                    },
                    {
                        id: 'CAT002',
                        name: '质量检验工序',
                        count: 8,
                        expanded: false,
                        children: [
                            { id: 'CAT002-1', name: '外观检验', count: 3 },
                            { id: 'CAT002-2', name: '尺寸检验', count: 2 },
                            { id: 'CAT002-3', name: '性能检验', count: 3 }
                        ]
                    },
                    {
                        id: 'CAT003',
                        name: '包装运输工序',
                        count: 6,
                        expanded: false,
                        children: [
                            { id: 'CAT003-1', name: '包装工序', count: 3 },
                            { id: 'CAT003-2', name: '装载工序', count: 2 },
                            { id: 'CAT003-3', name: '运输工序', count: 1 }
                        ]
                    }
                ],

                // 工序数据
                processes: [
                    {
                        id: 'PROC001',
                        code: 'OP-CUT-001',
                        name: '玻璃切割',
                        type: '加工工序',
                        status: '生效',
                        description: '使用切割机对玻璃原片进行精确切割',
                        standardTime: 15,
                        skillLevel: '中级',
                        equipment: '全自动切割机',
                        qualityStandard: '尺寸偏差±1mm',
                        laborCost: 25,
                        equipmentCost: 15,
                        otherCost: 5,
                        updateTime: '2025-07-30',
                        categoryId: 'CAT001-1'
                    },
                    {
                        id: 'PROC002',
                        code: 'OP-EDGE-001',
                        name: '直边磨边',
                        type: '加工工序',
                        status: '生效',
                        description: '对玻璃边缘进行直边磨边处理',
                        standardTime: 20,
                        skillLevel: '中级',
                        equipment: '直边磨边机',
                        qualityStandard: '边缘光滑无崩边',
                        laborCost: 30,
                        equipmentCost: 20,
                        otherCost: 3,
                        updateTime: '2025-07-29',
                        categoryId: 'CAT001-2'
                    },
                    {
                        id: 'PROC003',
                        code: 'OP-TEMP-001',
                        name: '钢化处理',
                        type: '加工工序',
                        status: '生效',
                        description: '通过高温加热和急速冷却实现玻璃钢化',
                        standardTime: 45,
                        skillLevel: '高级',
                        equipment: '钢化炉',
                        qualityStandard: '应力值≥90MPa',
                        laborCost: 40,
                        equipmentCost: 60,
                        otherCost: 10,
                        updateTime: '2025-07-28',
                        categoryId: 'CAT001-3'
                    },
                    {
                        id: 'PROC004',
                        code: 'OP-LAM-001',
                        name: 'PVB夹胶',
                        type: '加工工序',
                        status: '生效',
                        description: '使用PVB胶片进行玻璃夹胶处理',
                        standardTime: 35,
                        skillLevel: '高级',
                        equipment: '夹胶设备',
                        qualityStandard: '无气泡、无脱胶',
                        laborCost: 35,
                        equipmentCost: 45,
                        otherCost: 8,
                        updateTime: '2025-07-27',
                        categoryId: 'CAT001-4'
                    },
                    {
                        id: 'PROC005',
                        code: 'OP-QC-001',
                        name: '外观检验',
                        type: '检验工序',
                        status: '生效',
                        description: '检查玻璃表面质量和外观缺陷',
                        standardTime: 10,
                        skillLevel: '中级',
                        equipment: '检验台',
                        qualityStandard: '按GB15763标准执行',
                        laborCost: 20,
                        equipmentCost: 5,
                        otherCost: 2,
                        updateTime: '2025-07-26',
                        categoryId: 'CAT002-1'
                    },
                    {
                        id: 'PROC006',
                        code: 'OP-PACK-001',
                        name: '产品包装',
                        type: '包装工序',
                        status: '生效',
                        description: '对成品玻璃进行保护性包装',
                        standardTime: 12,
                        skillLevel: '初级',
                        equipment: '包装台',
                        qualityStandard: '包装完整无损',
                        laborCost: 15,
                        equipmentCost: 3,
                        otherCost: 5,
                        updateTime: '2025-07-25',
                        categoryId: 'CAT003-1'
                    }
                ],

                filteredProcesses: [],

                init() {
                    this.filteredProcesses = [...this.processes];
                },

                // 分类操作
                toggleCategory(category) {
                    category.expanded = !category.expanded;
                },

                selectCategory(category) {
                    this.selectedCategory = category;
                    this.filterProcesses();
                },

                // 筛选方法
                filterProcesses() {
                    let filtered = [...this.processes];

                    // 按分类筛选
                    if (this.selectedCategory) {
                        filtered = filtered.filter(process =>
                            process.categoryId === this.selectedCategory.id ||
                            (this.selectedCategory.children &&
                             this.selectedCategory.children.some(child => child.id === process.categoryId))
                        );
                    }

                    // 按搜索关键词筛选
                    if (this.searchQuery.trim()) {
                        const query = this.searchQuery.toLowerCase();
                        filtered = filtered.filter(process =>
                            process.code.toLowerCase().includes(query) ||
                            process.name.toLowerCase().includes(query) ||
                            process.description.toLowerCase().includes(query)
                        );
                    }

                    // 按类型筛选
                    if (this.typeFilter) {
                        filtered = filtered.filter(process => process.type === this.typeFilter);
                    }

                    // 按状态筛选
                    if (this.statusFilter) {
                        filtered = filtered.filter(process => process.status === this.statusFilter);
                    }

                    this.filteredProcesses = filtered;
                },

                resetFilters() {
                    this.searchQuery = '';
                    this.typeFilter = '';
                    this.statusFilter = '';
                    this.selectedCategory = null;
                    this.filterProcesses();
                },

                // 工序操作
                selectProcess(process) {
                    this.selectedProcess = process;
                },

                editProcess(process) {
                    alert(`编辑工序: ${process.name}`);
                    // 这里应该打开编辑模态框
                },

                copyProcess(process) {
                    if (confirm(`确定复制工序 ${process.name} 吗？`)) {
                        const newProcess = {
                            ...process,
                            id: 'PROC' + Date.now(),
                            code: process.code + '-COPY',
                            name: process.name + ' (副本)',
                            status: '草稿',
                            updateTime: new Date().toISOString().split('T')[0]
                        };

                        this.processes.unshift(newProcess);
                        this.filterProcesses();
                        alert('工序复制成功');
                    }
                },

                deleteProcess(process) {
                    if (confirm(`确定删除工序 ${process.name} 吗？此操作不可撤销。`)) {
                        const index = this.processes.findIndex(p => p.id === process.id);
                        if (index > -1) {
                            this.processes.splice(index, 1);
                            this.filterProcesses();

                            if (this.selectedProcess?.id === process.id) {
                                this.selectedProcess = null;
                            }

                            alert('工序删除成功');
                        }
                    }
                },

                viewHistory(process) {
                    alert(`查看工序 ${process.name} 的变更历史`);
                },

                // 批量操作
                exportProcesses() {
                    alert('导出工序数据功能');
                },

                importProcesses() {
                    alert('导入工序数据功能');
                },

                // 样式方法
                getStatusClass(status) {
                    const classMap = {
                        '草稿': 'bg-gray-100 text-gray-800',
                        '生效': 'bg-green-100 text-green-800',
                        '停用': 'bg-red-100 text-red-800'
                    };
                    return classMap[status] || 'bg-gray-100 text-gray-800';
                }
            }
        }
    </script>
</body>
</html>
